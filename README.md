# 鞋子 SVG 生成器插件

这个 WordPress 插件用于生成鞋子 SVG 图像并创建相应的 WooCommerce 产品。

## 安装要求

### 必需的软件

1. **rsvg-convert**：用于 SVG 到 PNG 的转换
   - Windows 安装: `choco install rsvg-convert`
   - Linux 安装: `apt-get install librsvg2-bin`
   - macOS 安装: `brew install librsvg`

2. **ImageMagick**：用于图像处理
   - Windows 安装: `choco install imagemagick`
   - Linux 安装: `apt-get install imagemagick`
   - macOS 安装: `brew install imagemagick`

3. **Python 3**：用于 SVG 处理脚本
   - Windows 安装: [Python.org 下载页面](https://www.python.org/downloads/windows/)
   - Linux 安装: `apt-get install python3 python3-pip`
   - macOS 安装: `brew install python`

4. **Python 依赖**：
   ```
   pip install svgutils lxml pillow
   ```

### 可选软件

1. **Inkscape**：作为 SVG 处理的备用选项
   - Windows 安装: `choco install inkscape`
   - Linux 安装: `apt-get install inkscape`
   - macOS 安装: `brew install inkscape`

## 插件功能

- SVG 文件上传和管理
- 自动颜色提取和处理
- 批量产品生成
- 前端 SVG 预览
- 并行处理任务

## 配置

1. 在 WordPress 管理面板中激活插件
2. 访问"SVG 生成器 > 设置"配置插件参数
3. 上传 SVG 文件到"SVG 库"
4. 创建产品模板并设置 SVG 填充区域
5. 使用"产品生成"功能批量创建产品

## 故障排除

如果遇到 SVG 转换问题，请确保：

1. rsvg-convert 已正确安装并可在命令行中访问
2. 服务器有足够的内存处理大型 SVG 文件
3. 文件权限正确设置，允许插件写入上传目录

## 性能优化

- 调整"设置"中的最大并行任务数以适应服务器性能
- 对于大量 SVG 文件，建议使用 WP-CLI 命令进行批处理
- 定期清理临时文件目录以释放磁盘空间

## 支持

如有问题或需要帮助，请联系插件开发者。
