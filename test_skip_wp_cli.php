<?php
/**
 * WordPress CLI环境下的PNG跳过功能测试
 * 
 * 使用WP-CLI测试PNG跳过逻辑
 */

// 确保在WP-CLI环境中运行
if (!defined('WP_CLI') && !defined('ABSPATH')) {
    echo "请在WordPress环境中运行此脚本\n";
    exit(1);
}

if (defined('WP_CLI') && WP_CLI) {
    class PNG_Skip_Test_CLI {
        
        public function test_skip() {
            $template_id = 324761; // 测试模板ID
            
            WP_CLI::log('🔍 测试PNG跳过功能...');
            
            // 初始化管理器
            $png_manager = SS_PNG_File_Manager::get_instance();
            
            // 扫描目录结构
            $directory_structure = $png_manager->scan_png_directory_structure();
            
            if (empty($directory_structure)) {
                WP_CLI::log('❌ 未找到PNG文件夹');
                return;
            }
            
            WP_CLI::log('📁 发现文件夹: ' . count($directory_structure));
            
            $processed_folders = 0;
            $folders_to_process = 0;
            
            foreach ($directory_structure as $folder_path => $folder_info) {
                WP_CLI::log("\n📂 检查文件夹: $folder_path");
                
                // 获取类目ID
                $category_id = $png_manager->get_or_create_category($folder_path);
                if (!$category_id) {
                    WP_CLI::log('  ❌ 无法创建类目');
                    continue;
                }
                
                // 检查JSON记录是否存在
                $json_record = $png_manager->get_folder_json_record($folder_path, $category_id);
                if ($json_record) {
                    WP_CLI::log('  ✅ JSON记录存在');
                    $total_files = count($json_record['files']);
                    $processed_files = 0;
                    
                    foreach ($json_record['files'] as $file_info) {
                        if (isset($file_info['templates'][$template_id])) {
                            $processed_files++;
                        }
                    }
                    
                    if ($processed_files == $total_files && $total_files > 0) {
                        WP_CLI::log("  ⏭️  已处理完成: {$processed_files}/{$total_files} 文件");
                        $processed_folders++;
                    } else {
                        WP_CLI::log("  📋 需要处理: {$processed_files}/{$total_files} 文件已处理");
                        $folders_to_process++;
                    }
                } else {
                    WP_CLI::log('  📋 无JSON记录，需要处理');
                    $folders_to_process++;
                }
            }
            
            WP_CLI::log("\n📊 汇总:");
            WP_CLI::log("  已处理完成文件夹: $processed_folders");
            WP_CLI::log("  需要处理文件夹: $folders_to_process");
            
            if ($processed_folders > 0) {
                WP_CLI::success('✅ 跳过功能工作正常！');
            } else {
                WP_CLI::log('ℹ️  所有文件夹都需要处理（首次运行或JSON记录不存在）');
            }
        }
    }
    
    WP_CLI::add_command('ss test_skip', array('PNG_Skip_Test_CLI', 'test_skip'));
}