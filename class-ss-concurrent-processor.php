<?php
/**
 * 并发处理管理器
 * 
 * 实现多线程/多进程 SVG 预览图生成，充分利用服务器 CPU 资源
 * 
 * @package ShoeSVGGenerator
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class SS_Concurrent_Processor {
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 最大并发工作进程数
     */
    private $max_workers;
    
    /**
     * 批量处理大小
     */
    private $batch_size;
    
    /**
     * 活动进程列表
     */
    private $active_processes = [];
    
    /**
     * 性能监控器
     */
    private $performance_monitor;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_settings();
        $this->performance_monitor = new SS_Performance_Monitor();
        
        // 添加调试日志
        error_log('[并发处理器] 初始化完成，最大工作进程数: ' . $this->max_workers . ', 批量大小: ' . $this->batch_size);
    }
    
    /**
     * 【优化】初始化设置 - 充分利用多核性能
     */
    private function init_settings() {
        // 检测 CPU 核心数
        $cpu_cores = $this->detect_cpu_cores();

        // 【优化】更激进的默认配置，充分利用多核性能
        $default_workers = min($cpu_cores * 2, 16); // 默认为CPU核心数的2倍，最大16
        $default_batch_size = min($cpu_cores * 4, 32); // 默认为CPU核心数的4倍，最大32

        // 从设置中获取配置，如果没有则使用优化后的默认值
        $this->max_workers = get_option('ss_max_concurrent_workers', $default_workers);
        $this->batch_size = get_option('ss_batch_size', $default_batch_size);

        // 确保最小值
        $this->max_workers = max(2, $this->max_workers); // 最小2个工作进程
        $this->batch_size = max(4, $this->batch_size);   // 最小4个文件一批

        // 【新增】记录优化信息
        error_log(sprintf('[并发优化] CPU核心数: %d, 工作进程数: %d, 批次大小: %d',
            $cpu_cores, $this->max_workers, $this->batch_size));
    }
    
    /**
     * 【优化】检测 CPU 核心数 - 支持更多核心
     */
    private function detect_cpu_cores() {
        $cores = 1; // 默认值

        if (function_exists('shell_exec')) {
            // Linux/Unix 系统 - 优先使用nproc
            $output = shell_exec('nproc 2>/dev/null');
            if ($output) {
                $cores = intval(trim($output));
            } else {
                // 备选方法1：从/proc/cpuinfo读取
                $output = shell_exec('grep -c ^processor /proc/cpuinfo 2>/dev/null');
                if ($output) {
                    $cores = intval(trim($output));
                } else {
                    // 备选方法2：从/proc/stat读取
                    $output = shell_exec('grep -c ^cpu /proc/stat 2>/dev/null');
                    if ($output) {
                        $cores = intval(trim($output)) - 1; // 减去cpu总行
                    }
                }
            }
        }

        // 【优化】支持更多核心，移除16核心限制
        $cores = max(1, $cores);

        // 【调试】记录检测结果
        error_log('[CPU检测] 检测到CPU核心数: ' . $cores);

        return $cores;
    }
    
    /**
     * 批量处理 SVG 文件
     *
     * @param array $svg_files SVG 文件列表
     * @param array $category_data 类目数据
     * @param int $custom_batch_size 自定义批次大小，为null时使用默认值
     * @return array 处理结果
     */
    public function process_svg_batch($svg_files, $category_data, $custom_batch_size = null) {
        if (empty($svg_files)) {
            return [];
        }

        // 【新增】使用自定义批次大小（如果提供）
        $original_batch_size = $this->batch_size;
        if ($custom_batch_size !== null) {
            $this->batch_size = max(4, $custom_batch_size); // 确保最小值为4
            error_log('[并发处理器] 使用自定义批次大小: ' . $this->batch_size);
        }

        $start_time = microtime(true);
        error_log('[并发处理器] 开始批量处理 ' . count($svg_files) . ' 个 SVG 文件，批次大小: ' . $this->batch_size);
        
        // 【修复】强制启用并发处理，禁用串行处理以避免冲突
        if (!get_option('ss_enable_concurrent_processing', true)) {
            error_log('[并发处理器] 并发处理已禁用，但为避免路径冲突，强制启用并发处理');
            // 不再回退到串行处理，强制使用并发处理
        }
        
        // 动态调整并发数量
        $current_workers = $this->performance_monitor->adjust_concurrency($this->max_workers);
        
        try {
            // 检查文件数量，决定处理策略
            $total_files = count($svg_files);

            if ($total_files <= $this->batch_size) {
                // 文件数量较少，直接并发处理
                error_log('[并发处理器] 文件数量较少(' . $total_files . ')，直接并发处理');
                $results = $this->process_batch_concurrent($svg_files, $category_data, $current_workers);
                $this->update_progress($category_data['term_id'], 1, 1, count($results));
            } else {
                // 文件数量较多，使用真正的并发批次处理
                error_log('[并发处理器] 文件数量较多(' . $total_files . ')，使用并发批次处理');
                $results = $this->process_large_batch_concurrent($svg_files, $category_data, $current_workers);
            }
            
            $duration = microtime(true) - $start_time;
            error_log('[并发处理器] 批量处理完成，耗时: ' . round($duration, 2) . ' 秒，成功处理: ' . count($results) . ' 个文件');

            // 【新增】恢复原始批次大小
            if ($custom_batch_size !== null) {
                $this->batch_size = $original_batch_size;
            }

            return $results;

        } catch (Exception $e) {
            error_log('[并发处理器] 并发处理失败: ' . $e->getMessage());

            // 【新增】恢复原始批次大小
            if ($custom_batch_size !== null) {
                $this->batch_size = $original_batch_size;
            }

            // 【修复】不再回退到串行处理，返回空结果避免路径冲突
            return [];
        }
    }
    
    /**
     * 并发处理单个批次
     */
    private function process_batch_concurrent($batch, $category_data, $max_workers) {
        $python_script = WP_PLUGIN_DIR . '/shoe-svg-generator/python/process_svg_batch.py';
        
        // 检查批量处理脚本是否存在
        if (!file_exists($python_script)) {
            error_log('[并发处理器] 批量处理脚本不存在: ' . $python_script);
            return [];
        }
        
        // 【修复】准备批次数据，添加正确的输出目录
        $upload_dir = wp_upload_dir();
        $safe_category_name = sanitize_title($category_data['name']);
        $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $safe_category_name;

        // 确保输出目录存在
        if (!file_exists($output_dir)) {
            wp_mkdir_p($output_dir);
            @chmod($output_dir, 0755);
        }

        $batch_data = [
            'files' => $batch,
            'category_data' => array_merge($category_data, [
                'output_dir' => $output_dir
            ]),
            'max_workers' => min($max_workers, count($batch))
        ];
        
        // 创建临时文件存储批次数据
        $temp_file = tempnam(sys_get_temp_dir(), 'ss_batch_');
        file_put_contents($temp_file, json_encode($batch_data));
        
        try {
            // 构建命令
            $cmd = sprintf('python3 %s %s 2>&1',
                escapeshellarg($python_script),
                escapeshellarg($temp_file)
            );
            
            error_log('[并发处理器] 执行批量处理命令: ' . $cmd);
            
            // 执行命令
            $output = shell_exec($cmd);
            error_log('[并发处理器] 批量处理输出: ' . $output);
            
            // 解析结果
            $results = [];
            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (strpos($line, 'RESULT:') === 0) {
                        $result_data = json_decode(substr($line, 7), true);
                        if ($result_data) {
                            $results[] = $result_data;
                        }
                    }
                }
            }
            
            return $results;
            
        } finally {
            // 清理临时文件
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }
        }
    }

    /**
     * 处理大批量文件的真正并发方法
     */
    private function process_large_batch_concurrent($svg_files, $category_data, $max_workers) {
        // 将文件分成多个批次
        $batches = array_chunk($svg_files, $this->batch_size);
        $total_batches = count($batches);

        error_log('[并发处理器] 分成 ' . $total_batches . ' 个批次，每批次 ' . $this->batch_size . ' 个文件');

        // 使用 PHP 的多进程处理批次
        $all_results = [];
        $active_processes = [];
        $completed_batches = 0;

        // 限制同时运行的进程数
        $max_concurrent_processes = min($max_workers, $total_batches);

        for ($i = 0; $i < $total_batches; $i++) {
            // 等待进程槽位
            while (count($active_processes) >= $max_concurrent_processes) {
                $this->wait_for_process_completion($active_processes, $all_results, $completed_batches, $total_batches, $category_data['term_id']);
            }

            // 启动新的批次处理进程
            $process = $this->start_batch_process($batches[$i], $category_data, $i);
            if ($process) {
                $active_processes[$i] = $process;
                error_log('[并发处理器] 启动批次 ' . ($i + 1) . '/' . $total_batches . ' 处理进程');
            }
        }

        // 等待所有进程完成
        while (!empty($active_processes)) {
            $this->wait_for_process_completion($active_processes, $all_results, $completed_batches, $total_batches, $category_data['term_id']);
        }

        error_log('[并发处理器] 所有批次处理完成，总结果数: ' . count($all_results));
        return $all_results;
    }

    /**
     * 启动批次处理进程
     */
    private function start_batch_process($batch, $category_data, $batch_index) {
        $python_script = WP_PLUGIN_DIR . '/shoe-svg-generator/python/process_svg_batch.py';

        if (!file_exists($python_script)) {
            error_log('[并发处理器] Python 脚本不存在: ' . $python_script);
            return false;
        }

        // 【修复】准备批次数据，添加正确的输出目录
        $upload_dir = wp_upload_dir();
        $safe_category_name = sanitize_title($category_data['name']);
        $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $safe_category_name;

        // 确保输出目录存在
        if (!file_exists($output_dir)) {
            wp_mkdir_p($output_dir);
            @chmod($output_dir, 0755);
        }

        $batch_data = [
            'files' => $batch,
            'category_data' => array_merge($category_data, [
                'output_dir' => $output_dir
            ]),
            'max_workers' => min(4, count($batch)), // 每个批次内部的线程数
            'batch_index' => $batch_index
        ];

        // 创建临时文件
        $temp_file = tempnam(sys_get_temp_dir(), 'ss_batch_');
        file_put_contents($temp_file, json_encode($batch_data));

        // 构建命令
        $cmd = sprintf('python3 %s %s',
            escapeshellarg($python_script),
            escapeshellarg($temp_file)
        );

        // 启动异步进程
        $descriptors = [
            0 => ['pipe', 'r'],  // stdin
            1 => ['pipe', 'w'],  // stdout
            2 => ['pipe', 'w']   // stderr
        ];

        $process = proc_open($cmd, $descriptors, $pipes);

        if (is_resource($process)) {
            // 关闭 stdin
            fclose($pipes[0]);

            // 设置非阻塞模式
            stream_set_blocking($pipes[1], false);
            stream_set_blocking($pipes[2], false);

            return [
                'process' => $process,
                'pipes' => $pipes,
                'temp_file' => $temp_file,
                'batch_index' => $batch_index,
                'start_time' => microtime(true)
            ];
        }

        // 清理临时文件
        if (file_exists($temp_file)) {
            unlink($temp_file);
        }

        return false;
    }

    /**
     * 等待进程完成
     */
    private function wait_for_process_completion(&$active_processes, &$all_results, &$completed_batches, $total_batches, $term_id) {
        foreach ($active_processes as $batch_index => $process_info) {
            $status = proc_get_status($process_info['process']);

            if (!$status['running']) {
                // 进程已完成，读取输出
                $output = stream_get_contents($process_info['pipes'][1]);
                $error = stream_get_contents($process_info['pipes'][2]);

                // 关闭管道和进程
                fclose($process_info['pipes'][1]);
                fclose($process_info['pipes'][2]);
                proc_close($process_info['process']);

                // 清理临时文件
                if (file_exists($process_info['temp_file'])) {
                    unlink($process_info['temp_file']);
                }

                $duration = microtime(true) - $process_info['start_time'];
                error_log('[并发处理器] 批次 ' . ($batch_index + 1) . ' 完成，耗时: ' . round($duration, 2) . ' 秒');

                // 解析结果
                $batch_results = $this->parse_batch_output($output);
                $all_results = array_merge($all_results, $batch_results);

                if (!empty($error)) {
                    error_log('[并发处理器] 批次 ' . ($batch_index + 1) . ' 错误输出: ' . $error);
                }

                // 更新进度
                $completed_batches++;
                $this->update_progress($term_id, $completed_batches, $total_batches, count($all_results));

                // 移除已完成的进程
                unset($active_processes[$batch_index]);
                break; // 只处理一个完成的进程，然后返回
            }
        }

        // 如果没有进程完成，短暂等待
        if (!empty($active_processes)) {
            usleep(100000); // 等待 0.1 秒
        }
    }

    /**
     * 解析批次输出
     */
    private function parse_batch_output($output) {
        $results = [];

        if ($output) {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'RESULT:') === 0) {
                    $result_data = json_decode(substr($line, 7), true);
                    if ($result_data) {
                        $results[] = $result_data;
                    }
                }
            }
        }

        return $results;
    }

    /**
     * 串行处理（回退方案）
     */
    private function process_sequential($svg_files, $category_data) {
        $results = [];
        $frontend_manager = SS_Frontend_Manager::get_instance();
        
        foreach ($svg_files as $index => $svg_file) {
            try {
                $result = $frontend_manager->generate_svg_preview_file(
                    $svg_file,
                    $category_data['name'],
                    $category_data['colors'][0] ?? '#000000',
                    true,
                    $category_data['term_id']
                );
                
                if ($result) {
                    $results[] = [
                        'file' => $svg_file,
                        'index' => $index,
                        'success' => true,
                        'preview_url' => $result
                    ];
                }
            } catch (Exception $e) {
                error_log('[并发处理器] 串行处理失败: ' . $e->getMessage());
            }
        }
        
        return $results;
    }
    
    /**
     * 更新处理进度
     */
    private function update_progress($term_id, $current_batch, $total_batches, $completed_files) {
        $progress = [
            'current_batch' => $current_batch,
            'total_batches' => $total_batches,
            'completed_files' => $completed_files,
            'percentage' => round(($current_batch / $total_batches) * 100, 2),
            'timestamp' => time()
        ];
        
        // 使用 transient 存储进度，有效期 5 分钟
        set_transient('ss_progress_' . $term_id, $progress, 300);
        
        error_log('[并发处理器] 更新进度: ' . $progress['percentage'] . '% (' . $current_batch . '/' . $total_batches . ')');
    }
    
    /**
     * 获取处理进度
     */
    public function get_progress($term_id) {
        return get_transient('ss_progress_' . $term_id);
    }
    
    /**
     * 清理进度数据
     */
    public function clear_progress($term_id) {
        delete_transient('ss_progress_' . $term_id);
    }
    
    /**
     * 获取系统信息
     */
    public function get_system_info() {
        return [
            'cpu_cores' => $this->detect_cpu_cores(),
            'max_workers' => $this->max_workers,
            'batch_size' => $this->batch_size,
            'concurrent_enabled' => get_option('ss_enable_concurrent_processing', true),
            'current_load' => $this->performance_monitor->get_cpu_usage()
        ];
    }
}

/**
 * 性能监控器类
 */
class SS_Performance_Monitor {
    /**
     * 获取 CPU 使用率
     */
    public function get_cpu_usage() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $cpu_count = $this->get_cpu_count();
            return ($load[0] / $cpu_count) * 100;
        }

        return 0; // 无法获取时返回 0
    }

    /**
     * 获取 CPU 核心数
     */
    private function get_cpu_count() {
        if (function_exists('shell_exec')) {
            $output = shell_exec('nproc 2>/dev/null');
            if ($output) {
                return intval(trim($output));
            }
        }

        return 1; // 默认值
    }

    /**
     * 【优化】自适应调整并发数量 - 更智能的性能调节
     */
    public function adjust_concurrency($max_workers) {
        $cpu_usage = $this->get_cpu_usage();
        $memory_usage = $this->get_memory_usage();
        $current_workers = get_transient('ss_current_workers') ?: $max_workers;

        // 【优化】更智能的调整策略
        if ($cpu_usage > 90 || $memory_usage > 85) {
            // 系统负载过高，减少并发数
            $new_workers = max(2, $current_workers - 2); // 最少保持2个工作进程
            error_log('[性能监控] 系统负载过高 (CPU: ' . round($cpu_usage, 2) . '%, 内存: ' . round($memory_usage, 2) . '%)，减少并发数至: ' . $new_workers);
        } elseif ($cpu_usage < 60 && $memory_usage < 70 && $current_workers < $max_workers) {
            // 系统负载较低，可以增加并发数
            $increase = ($cpu_usage < 40) ? 2 : 1; // CPU使用率很低时增加更多
            $new_workers = min($max_workers, $current_workers + $increase);
            error_log('[性能监控] 系统负载较低 (CPU: ' . round($cpu_usage, 2) . '%, 内存: ' . round($memory_usage, 2) . '%)，增加并发数至: ' . $new_workers);
        } else {
            $new_workers = $current_workers;
        }

        // 缓存当前并发数
        set_transient('ss_current_workers', $new_workers, 300);

        return $new_workers;
    }

    /**
     * 【修复】获取系统内存使用率（百分比）
     */
    public function get_memory_usage() {
        if (function_exists('shell_exec')) {
            // 获取系统内存使用情况
            $output = shell_exec("free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'");
            if ($output) {
                return floatval($output);
            }
        }

        return 0; // 无法获取时返回0
    }

    /**
     * 【新增】获取PHP内存使用情况
     */
    public function get_php_memory_usage() {
        return [
            'used' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => $this->get_memory_limit()
        ];
    }

    /**
     * 获取内存限制
     */
    private function get_memory_limit() {
        $limit = ini_get('memory_limit');
        if ($limit == -1) {
            return PHP_INT_MAX;
        }

        return $this->convert_to_bytes($limit);
    }

    /**
     * 转换内存大小为字节
     */
    private function convert_to_bytes($size) {
        $unit = strtolower(substr($size, -1));
        $value = intval($size);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }
}
