#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import xml.etree.ElementTree as ET
from collections import defaultdict

def replace_colors_in_svg(input_file, output_file, preset_colors):
    """
    解析并修改单个SVG文件中的颜色，然后输出到 output_file。
    使用PHP传递的预设颜色，不再自行排序和映射颜色。

    修复：确保只处理原始灰度颜色，避免处理已经包含预设颜色的SVG文件
    """
    try:
        # 【修复】增加文件完整性检查
        if not os.path.exists(input_file):
            print(f"Error: Input file does not exist: {input_file}")
            return False

        # 检查文件大小
        file_size = os.path.getsize(input_file)
        if file_size == 0:
            print(f"Error: Input file is empty: {input_file}")
            return False

        # 【修复】先读取文件内容检查完整性
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        if not content.strip():
            print(f"Error: Input file has no content: {input_file}")
            return False

        # 【修复】检查SVG文件是否完整（有结束标签）
        if not content.strip().endswith('</svg>'):
            print(f"Error: SVG file appears to be incomplete (missing </svg>): {input_file}")
            return False

        # 解析 SVG 文件
        tree = ET.parse(input_file)
        root = tree.getroot()
    except ET.ParseError as e:
        print(f"Error: Failed to parse SVG file {input_file}: {e}")
        return False
    except Exception as e:
        print(f"Error: Failed to read SVG file {input_file}: {e}")
        return False

    # 定义命名空间
    ns = {
        'svg': 'http://www.w3.org/2000/svg',
        'xlink': 'http://www.w3.org/1999/xlink'
    }

    # 收集所有 fill 的颜色统计
    colors = defaultdict(int)
    for elem in root.findall('.//*[@fill]', ns):
        fill_value = elem.get('fill', '').strip().lower()
        if fill_value and fill_value != 'none':
            colors[fill_value] += 1

    # 如果没有找到颜色，直接返回
    if not colors:
        print(f"Warning: No fill colors found in {input_file}")
        # 仍然保存文件，但不做任何修改
        try:
            tree.write(output_file, xml_declaration=True, encoding='utf-8', method="xml")
            return True
        except Exception as e:
            print(f"Error: Failed to write output file {output_file}: {e}")
            return False

    # 调试：输出从SVG中提取到的原始颜色
    print(f"Colors extracted from {os.path.basename(input_file)}: {list(colors.keys())}")

    # 检查是否包含预设颜色（防止处理已经处理过的文件）
    preset_colors_lower = [color.lower() for color in preset_colors]
    extracted_colors = list(colors.keys())

    # 如果提取到的颜色中包含预设颜色，说明这是已经处理过的文件
    contains_preset_colors = any(color in preset_colors_lower for color in extracted_colors)
    if contains_preset_colors:
        print(f"Warning: {os.path.basename(input_file)} contains preset colors, may be already processed!")
        print(f"Extracted colors: {extracted_colors}")
        print(f"Preset colors (lowercase): {preset_colors_lower}")

        # 添加详细的错误日志
        overlapping_colors = [color for color in extracted_colors if color in preset_colors_lower]
        print(f"Overlapping colors found: {overlapping_colors}")
        print(f"This suggests the input SVG file has already been processed and contains preset colors instead of original grayscale colors.")

    # 将收集到的颜色从暗到亮排序
    sorted_colors = sorted(colors.keys(), key=lambda c: int(c[1:], 16) if c.startswith('#') else 0, reverse=True)

    # 创建颜色映射表，直接使用PHP传递的预设颜色
    # 不再颠倒预设颜色顺序，使用PHP传递的顺序
    color_mapping = {}

    # 确保至少有一个预设颜色
    if not preset_colors:
        print(f"Warning: No preset colors provided for {input_file}")
        preset_colors = ['#d2bb8f']  # 使用默认颜色

    # 为每个SVG中的颜色分配一个预设颜色
    # 如果SVG中的颜色数量超过预设颜色数量，则循环使用预设颜色
    for i, color in enumerate(sorted_colors):
        color_mapping[color] = preset_colors[i % len(preset_colors)]

    print(f"Color mapping for {os.path.basename(input_file)}: {color_mapping}")

    # 替换颜色
    for elem in root.findall('.//*[@fill]', ns):
        fill_value = elem.get('fill', '').strip().lower()
        if fill_value and fill_value != 'none' and fill_value in color_mapping:
            elem.set('fill', color_mapping[fill_value])

    # 清理命名空间（确保输出 XML 中的命名空间干净）
    ET.register_namespace('', ns['svg'])
    ET.register_namespace('xlink', ns['xlink'])

    # 【修复】保存修改后的 SVG，增加错误处理
    try:
        tree.write(output_file, xml_declaration=True, encoding='utf-8', method="xml")

        # 【修复】验证输出文件是否正确生成
        if not os.path.exists(output_file):
            print(f"Error: Output file was not created: {output_file}")
            return False

        # 检查输出文件大小
        output_size = os.path.getsize(output_file)
        if output_size == 0:
            print(f"Error: Output file is empty: {output_file}")
            return False

        return True
    except Exception as e:
        print(f"Error: Failed to write output file {output_file}: {e}")
        return False


def process_svg_files(input_folder, output_folder, preset_colors, category_name):
    """
    遍历一个文件夹，处理所有 .svg 文件。
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 将分类名称转换为安全的文件名 (用于日志记录，不再用于文件命名)
    safe_category_name = category_name.replace(' ', '_')
    safe_category_name = ''.join(
        c for c in safe_category_name if c.isalnum() or c in ('_', '-')
    ).rstrip()

    for filename in os.listdir(input_folder):
        if filename.lower().endswith(".svg"):
            input_file = os.path.join(input_folder, filename)
            # 不再添加类目前缀，直接使用原文件名
            output_filename = filename
            output_file = os.path.join(output_folder, output_filename)
            try:
                replace_colors_in_svg(input_file, output_file, preset_colors)
                print(f'Processed {input_file} -> {output_file}')
            except Exception as e:
                print(f'Error processing {input_file}: {e}')


def process_svg_files_list(list_file_path, output_folder, preset_colors, category_name):
    """
    读取 list_file_path 中的每一行(即单个SVG绝对路径)，只处理这些 SVG 文件。
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 将分类名称转换为安全的文件名 (用于日志记录，不再用于文件命名)
    safe_category_name = category_name.replace(' ', '_')
    safe_category_name = ''.join(
        c for c in safe_category_name if c.isalnum() or c in ('_', '-')
    ).rstrip()

    with open(list_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            svg_path = line.strip()
            if not svg_path.lower().endswith('.svg'):
                continue  # 如果这一行不是 .svg 文件，就跳过

            filename = os.path.basename(svg_path)
            # 不再添加类目前缀，直接使用原文件名
            output_filename = filename
            output_file = os.path.join(output_folder, output_filename)
            try:
                replace_colors_in_svg(svg_path, output_file, preset_colors)
                print(f'Processed {svg_path} -> {output_file}')
            except Exception as e:
                print(f'Error processing {svg_path}: {e}')


if __name__ == "__main__":
    """
    使用说明：
    1) 不带 --file-list 时：
       python process_svg.py <input_path> <output_path> <preset_colors> <category_name>
       - <input_path>:  可以是一个SVG文件路径，或是一个文件夹路径。
       - <output_path>: 输出的文件(若input是单个文件)或文件夹(若input是文件夹)。
       - <preset_colors>: 用逗号分隔的颜色值，如 "#ff0000,#00ff00,#0000ff"
       - <category_name>: 用于给输出文件名加前缀。

    2) 带 --file-list=xxx 时：
       python process_svg.py --file-list=/tmp/svg_list.txt <output_path> <preset_colors> <category_name>
       - /tmp/svg_list.txt 每行一个 .svg 的绝对路径
       - 其余参数同上
    """
    # 先尝试解析 --file-list 参数（若存在则走"只处理清单中的文件"分支）
    file_list_path = None
    for arg in sys.argv[1:]:
        if arg.startswith('--file-list='):
            file_list_path = arg.split('=', 1)[1].strip()
            sys.argv.remove(arg)  # 从 sys.argv 中移除，以免后面长度判断报错
            break

    if file_list_path:
        # 需要四个参数： (output_path, preset_colors, category_name) + --file-list
        if len(sys.argv) != 4:
            print("Usage: python process_svg.py --file-list=<list_file> <output_path> <preset_colors> <category_name>")
            sys.exit(1)

        output_path = sys.argv[1]
        raw_colors = sys.argv[2].split(',')
        category_name = sys.argv[3]

        # 处理颜色 - 确保所有颜色都有#前缀
        preset_colors = ['#' + color.strip().lstrip('\\#').lstrip('#') for color in raw_colors]
        print(f"Using preset colors: {preset_colors}")

        # 调用专用函数仅处理清单中的 SVG
        process_svg_files_list(file_list_path, output_path, preset_colors, category_name)

    else:
        # 无 --file-list 参数 => 走原来模式
        if len(sys.argv) != 5:
            print("Usage: python process_svg.py <input_path> <output_path> <preset_colors> <category_name>")
            sys.exit(1)

        input_path = sys.argv[1]
        output_path = sys.argv[2]
        raw_colors = sys.argv[3].split(',')
        category_name = sys.argv[4]

        # 处理颜色 - 确保所有颜色都有#前缀
        preset_colors = ['#' + color.strip().lstrip('\\#').lstrip('#') for color in raw_colors]
        print(f"Using preset colors: {preset_colors}")

        # 判断 input_path 是单文件还是目录
        if os.path.isfile(input_path):
            # 处理单个 .svg
            try:
                replace_colors_in_svg(input_path, output_path, preset_colors)
                print(f'Processed {input_path} -> {output_path}')
            except Exception as e:
                print(f'Error processing {input_path}: {e}')
        elif os.path.isdir(input_path):
            # 处理文件夹中的所有 .svg
            process_svg_files(input_path, output_path, preset_colors, category_name)
        else:
            print("Invalid input path (not a file nor directory).")
            sys.exit(1)