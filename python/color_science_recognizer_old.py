#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Color Science颜色识别器
基于科学色彩理论的专业颜色识别系统
"""

import sys
import json
import math
import os
from datetime import datetime

# 检查并导入必要的库
try:
    import numpy as np
    from PIL import Image
    from sklearn.cluster import KMeans
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    IMPORT_ERROR = str(e)

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print(json.dumps({"status": "error", "message": "参数错误"}))
        return

    params_file = sys.argv[1]
    
    try:
        with open(params_file, "r", encoding="utf-8") as f:
            params = json.load(f)
    except Exception as e:
        print(json.dumps({"status": "error", "message": f"参数文件读取失败: {e}"}))
        return

    # 执行颜色识别
    result = recognize_image_colors(params)
    print(json.dumps(result, ensure_ascii=False))

def recognize_image_colors(params):
    """识别图片颜色"""
    try:
        # 检查依赖库是否可用
        if not DEPENDENCIES_AVAILABLE:
            return {"status": "error", "message": f"缺少必要的库: {IMPORT_ERROR}"}

        image_path = params["image_path"]
        color_count = params.get("color_count", 8)
        verbose_mode = params.get("verbose_mode", False)
        
        debug_log = []
        debug_log.append(f"🎨 Color Science颜色识别开始")
        debug_log.append(f"📁 图片路径: {image_path}")
        debug_log.append(f"🔢 目标颜色数量: {color_count}")
        debug_log.append(f"📊 配置参数: {params}")
        
        # 检查图片文件
        if not os.path.exists(image_path):
            return {"status": "error", "message": f"图片文件不存在: {image_path}"}

        # 加载图片
        try:
            image = Image.open(image_path)
            if image.mode != "RGB":
                image = image.convert("RGB")
            debug_log.append(f"✅ 图片加载成功，尺寸: {image.size}, 模式: {image.mode}")
        except Exception as e:
            return {"status": "error", "message": f"图片加载失败: {e}"}

        # 提取颜色 - 固定提取8个颜色进行聚类（参考文档标准）
        cluster_count = 8
        debug_log.append(f"🔍 开始K-means聚类，聚类数量: {cluster_count}（固定8个颜色）")
        colors, counts, total_pixels = extract_colors_kmeans(image, cluster_count)
        debug_log.append(f"📈 聚类完成，总像素数: {total_pixels}")

        # 创建颜色信息列表
        color_info_list = []
        debug_log.append(f"🎯 开始分析提取的颜色:")
        for i, (color, count) in enumerate(zip(colors, counts)):
            percentage = (count / total_pixels) * 100
            hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"

            # 使用Color Science分类
            label = classify_color_science(tuple(color))

            color_info = {
                "color_code": hex_color,
                "hex": hex_color,
                "rgb": tuple(int(c) for c in color),
                "percentage": percentage,
                "label": label,
                "colour_science": label
            }
            color_info_list.append(color_info)
            debug_log.append(f"  颜色{i+1}: {hex_color} ({label}) - 占比{percentage:.1f}%")

        # 按占比排序
        color_info_list.sort(key=lambda x: x["percentage"], reverse=True)
        debug_log.append(f"📊 颜色按占比排序完成")

        # 应用Color Science处理逻辑
        debug_log.append(f"🧪 开始应用Color Science处理逻辑")
        processed_colors = apply_color_science_processing(color_info_list, color_count, params, debug_log)

        debug_log.append(f"✅ Color Science颜色识别完成，最终输出{len(processed_colors)}个颜色")
        
        return {
            "status": "success",
            "image_path": image_path,
            "colors": processed_colors,
            "debug_log": debug_log if verbose_mode else [],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        return {"status": "error", "message": f"识别失败: {e}"}

def extract_colors_kmeans(image, n_colors):
    """使用K-means提取颜色"""
    try:
        # 将图片转换为numpy数组
        data = np.array(image)
        data = data.reshape((-1, 3))
        
        # 移除透明像素（如果有的话）
        data = data[~np.all(data == [0, 0, 0], axis=1)]
        
        total_pixels = len(data)
        
        # 使用K-means聚类
        n_colors = min(n_colors, len(data))
        if n_colors <= 0:
            n_colors = 1
            
        kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
        kmeans.fit(data)
        
        # 获取聚类中心（颜色）
        colors = kmeans.cluster_centers_.astype(int)
        
        # 计算每个颜色的像素数量
        labels = kmeans.labels_
        counts = np.bincount(labels)
        
        return colors, counts, total_pixels
        
    except Exception as e:
        # 如果K-means失败，返回基本颜色
        basic_colors = np.array([[128, 128, 128], [255, 255, 255], [0, 0, 0]])
        basic_counts = np.array([100, 100, 100])
        return basic_colors, basic_counts, 300

def classify_color_science(rgb_color):
    """Color Science颜色分类"""
    try:
        r, g, b = rgb_color
        
        # 转换为HSV色彩空间进行分类
        h, s, v = rgb_to_hsv(r, g, b)
        
        # 判断黑白灰
        if v < 0.2:  # 很暗
            return "black"
        elif v > 0.8 and s < 0.1:  # 很亮且饱和度低
            return "white"
        elif s < 0.15:  # 饱和度很低
            return "gray"
        
        # 基于色相分类彩色
        hue_deg = h * 360
        if 0 <= hue_deg < 15 or 345 <= hue_deg <= 360:
            return "red"
        elif 15 <= hue_deg < 45:
            return "orange"
        elif 45 <= hue_deg < 75:
            return "yellow"
        elif 75 <= hue_deg < 165:
            return "green"
        elif 165 <= hue_deg < 195:
            return "cyan"
        elif 195 <= hue_deg < 255:
            return "blue"
        elif 255 <= hue_deg < 285:
            return "purple"
        elif 285 <= hue_deg < 315:
            return "magenta"
        elif 315 <= hue_deg < 345:
            return "pink"
        else:
            return "unknown"
            
    except Exception:
        # 简单的RGB分类作为备选
        r, g, b = rgb_color
        if r > 200 and g < 100 and b < 100:
            return "red"
        elif r < 100 and g > 200 and b < 100:
            return "green"
        elif r < 100 and g < 100 and b > 200:
            return "blue"
        elif r > 200 and g > 200 and b < 100:
            return "yellow"
        elif r < 50 and g < 50 and b < 50:
            return "black"
        elif r > 200 and g > 200 and b > 200:
            return "white"
        else:
            return "gray"

def apply_color_science_processing(color_info_list, target_count, params, debug_log):
    """应用Color Science颜色处理逻辑"""
    # 根据本地软件标准，保持原始颜色，不过度增强
    final_target_count = 5
    debug_log.append(f"🔬 Color Science处理开始，输入{len(color_info_list)}个颜色，目标{final_target_count}个（本地软件兼容模式）")
    
    # 分离黑白灰和其他颜色
    bwg_colors = []  # black, white, gray
    non_bwg_colors = []

    debug_log.append(f"🎨 开始分离黑白灰和彩色:")
    for color in color_info_list:
        label = color["label"].lower()
        if label in ["black", "white", "gray", "grey"]:
            bwg_colors.append(color)
            debug_log.append(f"  ⚫ 黑白灰: {color['hex']} ({label}) - {color['percentage']:.1f}%")
        else:
            non_bwg_colors.append(color)
            debug_log.append(f"  🌈 彩色: {color['hex']} ({label}) - {color['percentage']:.1f}%")

    debug_log.append(f"📊 分离结果: 黑白灰{len(bwg_colors)}个, 彩色{len(non_bwg_colors)}个")

    # 决定使用哪些颜色 - 本地软件兼容模式：保留更多颜色
    if non_bwg_colors:
        # 存在其他颜色，使用非黑白灰颜色，但不过度去重
        working_colors = non_bwg_colors
        debug_log.append(f"✅ 检测到彩色，使用{len(working_colors)}个非黑白灰颜色（本地软件兼容模式）")
    else:
        # 仅包含黑白灰，使用预设颜色
        dominant_type = determine_dominant_bwg_type(bwg_colors)
        debug_log.append(f"⚫ 仅检测到黑白灰，使用预设颜色方案: {dominant_type}")
        return get_fixed_bwg_colors(dominant_type, final_target_count, debug_log)

    # 本地软件兼容模式：不进行标签去重，保持原始颜色多样性
    debug_log.append(f"🔄 本地软件兼容模式：保留所有颜色，不进行标签去重")
    unique_colors = working_colors[:final_target_count]  # 直接取前N个颜色
    
    for i, color in enumerate(unique_colors):
        debug_log.append(f"  ✅ 保留颜色{i+1}: {color['hex']} ({color['label']}) - {color['percentage']:.1f}%")

    debug_log.append(f"📊 本地软件兼容模式结果: 保留前{len(unique_colors)}个颜色")

    # 本地软件兼容模式：最小化颜色增强，保持原始颜色
    debug_log.append(f"🎨 本地软件兼容模式：最小化颜色增强，保持原始颜色")
    enhanced_colors = []
    for i, color in enumerate(unique_colors):
        debug_log.append(f"  处理颜色{i+1}: {color['hex']} ({color['label']})")
        # 应用最小化的颜色增强
        enhanced_color = apply_minimal_color_enhancement(color, params, debug_log)
        enhanced_colors.append(enhanced_color)

    debug_log.append(f"✨ 最小化颜色增强完成，处理了{len(enhanced_colors)}个颜色")

    # 本地软件兼容模式：直接返回可用的颜色，不生成变体
    debug_log.append(f"🔢 本地软件兼容模式：直接使用可用颜色，不生成变体")
    final_colors = enhanced_colors[:final_target_count]  # 只取前N个可用颜色
    
    debug_log.append(f"📋 最终颜色列表（本地软件兼容）:")
    for i, color in enumerate(final_colors):
        debug_log.append(f"  最终颜色{i+1}: {color['hex']} ({color['label']}) - {color['percentage']:.1f}%")

    debug_log.append(f"🎯 Color Science处理完成，最终输出{len(final_colors)}个颜色（本地软件兼容模式）")
    return final_colors

def apply_minimal_color_enhancement(color, params, debug_log):
    """应用最小化颜色增强（本地软件兼容模式）"""
    enhanced_color = color.copy()
    
    r, g, b = color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_hex = color["hex"]
    debug_log.append(f"    🎨 原始颜色: {original_hex} RGB({r},{g},{b}) HSV({h:.3f},{s:.3f},{v:.3f})")
    
    # 本地软件兼容模式：只应用非常轻微的调整
    debug_log.append(f"    🔧 本地软件兼容模式：保持原始颜色，仅微调")
    
    # 只应用用户自定义参数（如果有）
    saturation_adj = params.get("saturation_adjustment", 0) / 100.0
    lightness_adj = params.get("lightness_adjustment", 0) / 100.0
    
    if saturation_adj != 0 or lightness_adj != 0:
        debug_log.append(f"    ⚙️ 应用用户自定义调整:")
        old_s, old_v = s, v
        s = max(0, min(1, s + saturation_adj))
        v = max(0, min(1, v + lightness_adj))
        debug_log.append(f"      🎛️ 饱和度调整: {old_s:.3f} + {saturation_adj:.3f} = {s:.3f}")
        debug_log.append(f"      🎛️ 亮度调整: {old_v:.3f} + {lightness_adj:.3f} = {v:.3f}")
    else:
        debug_log.append(f"    ⚙️ 无用户自定义调整，保持原始颜色")
    
    # 转换回RGB
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    enhanced_color["rgb"] = (new_r, new_g, new_b)
    enhanced_color["hex"] = new_hex
    enhanced_color["color_code"] = new_hex
    
    if new_hex != original_hex:
        debug_log.append(f"    ✅ 微调完成: {original_hex} → {new_hex}")
    else:
        debug_log.append(f"    ✅ 保持原色: {original_hex}")
    
    return enhanced_color

def apply_color_enhancement(color, params, debug_log):
    """应用颜色增强（完整模式）"""
    enhanced_color = color.copy()
    
    r, g, b = color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_hex = color["hex"]
    debug_log.append(f"    🎨 原始颜色: {original_hex} RGB({r},{g},{b}) HSV({h:.3f},{s:.3f},{v:.3f})")
    
    original_s, original_v = s, v
    
    # 应用鲜艳度调节（如果启用）
    if params.get("vivid_enhancement", True):
        debug_log.append(f"    ✨ 应用高级鲜艳度调节方案:")
        
        # 亮度调整
        old_v = v
        if v < 0.45:
            v = min(v * 1.25, 0.60)
            debug_log.append(f"      💡 亮度调整(低亮度): {old_v:.3f} → {v:.3f} (×1.25)")
        elif v < 0.65:
            v = min(v * 1.15, 0.75)
            debug_log.append(f"      💡 亮度调整(中亮度): {old_v:.3f} → {v:.3f} (×1.15)")
        else:
            debug_log.append(f"      💡 亮度保持: {v:.3f} (高亮度无需调整)")
        
        # 饱和度调整
        old_s = s
        if s < 0.35:
            s = min(s * 1.40, 0.55)
            debug_log.append(f"      🌈 饱和度调整(低饱和): {old_s:.3f} → {s:.3f} (×1.40)")
        elif s < 0.70:
            s = min(s * 1.25, 0.85)
            debug_log.append(f"      🌈 饱和度调整(中饱和): {old_s:.3f} → {s:.3f} (×1.25)")
        else:
            s = min(s * 1.10, 1.00)
            debug_log.append(f"      🌈 饱和度调整(高饱和): {old_s:.3f} → {s:.3f} (×1.10)")
    else:
        debug_log.append(f"    ⏸️ 鲜艳度调节已禁用")
    
    # 应用打印补偿（如果启用）
    if params.get("print_compensation", True):
        preset = params.get("print_preset", "mid_cotton")
        debug_log.append(f"    🖨️ 应用数码直喷打印补偿 ({preset}):")
        if preset == "mid_cotton":
            old_s, old_v = s, v
            # 中磅纯棉预设的补偿
            s = min(s * 1.1, 1.0)
            v = min(v * 1.05, 1.0)
            debug_log.append(f"      📄 中磅纯棉补偿: 饱和度{old_s:.3f}→{s:.3f} 亮度{old_v:.3f}→{v:.3f}")
    else:
        debug_log.append(f"    ⏸️ 打印补偿已禁用")
    
    # 应用用户自定义参数
    saturation_adj = params.get("saturation_adjustment", 0) / 100.0
    lightness_adj = params.get("lightness_adjustment", 0) / 100.0
    
    if saturation_adj != 0 or lightness_adj != 0:
        debug_log.append(f"    ⚙️ 应用用户自定义调整:")
        old_s, old_v = s, v
        s = max(0, min(1, s + saturation_adj))
        v = max(0, min(1, v + lightness_adj))
        debug_log.append(f"      🎛️ 饱和度调整: {old_s:.3f} + {saturation_adj:.3f} = {s:.3f}")
        debug_log.append(f"      🎛️ 亮度调整: {old_v:.3f} + {lightness_adj:.3f} = {v:.3f}")
    else:
        debug_log.append(f"    ⚙️ 用户自定义调整: 无 (饱和度+{saturation_adj}, 亮度+{lightness_adj})")
    
    # 转换回RGB
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    enhanced_color["rgb"] = (new_r, new_g, new_b)
    enhanced_color["hex"] = new_hex
    enhanced_color["color_code"] = new_hex
    
    debug_log.append(f"    ✅ 增强完成: {original_hex} → {new_hex}")
    debug_log.append(f"    📊 变化总结: 饱和度{original_s:.3f}→{s:.3f} 亮度{original_v:.3f}→{v:.3f}")
    
    return enhanced_color

def determine_dominant_bwg_type(bwg_colors):
    """判定黑白灰主导类型"""
    if not bwg_colors:
        return "gray"
    
    # 按占比排序，取最大的
    bwg_colors.sort(key=lambda x: x["percentage"], reverse=True)
    return bwg_colors[0]["label"].lower()

def get_fixed_bwg_colors(dominant_type, target_count, debug_log):
    """获取固定的黑白灰预设颜色"""
    presets = {
        "black": ["#404040", "#353535", "#4a4a4a", "#3f3f3f", "#2a2a2a"],
        "white": ["#f4f4f4", "#dfdfdf", "#e9e9e9", "#d4d4d4", "#b7b7b7"],
        "gray": ["#dedede", "#b1b1b1", "#cdcdcd", "#959595", "#747474"]
    }
    
    debug_log.append(f"⚫ 使用固定黑白灰预设方案: {dominant_type}")
    colors = presets.get(dominant_type, presets["gray"])
    result = []
    
    debug_log.append(f"🎨 生成{target_count}个{dominant_type}预设颜色:")
    for i, hex_color in enumerate(colors[:target_count]):
        rgb = hex_to_rgb(hex_color)
        result.append({
            "color_code": hex_color,
            "hex": hex_color,
            "rgb": rgb,
            "percentage": 100.0 / target_count,
            "label": dominant_type,
            "colour_science": dominant_type
        })
        debug_log.append(f"  预设{i+1}: {hex_color} ({dominant_type})")
    
    return result

def ensure_enough_colors(colors, target_count, debug_log):
    """确保有足够的颜色"""
    debug_log.append(f"🔢 检查颜色数量: 当前{len(colors)}个，目标{target_count}个")
    
    if len(colors) >= target_count:
        debug_log.append(f"✅ 颜色数量充足，选择前{target_count}个")
        final_colors = colors[:target_count]
        for i, color in enumerate(final_colors):
            debug_log.append(f"  最终颜色{i+1}: {color['hex']} ({color['label']})")
        return final_colors
    
    # 如果颜色不够，生成变体
    debug_log.append(f"⚠️ 颜色不足，需要生成{target_count - len(colors)}个变体")
    result = colors.copy()
    
    variant_count = 0
    while len(result) < target_count:
        # 基于现有颜色生成变体
        base_index = len(result) % len(colors)
        base_color = result[base_index]
        variant = generate_color_variant(base_color, len(result), debug_log)
        result.append(variant)
        variant_count += 1
        debug_log.append(f"  生成变体{variant_count}: {variant['hex']} (基于{base_color['hex']})")
    
    debug_log.append(f"✅ 颜色数量补充完成，总计{len(result)}个")
    final_colors = result[:target_count]
    debug_log.append(f"📋 最终颜色列表:")
    for i, color in enumerate(final_colors):
        debug_log.append(f"  最终颜色{i+1}: {color['hex']} ({color['label']})")
    
    return final_colors

def generate_color_variant(base_color, variant_index, debug_log):
    """生成颜色变体"""
    r, g, b = base_color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_h, original_s, original_v = h, s, v
    variant_type = ""
    
    # 根据变体索引调整颜色
    if variant_index % 4 == 1:
        # 变体1：提高亮度
        v = min(v * 1.1, 1.0)
        variant_type = "提高亮度"
    elif variant_index % 4 == 2:
        # 变体2：降低饱和度
        s = max(s * 0.8, 0.0)
        variant_type = "降低饱和度"
    elif variant_index % 4 == 3:
        # 变体3：调整色相
        h = (h + 0.1) % 1.0
        variant_type = "调整色相"
    else:
        variant_type = "基础变体"
    
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    variant = base_color.copy()
    variant["rgb"] = (new_r, new_g, new_b)
    variant["hex"] = new_hex
    variant["color_code"] = new_hex
    variant["percentage"] = base_color["percentage"] * 0.5
    variant["label"] = base_color["label"] + "_variant"
    
    debug_log.append(f"    🔄 变体生成({variant_type}): {base_color['hex']} → {new_hex}")
    debug_log.append(f"      HSV变化: ({original_h:.3f},{original_s:.3f},{original_v:.3f}) → ({h:.3f},{s:.3f},{v:.3f})")
    
    return variant

def rgb_to_hsv(r, g, b):
    """RGB转HSV"""
    r, g, b = r/255.0, g/255.0, b/255.0
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    diff = max_val - min_val
    
    # 计算色相
    if diff == 0:
        h = 0
    elif max_val == r:
        h = (60 * ((g - b) / diff) + 360) % 360
    elif max_val == g:
        h = (60 * ((b - r) / diff) + 120) % 360
    elif max_val == b:
        h = (60 * ((r - g) / diff) + 240) % 360
    
    h = h / 360.0
    
    # 计算饱和度
    s = 0 if max_val == 0 else diff / max_val
    
    # 计算明度
    v = max_val
    
    return h, s, v

def hsv_to_rgb(h, s, v):
    """HSV转RGB"""
    h = h * 360
    c = v * s
    x = c * (1 - abs((h / 60) % 2 - 1))
    m = v - c
    
    if 0 <= h < 60:
        r, g, b = c, x, 0
    elif 60 <= h < 120:
        r, g, b = x, c, 0
    elif 120 <= h < 180:
        r, g, b = 0, c, x
    elif 180 <= h < 240:
        r, g, b = 0, x, c
    elif 240 <= h < 300:
        r, g, b = x, 0, c
    elif 300 <= h < 360:
        r, g, b = c, 0, x
    else:
        r, g, b = 0, 0, 0
    
    r = int((r + m) * 255)
    g = int((g + m) * 255)
    b = int((b + m) * 255)
    
    return r, g, b

def hex_to_rgb(hex_color):
    """十六进制转RGB"""
    hex_color = hex_color.lstrip("#")
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

if __name__ == "__main__":
    main()