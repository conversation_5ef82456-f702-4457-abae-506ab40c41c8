#!/usr/bin/env python3
"""
批量 SVG 处理脚本 - 支持多线程并发处理
充分利用多核 CPU 资源，提升 SVG 预览图生成速度

@package ShoeSVGGenerator
@since 1.0.0
"""

import sys
import json
import os
import tempfile
import concurrent.futures
import multiprocessing
import time
from collections import defaultdict
import xml.etree.ElementTree as ET

def replace_colors_in_svg(input_file, output_file, preset_colors):
    """
    处理单个 SVG 文件的颜色替换
    """
    try:
        # 解析 SVG 文件
        tree = ET.parse(input_file)
        root = tree.getroot()

        # 定义命名空间
        ns = {
            'svg': 'http://www.w3.org/2000/svg',
            'xlink': 'http://www.w3.org/1999/xlink'
        }

        # 收集所有 fill 的颜色统计
        colors = defaultdict(int)
        for elem in root.findall('.//*[@fill]', ns):
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value != 'none':
                colors[fill_value] += 1

        # 如果没有找到颜色，直接保存原文件
        if not colors:
            tree.write(output_file, xml_declaration=True, encoding='utf-8', method="xml")
            return True

        # 过滤出灰度颜色（避免处理已经包含预设颜色的 SVG）
        gray_colors = []
        for color, count in colors.items():
            if color.startswith('#') and len(color) == 7:
                try:
                    r = int(color[1:3], 16)
                    g = int(color[3:5], 16)
                    b = int(color[5:7], 16)
                    
                    # 检查是否为灰度色（RGB 值相近）
                    if abs(r - g) <= 10 and abs(g - b) <= 10 and abs(r - b) <= 10:
                        gray_colors.append((color, count))
                except ValueError:
                    continue

        # 如果没有灰度颜色，直接保存
        if not gray_colors:
            tree.write(output_file, xml_declaration=True, encoding='utf-8', method="xml")
            return True

        # 按使用频率排序灰度颜色
        gray_colors.sort(key=lambda x: x[1], reverse=True)

        # 创建颜色映射
        color_mapping = {}
        for i, (gray_color, _) in enumerate(gray_colors):
            if i < len(preset_colors):
                color_mapping[gray_color] = preset_colors[i]

        # 替换颜色
        for elem in root.findall('.//*[@fill]', ns):
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value != 'none' and fill_value in color_mapping:
                elem.set('fill', color_mapping[fill_value])

        # 清理命名空间
        ET.register_namespace('', ns['svg'])
        ET.register_namespace('xlink', ns['xlink'])

        # 保存修改后的 SVG
        tree.write(output_file, xml_declaration=True, encoding='utf-8', method="xml")
        return True

    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        return False

def process_single_svg_task(task_data):
    """
    处理单个 SVG 任务的包装函数
    专门针对前端预览图生成优化
    """
    svg_file, category_data, output_dir = task_data

    try:
        # 准备输出文件路径
        filename = os.path.basename(svg_file)
        safe_name = os.path.splitext(filename)[0]

        # 【修复】处理后的 SVG 文件路径 - 直接使用output_dir，不再添加processed子目录
        processed_svg_file = os.path.join(output_dir, filename)

        # 【调试】记录路径信息
        print(f"[并发处理][调试] 输出目录: {output_dir}")
        print(f"[并发处理][调试] 处理后SVG文件: {processed_svg_file}")

        # 预览图目录
        preview_dir = os.path.join(output_dir, 'previews')
        os.makedirs(preview_dir, exist_ok=True)

        # 处理 SVG 颜色
        success = replace_colors_in_svg(svg_file, processed_svg_file, category_data['colors'])

        if success:
            # 生成预览图（这里主要是处理 SVG，WebP 转换由 PHP 处理）
            result = {
                'file': svg_file,
                'processed_svg': processed_svg_file,
                'preview_dir': preview_dir,
                'success': True,
                'timestamp': time.time(),
                'colors_applied': category_data['colors']
            }
        else:
            result = {
                'file': svg_file,
                'success': False,
                'error': 'SVG color processing failed',
                'timestamp': time.time()
            }

        # 输出结果（PHP 会解析这个）
        print(f"RESULT:{json.dumps(result)}")

        return result

    except Exception as e:
        error_result = {
            'file': svg_file,
            'success': False,
            'error': str(e),
            'timestamp': time.time()
        }
        print(f"RESULT:{json.dumps(error_result)}")
        return error_result

def process_svg_batch(svg_files, category_data, max_workers=None):
    """
    批量处理 SVG 文件，使用多线程并发
    """
    if not svg_files:
        return []
    
    # 确定工作线程数
    if max_workers is None:
        max_workers = min(len(svg_files), multiprocessing.cpu_count())
    else:
        # 确保 max_workers 是整数类型（从 PHP 传入时可能是字符串）
        max_workers = int(max_workers)

    max_workers = max(1, min(max_workers, len(svg_files)))
    
    print(f"开始批量处理 {len(svg_files)} 个 SVG 文件，使用 {max_workers} 个工作线程")
    
    # 准备输出目录
    # 【修复】使用正确的输出路径结构
    if 'output_dir' in category_data:
        output_dir = category_data['output_dir']
    else:
        # 【修复】使用与WebP预览图生成一致的路径结构
        safe_category_name = "".join(c for c in category_data['name'] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_category_name = safe_category_name.lower().replace(' ', '-')
        upload_dir = '/wp-content/uploads/shoe-svg-generator'  # 这个会被 PHP 传入
        output_dir = os.path.join(upload_dir, 'processed_svg', safe_category_name)
    
    # 准备任务数据
    tasks = [(svg_file, category_data, output_dir) for svg_file in svg_files]
    
    results = []
    start_time = time.time()
    
    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(process_single_svg_task, task): task for task in tasks}
        
        # 收集结果
        for future in concurrent.futures.as_completed(future_to_task):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                task = future_to_task[future]
                error_result = {
                    'file': task[0],
                    'success': False,
                    'error': str(e),
                    'timestamp': time.time()
                }
                results.append(error_result)
                print(f"RESULT:{json.dumps(error_result)}")
    
    duration = time.time() - start_time
    success_count = sum(1 for r in results if r['success'])
    
    print(f"批量处理完成: {success_count}/{len(svg_files)} 成功，耗时: {duration:.2f} 秒")
    
    return results

def main():
    """
    主函数 - 从命令行参数读取批次数据并处理
    """
    if len(sys.argv) != 2:
        print("Usage: python3 process_svg_batch.py <batch_data_file>")
        sys.exit(1)
    
    batch_data_file = sys.argv[1]
    
    try:
        # 读取批次数据
        with open(batch_data_file, 'r', encoding='utf-8') as f:
            batch_data = json.load(f)
        
        svg_files = batch_data['files']
        category_data = batch_data['category_data']
        max_workers = batch_data.get('max_workers', None)
        
        # 处理颜色数据
        if 'colors' in category_data and category_data['colors']:
            # 确保颜色格式正确
            colors = []
            for color in category_data['colors']:
                if isinstance(color, str):
                    color = color.strip()
                    if not color.startswith('#'):
                        color = '#' + color
                    colors.append(color)
            category_data['colors'] = colors
        else:
            category_data['colors'] = ['#000000']  # 默认黑色
        
        print(f"使用预设颜色: {category_data['colors']}")
        
        # 批量处理
        results = process_svg_batch(svg_files, category_data, max_workers)
        
        # 输出最终统计
        success_count = sum(1 for r in results if r['success'])
        print(f"FINAL_STATS: {success_count}/{len(svg_files)} 文件处理成功")
        
    except Exception as e:
        print(f"批量处理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
