#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile
from collections import defaultdict

# 使用lxml进行更可靠的XML处理
try:
    from lxml import etree
    HAS_LXML = True
except ImportError:
    import xml.etree.ElementTree as ET
    HAS_LXML = False
    print("Warning: lxml not available, falling back to xml.etree.ElementTree")

def replace_colors_in_svg_dom(input_file, output_file, preset_colors):
    """
    使用基于DOM的颜色替换，避免文本级替换破坏XML结构
    实现原子写入，避免并发写入问题
    """
    try:
        # 【修复】文件完整性检查
        if not os.path.exists(input_file):
            print(f"Error: Input file does not exist: {input_file}")
            return False

        file_size = os.path.getsize(input_file)
        if file_size == 0:
            print(f"Error: Input file is empty: {input_file}")
            return False

        # 【关键修复】使用lxml解析，支持大型文档
        if HAS_LXML:
            parser = etree.XMLParser(huge_tree=True, recover=True)
            try:
                tree = etree.parse(input_file, parser)
                root = tree.getroot()
            except etree.XMLSyntaxError as e:
                print(f"Error: Failed to parse SVG file {input_file} with lxml: {e}")
                return False
        else:
            # 回退到标准库
            try:
                tree = ET.parse(input_file)
                root = tree.getroot()
            except ET.ParseError as e:
                print(f"Error: Failed to parse SVG file {input_file} with ElementTree: {e}")
                return False

    except Exception as e:
        print(f"Error: Failed to read SVG file {input_file}: {e}")
        return False

    # 定义命名空间
    if HAS_LXML:
        nsmap = root.nsmap
        svg_ns = nsmap.get(None, 'http://www.w3.org/2000/svg')
        namespaces = {'svg': svg_ns}
    else:
        namespaces = {
            'svg': 'http://www.w3.org/2000/svg',
            'xlink': 'http://www.w3.org/1999/xlink'
        }

    # 【关键修复】基于DOM的颜色收集，只处理fill和stroke属性
    colors = defaultdict(int)
    
    if HAS_LXML:
        # 使用XPath精确查找有颜色属性的元素
        for elem in root.xpath('//*[@fill or @stroke]'):
            # 处理fill属性
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value != 'none' and not fill_value.startswith('url('):
                colors[fill_value] += 1
            
            # 处理stroke属性
            stroke_value = elem.get('stroke', '').strip().lower()
            if stroke_value and stroke_value != 'none' and not stroke_value.startswith('url('):
                colors[stroke_value] += 1
    else:
        # ElementTree版本
        for elem in root.iter():
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value != 'none' and not fill_value.startswith('url('):
                colors[fill_value] += 1
            
            stroke_value = elem.get('stroke', '').strip().lower()
            if stroke_value and stroke_value != 'none' and not stroke_value.startswith('url('):
                colors[stroke_value] += 1

    if not colors:
        print(f"Warning: No fill/stroke colors found in {input_file}")

    print(f"Colors extracted from {os.path.basename(input_file)}: {list(colors.keys())}")

    # 检查是否包含预设颜色（防止重复处理）
    preset_colors_lower = [color.lower() for color in preset_colors]
    extracted_colors = list(colors.keys())
    
    contains_preset_colors = any(color in preset_colors_lower for color in extracted_colors)
    if contains_preset_colors:
        print(f"Warning: {os.path.basename(input_file)} contains preset colors, may be already processed!")

    # 创建颜色映射表
    sorted_colors = sorted(colors.keys(), key=lambda c: int(c[1:], 16) if c.startswith('#') else 0, reverse=True)
    color_mapping = {}

    if not preset_colors:
        print(f"Warning: No preset colors provided for {input_file}")
        preset_colors = ['#d2bb8f']

    for i, color in enumerate(sorted_colors):
        color_mapping[color] = preset_colors[i % len(preset_colors)]

    print(f"Color mapping for {os.path.basename(input_file)}: {color_mapping}")

    # 【关键修复】基于DOM的颜色替换，避免破坏XML结构
    if HAS_LXML:
        for elem in root.xpath('//*[@fill or @stroke]'):
            # 替换fill属性
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value in color_mapping:
                elem.set('fill', color_mapping[fill_value])
            
            # 替换stroke属性
            stroke_value = elem.get('stroke', '').strip().lower()
            if stroke_value and stroke_value in color_mapping:
                elem.set('stroke', color_mapping[stroke_value])
    else:
        for elem in root.iter():
            fill_value = elem.get('fill', '').strip().lower()
            if fill_value and fill_value in color_mapping:
                elem.set('fill', color_mapping[fill_value])
            
            stroke_value = elem.get('stroke', '').strip().lower()
            if stroke_value and stroke_value in color_mapping:
                elem.set('stroke', color_mapping[stroke_value])

    # 【P1优先级修复】增强原子写入，避免并发竞态条件
    try:
        # 创建临时文件在同一目录下，使用更安全的后缀
        output_dir = os.path.dirname(output_file)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 使用进程ID和时间戳确保临时文件名唯一性
        import time
        temp_suffix = f'.tmp_{os.getpid()}_{int(time.time() * 1000000)}'
        temp_path = output_file + temp_suffix

        try:
            # 【P1修复】完整写到临时文件
            with open(temp_path, 'wb') as temp_file:
                if HAS_LXML:
                    # 使用lxml写入，保持格式
                    tree.write(temp_file, xml_declaration=True, encoding='utf-8',
                              pretty_print=True, method="xml")
                else:
                    # ElementTree写入
                    tree.write(temp_file, xml_declaration=True, encoding='utf-8', method="xml")

                # 【P1关键】强制刷新到磁盘，确保数据完整写入
                temp_file.flush()
                os.fsync(temp_file.fileno())

            # 【P1修复】额外的页面缓存同步
            try:
                os.sync()  # 同步所有文件系统缓存
            except (OSError, AttributeError):
                pass  # 某些系统可能不支持os.sync()

            # 【P1修复】校验后才标记成功 - 使用多重验证
            if not validate_svg_file_comprehensive(temp_path):
                print(f"Error: Generated SVG file failed comprehensive validation: {temp_path}")
                try:
                    os.unlink(temp_path)
                except:
                    pass
                return False

            # 【P1关键】原子替换 - os.rename()在同一文件系统上是原子的
            os.rename(temp_path, output_file)

            # 【P1修复】最终验证 - 确保原子替换成功
            if not os.path.exists(output_file):
                print(f"Error: Atomic rename failed, output file does not exist: {output_file}")
                return False

            final_size = os.path.getsize(output_file)
            if final_size == 0:
                print(f"Error: Output file is empty after atomic rename: {output_file}")
                return False

            # 【P1修复】最终文件完整性验证
            if not validate_svg_file_comprehensive(output_file):
                print(f"Error: Final output file failed validation: {output_file}")
                try:
                    os.unlink(output_file)
                except:
                    pass
                return False

            print(f"Successfully processed {os.path.basename(input_file)} -> {os.path.basename(output_file)}")
            print(f"EXIT_CODE:0")  # 明确的成功标记
            return True

        except Exception as e:
            # 清理临时文件
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
            print(f"Error during atomic write process: {e}")
            raise e

    except Exception as e:
        print(f"Error: Failed to write output file {output_file}: {e}")
        print(f"EXIT_CODE:1")  # 明确的失败标记
        return False

def validate_svg_file(svg_path):
    """
    验证SVG文件的XML结构是否正确
    """
    try:
        if HAS_LXML:
            parser = etree.XMLParser(huge_tree=True)
            etree.parse(svg_path, parser)
        else:
            ET.parse(svg_path)
        return True
    except Exception as e:
        print(f"SVG validation failed for {svg_path}: {e}")
        return False

def validate_svg_file_comprehensive(svg_path):
    """
    【P1修复】综合验证SVG文件完整性
    包括文件大小、XML结构、SVG根元素等多重检查
    """
    try:
        # 1. 基本文件检查
        if not os.path.exists(svg_path):
            print(f"Validation failed: File does not exist: {svg_path}")
            return False

        file_size = os.path.getsize(svg_path)
        if file_size == 0:
            print(f"Validation failed: File is empty: {svg_path}")
            return False

        if file_size < 100:  # SVG文件至少应该有基本的XML声明和根元素
            print(f"Validation failed: File too small ({file_size} bytes): {svg_path}")
            return False

        # 2. 检查文件尾部是否完整（检查是否以</svg>结尾）
        try:
            with open(svg_path, 'rb') as f:
                f.seek(-50, 2)  # 从文件末尾向前读50字节
                tail_content = f.read().decode('utf-8', errors='ignore')
                if '</svg>' not in tail_content:
                    print(f"Validation failed: File does not end with </svg>: {svg_path}")
                    return False
        except Exception as e:
            print(f"Validation failed: Cannot read file tail: {svg_path}, error: {e}")
            return False

        # 3. XML结构验证
        try:
            if HAS_LXML:
                parser = etree.XMLParser(huge_tree=True, recover=False)  # 不使用恢复模式，严格验证
                tree = etree.parse(svg_path, parser)
                root = tree.getroot()
            else:
                tree = ET.parse(svg_path)
                root = tree.getroot()

            # 4. 验证是否为有效的SVG文件
            if root.tag.lower() not in ['svg', '{http://www.w3.org/2000/svg}svg']:
                print(f"Validation failed: Root element is not SVG: {svg_path}")
                return False

        except Exception as e:
            print(f"Validation failed: XML parsing error for {svg_path}: {e}")
            return False

        return True

    except Exception as e:
        print(f"Comprehensive validation failed for {svg_path}: {e}")
        return False

def process_svg_files_list(list_file_path, output_folder, preset_colors, category_name):
    """
    【P2修复】处理文件列表中的SVG文件，增加进程同步
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder, exist_ok=True)

    success_count = 0
    total_count = 0
    failed_files = []

    try:
        with open(list_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                svg_path = line.strip()
                if not svg_path.lower().endswith('.svg'):
                    continue

                total_count += 1
                filename = os.path.basename(svg_path)
                output_file = os.path.join(output_folder, filename)

                try:
                    # 【P1修复】处理前检查源文件完整性
                    if not validate_svg_file_comprehensive(svg_path):
                        print(f'❌ Source file validation failed: {svg_path}')
                        failed_files.append(filename)
                        continue

                    if replace_colors_in_svg_dom(svg_path, output_file, preset_colors):
                        success_count += 1
                        print(f'✓ Processed {svg_path} -> {output_file}')
                    else:
                        print(f'❌ Failed to process {svg_path}')
                        failed_files.append(filename)
                except Exception as e:
                    print(f'❌ Error processing {svg_path}: {e}')
                    failed_files.append(filename)

        # 【P2修复】详细的处理结果报告
        print(f"\nProcessing complete: {success_count}/{total_count} files successful")
        if failed_files:
            print(f"Failed files: {', '.join(failed_files)}")

        # 【P2修复】只有全部成功才返回True
        return success_count == total_count

    except Exception as e:
        print(f"Error in batch processing: {e}")
        return False

if __name__ == "__main__":
    # 【P2修复】增强的参数验证和错误处理
    if len(sys.argv) < 4:
        print("Usage: python process_svg_fixed.py --file-list=<list_file> <output_path> <preset_colors> <category_name>")
        print("   or: python process_svg_fixed.py <input_path> <output_path> <preset_colors> <category_name>")
        print("EXIT_CODE:1")
        sys.exit(1)

    try:
        # 解析--file-list参数
        file_list_path = None
        for arg in sys.argv[1:]:
            if arg.startswith('--file-list='):
                file_list_path = arg.split('=', 1)[1].strip()
                sys.argv.remove(arg)
                break

        if file_list_path:
            if len(sys.argv) != 4:
                print("Usage: python process_svg_fixed.py --file-list=<list_file> <output_path> <preset_colors> <category_name>")
                print("EXIT_CODE:1")
                sys.exit(1)

            output_path = sys.argv[1]
            raw_colors = sys.argv[2].split(',')
            category_name = sys.argv[3]

            preset_colors = ['#' + color.strip().lstrip('\\#').lstrip('#') for color in raw_colors]
            print(f"Using preset colors: {preset_colors}")
            print(f"Using {'lxml' if HAS_LXML else 'ElementTree'} for XML processing")

            # 【P2修复】验证文件列表存在
            if not os.path.exists(file_list_path):
                print(f"Error: File list does not exist: {file_list_path}")
                print("EXIT_CODE:1")
                sys.exit(1)

            success = process_svg_files_list(file_list_path, output_path, preset_colors, category_name)
            exit_code = 0 if success else 1
            print(f"EXIT_CODE:{exit_code}")
            sys.exit(exit_code)

        else:
            if len(sys.argv) != 5:
                print("Usage: python process_svg_fixed.py <input_path> <output_path> <preset_colors> <category_name>")
                print("EXIT_CODE:1")
                sys.exit(1)

            input_path = sys.argv[1]
            output_path = sys.argv[2]
            raw_colors = sys.argv[3].split(',')
            category_name = sys.argv[4]

            preset_colors = ['#' + color.strip().lstrip('\\#').lstrip('#') for color in raw_colors]
            print(f"Using preset colors: {preset_colors}")
            print(f"Using {'lxml' if HAS_LXML else 'ElementTree'} for XML processing")

            # 【P2修复】验证输入文件存在
            if not os.path.isfile(input_path):
                print(f"Error: Input file does not exist: {input_path}")
                print("EXIT_CODE:1")
                sys.exit(1)

            success = replace_colors_in_svg_dom(input_path, output_path, preset_colors)
            exit_code = 0 if success else 1
            print(f"EXIT_CODE:{exit_code}")
            sys.exit(exit_code)

    except Exception as e:
        print(f"Fatal error in main: {e}")
        print("EXIT_CODE:1")
        sys.exit(1)
