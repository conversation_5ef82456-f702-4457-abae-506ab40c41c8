#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Color Science颜色识别器 - 基于本地软件完整实现
完全符合本地软件svg_processor_gui.py中的Color Science算法
"""

import os
import sys
import json
import math
from datetime import datetime

# 检查依赖库
DEPENDENCIES_AVAILABLE = True
IMPORT_ERROR = ""

try:
    import numpy as np
    from PIL import Image
    from sklearn.cluster import KMeans
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    IMPORT_ERROR = str(e)

def srgb_to_linear(srgb_value):
    """将sRGB值转换为线性RGB（去γ校正）"""
    if srgb_value <= 0.04045:
        return srgb_value / 12.92
    else:
        return pow((srgb_value + 0.055) / 1.055, 2.4)

def xyz_to_lab(xyz):
    """将XYZ转换为Lab色彩空间"""
    # D65标准光源白点
    xn, yn, zn = 0.95047, 1.00000, 1.08883
    
    x, y, z = xyz[0] / xn, xyz[1] / yn, xyz[2] / zn
    
    def f(t):
        if t > (6/29)**3:
            return t**(1/3)
        else:
            return (1/3) * ((29/6)**2) * t + 4/29
    
    fx, fy, fz = f(x), f(y), f(z)
    
    L = 116 * fy - 16
    a = 500 * (fx - fy)
    b = 200 * (fy - fz)
    
    return [L, a, b]

def full_colour_science_classify(hue_angle, chroma, lightness):
    """完整的Colour Science颜色分类（基于本地软件实现）"""
    try:
        # 1. 处理极低彩度的灰度色 - 修复：降低灰色判定阈值
        if chroma < 1.5:  # 从3降低到1.5，减少误判
            if lightness < 15:
                return "black"
            elif lightness > 90:
                return "white"
            else:
                return "gray"

        # 2. 处理低彩度但有色相的颜色 - 修复：调整阈值范围
        elif 1.5 <= chroma < 6:  # 从3-8调整为1.5-6
            # 根据亮度和色相进行精细分类
            if lightness > 85:
                if 30 <= hue_angle < 90:
                    return "cream"  # 奶油色
                elif 300 <= hue_angle < 360 or 0 <= hue_angle < 30:
                    return "pink"   # 粉色
                else:
                    return "white"
            elif lightness < 25:
                if 30 <= hue_angle < 90:
                    return "brown"  # 深棕色
                elif 350 <= hue_angle < 360 or 0 <= hue_angle < 30:
                    return "maroon" # 栗色
                else:
                    return "black"
            else:
                # 中等亮度的低彩度颜色
                if 30 <= hue_angle < 90:
                    return "beige"  # 米色
                elif 90 <= hue_angle < 150:
                    return "olive"  # 橄榄色
                elif 350 <= hue_angle < 360 or 0 <= hue_angle < 30:
                    return "rose"   # 玫瑰色
                else:
                    return "gray"

        # 3. 处理中等彩度的颜色 - 修复：调整阈值范围
        elif 6 <= chroma < 15:  # 从8-20调整为6-15
            if 350 <= hue_angle < 360 or 0 <= hue_angle < 30:
                if lightness > 70:
                    return "pink"
                elif lightness < 30:
                    return "maroon"
                else:
                    return "red"
            elif 30 <= hue_angle < 60:
                if lightness > 70:
                    return "peach"  # 桃色
                elif lightness < 30:
                    return "brown"
                else:
                    return "orange"
            elif 60 <= hue_angle < 90:
                if lightness > 70:
                    return "cream"
                elif lightness < 30:
                    return "olive"
                else:
                    return "yellow"
            elif 90 <= hue_angle < 150:
                if lightness > 70:
                    return "mint"   # 薄荷色
                elif lightness < 30:
                    return "forest" # 森林绿
                else:
                    return "green"
            elif 150 <= hue_angle < 210:
                if lightness > 70:
                    return "aqua"   # 水色
                elif lightness < 30:
                    return "teal"   # 青绿色
                else:
                    return "cyan"
            elif 210 <= hue_angle < 270:
                if lightness > 70:
                    return "sky"    # 天蓝色
                elif lightness < 30:
                    return "navy"   # 海军蓝
                else:
                    return "blue"
            elif 270 <= hue_angle < 330:
                if lightness > 70:
                    return "lavender" # 薰衣草色
                elif lightness < 30:
                    return "indigo"   # 靛蓝色
                else:
                    return "purple"
            else:
                if lightness > 70:
                    return "magenta"
                else:
                    return "purple"

        # 4. 处理高彩度的鲜艳颜色 - 修复：调整阈值
        else:  # chroma >= 15，从20调整为15
            if 350 <= hue_angle < 360 or 0 <= hue_angle < 20:
                return "red"
            elif 20 <= hue_angle < 50:
                return "orange"
            elif 50 <= hue_angle < 80:
                return "yellow"
            elif 80 <= hue_angle < 140:
                return "green"
            elif 140 <= hue_angle < 200:
                return "cyan"
            elif 200 <= hue_angle < 260:
                return "blue"
            elif 260 <= hue_angle < 320:
                return "purple"
            elif 320 <= hue_angle < 350:
                return "magenta"
            else:
                return "red"

    except Exception as e:
        return "unknown"

def classify_color_hsv_fallback(rgb_color):
    """HSV备用颜色分类方案"""
    try:
        r, g, b = rgb_color
        h, s, v = rgb_to_hsv(r, g, b)
        
        # 判断黑白灰
        if v < 0.2:
            return "black"
        elif v > 0.8 and s < 0.1:
            return "white"
        elif s < 0.15:
            return "gray"
        
        # 基于色相分类彩色
        hue_deg = h * 360
        if 0 <= hue_deg < 15 or 345 <= hue_deg <= 360:
            return "red"
        elif 15 <= hue_deg < 45:
            return "orange"
        elif 45 <= hue_deg < 75:
            return "yellow"
        elif 75 <= hue_deg < 165:
            return "green"
        elif 165 <= hue_deg < 195:
            return "cyan"
        elif 195 <= hue_deg < 255:
            return "blue"
        elif 255 <= hue_deg < 285:
            return "purple"
        elif 285 <= hue_deg < 315:
            return "magenta"
        elif 315 <= hue_deg < 345:
            return "pink"
        else:
            return "unknown"
    except Exception:
        return "unknown"

def classify_color_science(rgb_color):
    """Color Science颜色分类（基于本地软件的完整实现）"""
    try:
        r, g, b = rgb_color

        # 将RGB值转换为0-1范围的浮点数
        rgb_normalized = [c / 255.0 for c in rgb_color]

        # 转换为XYZ色彩空间（简化版本，不依赖colour库）
        # 使用sRGB到XYZ的标准转换矩阵
        r_lin = srgb_to_linear(rgb_normalized[0])
        g_lin = srgb_to_linear(rgb_normalized[1])
        b_lin = srgb_to_linear(rgb_normalized[2])

        # XYZ转换矩阵（sRGB）
        x = 0.4124564 * r_lin + 0.3575761 * g_lin + 0.1804375 * b_lin
        y = 0.2126729 * r_lin + 0.7151522 * g_lin + 0.0721750 * b_lin
        z = 0.0193339 * r_lin + 0.1191920 * g_lin + 0.9503041 * b_lin

        # 转换为Lab色彩空间
        lab = xyz_to_lab([x, y, z])

        # 提取Lab分量
        L_star = lab[0]  # 亮度
        a_star = lab[1]  # 绿-红轴
        b_star = lab[2]  # 蓝-黄轴

        # 计算色相角度
        hue_angle = math.atan2(b_star, a_star) * 180 / math.pi
        if hue_angle < 0:
            hue_angle += 360

        # 计算彩度
        chroma = math.sqrt(a_star**2 + b_star**2)

        # 使用完整的颜色分类逻辑
        return full_colour_science_classify(hue_angle, chroma, L_star)

    except Exception as e:
        # 如果Lab转换失败，使用HSV备用方案
        return classify_color_hsv_fallback(rgb_color)

def classify_color_science_with_debug(rgb_color, debug_log=None):
    """Color Science颜色分类（带调试信息）"""
    if debug_log is None:
        debug_log = []

    try:
        r, g, b = rgb_color
        debug_log.append(f"    🔬 分析颜色 RGB({r},{g},{b})")

        # 将RGB值转换为0-1范围的浮点数
        rgb_normalized = [c / 255.0 for c in rgb_color]

        # 转换为XYZ色彩空间（简化版本，不依赖colour库）
        # 使用sRGB到XYZ的标准转换矩阵
        r_lin = srgb_to_linear(rgb_normalized[0])
        g_lin = srgb_to_linear(rgb_normalized[1])
        b_lin = srgb_to_linear(rgb_normalized[2])

        # XYZ转换矩阵（sRGB）
        x = 0.4124564 * r_lin + 0.3575761 * g_lin + 0.1804375 * b_lin
        y = 0.2126729 * r_lin + 0.7151522 * g_lin + 0.0721750 * b_lin
        z = 0.0193339 * r_lin + 0.1191920 * g_lin + 0.9503041 * b_lin

        debug_log.append(f"    📊 XYZ: ({x:.3f}, {y:.3f}, {z:.3f})")

        # 转换为Lab色彩空间
        lab = xyz_to_lab([x, y, z])

        # 提取Lab分量
        L_star = lab[0]  # 亮度
        a_star = lab[1]  # 绿-红轴
        b_star = lab[2]  # 蓝-黄轴

        debug_log.append(f"    🎨 Lab: L*={L_star:.2f}, a*={a_star:.2f}, b*={b_star:.2f}")

        # 计算色相角度
        hue_angle = math.atan2(b_star, a_star) * 180 / math.pi
        if hue_angle < 0:
            hue_angle += 360

        # 计算彩度
        chroma = math.sqrt(a_star**2 + b_star**2)

        debug_log.append(f"    🌈 色相角度: {hue_angle:.1f}°, 彩度: {chroma:.2f}")

        # 使用完整的颜色分类逻辑
        classification = full_colour_science_classify(hue_angle, chroma, L_star)

        # 判断分类依据
        if chroma < 1.5:
            debug_log.append(f"    ⚫ 分类依据: 极低彩度({chroma:.2f} < 1.5) → {classification}")
        elif 1.5 <= chroma < 6:
            debug_log.append(f"    🎨 分类依据: 低彩度({chroma:.2f} 在1.5-6之间) → {classification}")
        elif 6 <= chroma < 15:
            debug_log.append(f"    🌈 分类依据: 中等彩度({chroma:.2f} 在6-15之间) → {classification}")
        else:
            debug_log.append(f"    ✨ 分类依据: 高彩度({chroma:.2f} >= 15) → {classification}")

        return classification

    except Exception as e:
        debug_log.append(f"    ❌ Lab转换失败: {e}，使用HSV备用方案")
        # 如果Lab转换失败，使用HSV备用方案
        return classify_color_hsv_fallback(rgb_color)

def rgb_to_hsv(r, g, b):
    """RGB转HSV"""
    r, g, b = r/255.0, g/255.0, b/255.0
    mx = max(r, g, b)
    mn = min(r, g, b)
    df = mx-mn
    if mx == mn:
        h = 0
    elif mx == r:
        h = (60 * ((g-b)/df) + 360) % 360
    elif mx == g:
        h = (60 * ((b-r)/df) + 120) % 360
    elif mx == b:
        h = (60 * ((r-g)/df) + 240) % 360
    if mx == 0:
        s = 0
    else:
        s = df/mx
    v = mx
    return h/360.0, s, v

def hsv_to_rgb(h, s, v):
    """HSV转RGB"""
    h = h * 360
    c = v * s
    x = c * (1 - abs((h / 60) % 2 - 1))
    m = v - c
    
    if 0 <= h < 60:
        r, g, b = c, x, 0
    elif 60 <= h < 120:
        r, g, b = x, c, 0
    elif 120 <= h < 180:
        r, g, b = 0, c, x
    elif 180 <= h < 240:
        r, g, b = 0, x, c
    elif 240 <= h < 300:
        r, g, b = x, 0, c
    else:
        r, g, b = c, 0, x
    
    r = int((r + m) * 255)
    g = int((g + m) * 255)
    b = int((b + m) * 255)
    
    return r, g, b

def hex_to_rgb(hex_color):
    """十六进制转RGB"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def apply_color_science_processing_authentic(color_info_list, target_count=5, params=None, debug_log=None):
    """应用Color Science颜色处理逻辑（基于本地软件真实实现）"""
    if debug_log is None:
        debug_log = []
    if params is None:
        params = {}
        
    try:
        debug_log.append(f"🎨 开始Color Science颜色处理 - 原始颜色数: {len(color_info_list)}")

        # 步骤1：分离黑白灰和其他颜色
        non_bwg_colors = []
        bwg_colors = []

        for color in color_info_list:
            label = color['label'].lower()
            if label in ['black', 'white', 'gray', 'grey']:
                bwg_colors.append(color)
                debug_log.append(f"🖤 检测到黑白灰颜色: {color['hex']} ({label}) - {color['percentage']:.1f}%")
            else:
                non_bwg_colors.append(color)
                debug_log.append(f"🎨 检测到彩色: {color['hex']} ({label}) - {color['percentage']:.1f}%")

        debug_log.append(f"📊 步骤1：彩色{len(non_bwg_colors)}个，黑白灰{len(bwg_colors)}个")

        # 步骤2：决定使用哪些颜色
        if non_bwg_colors:
            # 存在其他颜色，仅使用非黑白灰颜色
            working_colors = non_bwg_colors
            debug_log.append(f"✅ 步骤2：使用彩色颜色，隐藏黑白灰")
        else:
            # 仅包含黑白灰，根据面积最大值判定主导类型并使用预设颜色
            debug_log.append(f"⚠️ 步骤2：仅包含黑白灰，根据面积判定主导类型")
            dominant_type = determine_dominant_bwg_type(bwg_colors)
            debug_log.append(f"🎯 主导类型: {dominant_type}")
            return get_fixed_bwg_colors(dominant_type, target_count, debug_log)

        # 步骤3：去重复标签，保留第一个
        unique_colors = []
        seen_labels = set()

        for color in working_colors:
            label = color['label'].lower()
            if label not in seen_labels:
                seen_labels.add(label)
                unique_colors.append(color)
                debug_log.append(f"✅ 保留颜色: {color['hex']} ({label}) - {color['percentage']:.1f}%")
            else:
                debug_log.append(f"🔄 跳过重复标签: {color['hex']} ({label}) - {color['percentage']:.1f}%")

        debug_log.append(f"📊 步骤3：去重后剩余{len(unique_colors)}个唯一颜色")

        # 步骤4：先对所有颜色应用高级鲜艳度调节和打印补偿（基于本地软件实现）
        debug_log.append(f"🎨 步骤4：应用高级鲜艳度调节和打印补偿")
        enhanced_colors = []
        for i, color in enumerate(unique_colors):
            debug_log.append(f"  处理颜色{i+1}: {color['hex']} ({color['label']})")
            enhanced_color = apply_vivid_enhancement_and_print_compensation(color, params, debug_log)
            enhanced_colors.append(enhanced_color)

        debug_log.append(f"✨ 颜色增强完成，处理了{len(enhanced_colors)}个颜色")

        # 步骤5：确保最终5个颜色，不足时生成变体（在增强之后）
        debug_log.append(f"🔢 步骤5：确保5个颜色，不足时生成变体（增强后）")
        final_colors = ensure_five_colors_with_variants(enhanced_colors, target_count, debug_log)

        debug_log.append(f"✅ Color Science颜色处理完成 - 最终颜色数: {len(final_colors)}")
        return final_colors

    except Exception as e:
        debug_log.append(f"❌ Color Science颜色处理失败: {e}")
        # 如果处理失败，返回原始颜色（前target_count个）
        return color_info_list[:target_count]

def determine_dominant_bwg_type(bwg_colors):
    """判定黑白灰主导类型"""
    if not bwg_colors:
        return "gray"
    
    # 按占比排序，取最大的
    bwg_colors.sort(key=lambda x: x["percentage"], reverse=True)
    return bwg_colors[0]["label"].lower()

def get_fixed_bwg_colors(dominant_type="gray", target_count=5, debug_log=None):
    """获取固定的黑白灰预设颜色"""
    if debug_log is None:
        debug_log = []
        
    # 预设颜色（基于本地软件配置）
    presets = {
        "black": ["#404040", "#353535", "#4a4a4a", "#3f3f3f", "#2a2a2a"],
        "white": ["#f4f4f4", "#dfdfdf", "#e9e9e9", "#d4d4d4", "#b7b7b7"],
        "gray": ["#dedede", "#b1b1b1", "#cdcdcd", "#959595", "#747474"]
    }
    
    debug_log.append(f"⚫ 使用固定黑白灰预设方案: {dominant_type}")
    colors = presets.get(dominant_type, presets["gray"])
    result = []
    
    debug_log.append(f"🎨 生成{target_count}个{dominant_type}预设颜色:")
    for i, hex_color in enumerate(colors[:target_count]):
        rgb = hex_to_rgb(hex_color)
        result.append({
            "color_code": hex_color,
            "hex": hex_color,
            "rgb": rgb,
            "percentage": 100.0 / target_count,
            "label": dominant_type,
            "colour_science": dominant_type
        })
        debug_log.append(f"  预设{i+1}: {hex_color} ({dominant_type})")
    
    return result

def ensure_five_colors_with_correct_variants(colors, target_count, debug_log):
    """确保有足够的颜色，按照正确的逻辑生成变体"""
    debug_log.append(f"🔢 检查颜色数量: 当前{len(colors)}个，目标{target_count}个")
    
    if len(colors) >= target_count:
        debug_log.append(f"✅ 颜色数量充足，选择前{target_count}个")
        return colors[:target_count]
    
    # 根据剩余颜色数量应用不同的变体生成逻辑
    remaining_count = len(colors)
    debug_log.append(f"⚠️ 颜色不足，剩余{remaining_count}个，需要生成变体")
    
    result = []
    
    if remaining_count == 1:
        # a. 剩下1个颜色的情况
        debug_log.append(f"📋 应用1个颜色的变体生成逻辑")
        base_color = colors[0]
        result.append(base_color)  # 颜色1=原始颜色
        result.append(generate_specific_variant(base_color, "亮度-5%", debug_log))  # 颜色2=亮度调低5%
        result.append(generate_specific_variant(base_color, "饱和度-10%亮度+10%", debug_log))  # 颜色3=饱和度-10%，亮度+10%
        result.append(generate_specific_variant(base_color, "饱和度-5%亮度-10%", debug_log))  # 颜色4=饱和度-5%，亮度-10%
        result.append(generate_specific_variant(base_color, "亮度-15%", debug_log))  # 颜色5=亮度-15%
        
    elif remaining_count == 2:
        # b. 剩下2个颜色的情况
        debug_log.append(f"📋 应用2个颜色的变体生成逻辑")
        color1, color2 = colors[0], colors[1]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(generate_specific_variant(color2, "亮度-5%", debug_log))  # 颜色4=第二个颜色亮度调低5%
        result.append(generate_specific_variant(color1, "亮度-15%", debug_log))  # 颜色5=第一个颜色亮度-15%
        
    elif remaining_count == 3:
        # c. 剩下3个颜色的情况
        debug_log.append(f"📋 应用3个颜色的变体生成逻辑")
        color1, color2, color3 = colors[0], colors[1], colors[2]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(generate_specific_variant(color2, "亮度-5%", debug_log))  # 颜色4=第二个颜色亮度调低5%
        result.append(color3)  # 颜色5=第三个原始颜色
        
    elif remaining_count == 4:
        # d. 剩下4个颜色的情况
        debug_log.append(f"📋 应用4个颜色的变体生成逻辑")
        color1, color2, color3, color4 = colors[0], colors[1], colors[2], colors[3]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(color3)  # 颜色4=第三个原始颜色
        result.append(color4)  # 颜色5=第四个原始颜色
    
    debug_log.append(f"✅ 变体生成完成，总计{len(result)}个颜色")
    return result[:target_count]

def generate_specific_variant(base_color, variant_type, debug_log):
    """生成特定类型的颜色变体"""
    r, g, b = base_color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_h, original_s, original_v = h, s, v
    
    # 根据变体类型调整颜色
    if variant_type == "亮度-5%":
        v = max(v * 0.95, 0.0)
    elif variant_type == "亮度-15%":
        v = max(v * 0.85, 0.0)
    elif variant_type == "饱和度-10%亮度+10%":
        s = max(s * 0.90, 0.0)
        v = min(v * 1.10, 1.0)
    elif variant_type == "饱和度-5%亮度-10%":
        s = max(s * 0.95, 0.0)
        v = max(v * 0.90, 0.0)
    
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    variant = base_color.copy()
    variant["rgb"] = (new_r, new_g, new_b)
    variant["hex"] = new_hex
    variant["color_code"] = new_hex
    variant["percentage"] = base_color["percentage"] * 0.8  # 变体占比稍低
    variant["label"] = base_color["label"]  # 保持相同标签
    
    debug_log.append(f"    🔄 变体生成({variant_type}): {base_color['hex']} → {new_hex}")
    debug_log.append(f"      HSV变化: ({original_h:.3f},{original_s:.3f},{original_v:.3f}) → ({h:.3f},{s:.3f},{v:.3f})")
    
    return variant

def ensure_five_colors_with_variants(colors, target_count, debug_log):
    """确保有足够的颜色，按照正确的逻辑生成变体（增强后）"""
    debug_log.append(f"🔢 检查颜色数量: 当前{len(colors)}个，目标{target_count}个")
    
    if len(colors) >= target_count:
        debug_log.append(f"✅ 颜色数量充足，选择前{target_count}个")
        return colors[:target_count]
    
    # 根据剩余颜色数量应用不同的变体生成逻辑（增强后的颜色）
    remaining_count = len(colors)
    debug_log.append(f"⚠️ 颜色不足，剩余{remaining_count}个，需要生成变体")
    
    result = []
    
    if remaining_count == 1:
        # a. 剩下1个颜色的情况
        debug_log.append(f"📋 应用1个颜色的变体生成逻辑（增强后）")
        base_color = colors[0]
        result.append(base_color)  # 颜色1=原始颜色
        result.append(generate_specific_variant(base_color, "亮度-5%", debug_log))  # 颜色2=亮度调低5%
        result.append(generate_specific_variant(base_color, "饱和度-10%亮度+10%", debug_log))  # 颜色3=饱和度-10%，亮度+10%
        result.append(generate_specific_variant(base_color, "饱和度-5%亮度-10%", debug_log))  # 颜色4=饱和度-5%，亮度-10%
        result.append(generate_specific_variant(base_color, "亮度-15%", debug_log))  # 颜色5=亮度-15%
        
    elif remaining_count == 2:
        # b. 剩下2个颜色的情况
        debug_log.append(f"📋 应用2个颜色的变体生成逻辑（增强后）")
        color1, color2 = colors[0], colors[1]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(generate_specific_variant(color2, "亮度-5%", debug_log))  # 颜色4=第二个颜色亮度调低5%
        result.append(generate_specific_variant(color1, "亮度-15%", debug_log))  # 颜色5=第一个颜色亮度-15%
        
    elif remaining_count == 3:
        # c. 剩下3个颜色的情况
        debug_log.append(f"📋 应用3个颜色的变体生成逻辑（增强后）")
        color1, color2, color3 = colors[0], colors[1], colors[2]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(generate_specific_variant(color2, "亮度-5%", debug_log))  # 颜色4=第二个颜色亮度调低5%
        result.append(color3)  # 颜色5=第三个原始颜色
        
    elif remaining_count == 4:
        # d. 剩下4个颜色的情况
        debug_log.append(f"📋 应用4个颜色的变体生成逻辑（增强后）")
        color1, color2, color3, color4 = colors[0], colors[1], colors[2], colors[3]
        result.append(color1)  # 颜色1=第一个原始颜色
        result.append(generate_specific_variant(color1, "亮度-5%", debug_log))  # 颜色2=第一个颜色亮度调低5%
        result.append(color2)  # 颜色3=第二个原始颜色
        result.append(color3)  # 颜色4=第三个原始颜色
        result.append(color4)  # 颜色5=第四个原始颜色
    
    debug_log.append(f"✅ 变体生成完成，总计{len(result)}个颜色")
    return result[:target_count]



def apply_vivid_enhancement_and_print_compensation(color, params, debug_log):
    """应用高级鲜艳度调节和打印补偿（基于本地软件实现）"""
    enhanced_color = color.copy()
    
    r, g, b = color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_hex = color["hex"]
    original_s, original_v = s, v
    debug_log.append(f"    🎨 原始颜色: {original_hex} RGB({r},{g},{b}) HSV({h:.3f},{s:.3f},{v:.3f})")
    
    # 应用高级鲜艳度调节（如果启用）
    if params.get("vivid_enhancement", True):
        debug_log.append(f"    ✨ 应用高级鲜艳度调节方案:")
        
        # 亮度调整（基于本地软件算法）
        old_v = v
        if v < 0.45:  # 非常暗，显著提亮
            v = min(v * 1.25, 0.60)
            debug_log.append(f"      💡 亮度调整(低亮度): {old_v:.3f} → {v:.3f} (×1.25)")
        elif v < 0.65:  # 中暗，适度提亮
            v = min(v * 1.15, 0.75)
            debug_log.append(f"      💡 亮度调整(中亮度): {old_v:.3f} → {v:.3f} (×1.15)")
        else:  # 足够亮，不调亮
            debug_log.append(f"      💡 亮度保持: {v:.3f} (高亮度无需调整)")
        
        # 饱和度调整（基于本地软件算法）
        old_s = s
        if s < 0.35:  # 饱和度低，强提升
            s = min(s * 1.40, 0.55)
            debug_log.append(f"      🌈 饱和度调整(低饱和): {old_s:.3f} → {s:.3f} (×1.40)")
        elif s < 0.70:  # 中等饱和，适度提升
            s = min(s * 1.25, 0.85)
            debug_log.append(f"      🌈 饱和度调整(中饱和): {old_s:.3f} → {s:.3f} (×1.25)")
        else:  # 已高饱和，轻微提升
            s = min(s * 1.10, 1.00)
            debug_log.append(f"      🌈 饱和度调整(高饱和): {old_s:.3f} → {s:.3f} (×1.10)")
    else:
        debug_log.append(f"    ⏸️ 高级鲜艳度调节已禁用")
    
    # 应用打印补偿（如果启用）
    if params.get("print_compensation", True):
        preset = params.get("print_preset", "mid_cotton")
        debug_log.append(f"    🖨️ 应用数码直喷打印补偿 ({preset}):")
        
        # 打印补偿预设配置（基于本地软件）
        PRINT_PRESETS = {
            "light_cotton": {"sat_k": 1.10, "v_k": 0.99},
            "mid_cotton": {"sat_k": 1.18, "v_k": 0.98},
            "heavy_absorb": {"sat_k": 1.25, "v_k": 0.97},
        }
        
        if preset not in PRINT_PRESETS:
            preset = "mid_cotton"
        
        cfg = PRINT_PRESETS[preset]
        old_s, old_v = s, v
        
        # 饱和度补偿：仅对S<0.85的区间乘系数，防止纯色过饱和溢色
        if s < 0.85:
            s = min(s * cfg["sat_k"], 1.0)
        else:
            s = min(s * 1.05, 1.0)
        
        # 亮度补偿：整体乘系数
        v = max(min(v * cfg["v_k"], 1.0), 0.0)
        
        debug_log.append(f"      📄 {preset}补偿: 饱和度{old_s:.3f}→{s:.3f} 亮度{old_v:.3f}→{v:.3f}")
    else:
        debug_log.append(f"    ⏸️ 打印补偿已禁用")
    
    # 应用用户自定义调整
    saturation_adj = params.get("saturation_adjustment", 0) / 100.0
    lightness_adj = params.get("lightness_adjustment", 0) / 100.0
    
    if saturation_adj != 0 or lightness_adj != 0:
        debug_log.append(f"    ⚙️ 应用用户自定义调整:")
        old_s, old_v = s, v
        s = max(0, min(1, s + saturation_adj))
        v = max(0, min(1, v + lightness_adj))
        debug_log.append(f"      🎛️ 饱和度调整: {old_s:.3f} + {saturation_adj:.3f} = {s:.3f}")
        debug_log.append(f"      🎛️ 亮度调整: {old_v:.3f} + {lightness_adj:.3f} = {v:.3f}")
    else:
        debug_log.append(f"    ⚙️ 用户自定义调整: 无 (饱和度+{saturation_adj}, 亮度+{lightness_adj})")
    
    # 转换回RGB
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    enhanced_color["rgb"] = (new_r, new_g, new_b)
    enhanced_color["hex"] = new_hex
    enhanced_color["color_code"] = new_hex
    
    debug_log.append(f"    ✅ 增强完成: {original_hex} → {new_hex}")
    debug_log.append(f"    📊 变化总结: 饱和度{original_s:.3f}→{s:.3f} 亮度{original_v:.3f}→{v:.3f}")
    
    return enhanced_color

def generate_color_variant(base_color, variant_index, debug_log):
    """生成颜色变体"""
    r, g, b = base_color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    original_h, original_s, original_v = h, s, v
    variant_type = ""
    
    # 根据变体索引调整颜色
    if variant_index % 4 == 1:
        # 变体1：提高亮度
        v = min(v * 1.1, 1.0)
        variant_type = "提高亮度"
    elif variant_index % 4 == 2:
        # 变体2：降低饱和度
        s = max(s * 0.8, 0.0)
        variant_type = "降低饱和度"
    elif variant_index % 4 == 3:
        # 变体3：调整色相
        h = (h + 0.1) % 1.0
        variant_type = "调整色相"
    else:
        variant_type = "基础变体"
    
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    variant = base_color.copy()
    variant["rgb"] = (new_r, new_g, new_b)
    variant["hex"] = new_hex
    variant["color_code"] = new_hex
    variant["percentage"] = base_color["percentage"] * 0.5
    variant["label"] = base_color["label"] + "_variant"
    
    debug_log.append(f"    🔄 变体生成({variant_type}): {base_color['hex']} → {new_hex}")
    debug_log.append(f"      HSV变化: ({original_h:.3f},{original_s:.3f},{original_v:.3f}) → ({h:.3f},{s:.3f},{v:.3f})")
    
    return variant

def extract_colors_kmeans(image, n_colors):
    """使用K-means提取颜色"""
    try:
        # 将图片转换为numpy数组
        data = np.array(image)
        original_shape = data.shape
        data = data.reshape((-1, 3))

        # 记录原始像素数
        total_pixels = len(data)

        # 为了提高性能，对大图片进行采样
        if total_pixels > 100000:  # 如果像素数超过10万，进行采样
            sample_size = 50000  # 采样5万个像素
            indices = np.random.choice(total_pixels, sample_size, replace=False)
            data = data[indices]
            total_pixels = len(data)

        # 移除完全透明的像素（仅针对RGBA图片，但这里是RGB所以不需要）
        # 注意：不要移除黑色像素，因为黑色是有效颜色

        # 确保有足够的数据进行聚类
        if total_pixels < n_colors:
            n_colors = max(1, total_pixels)

        # 使用K-means聚类
        kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10, max_iter=100)
        kmeans.fit(data)

        # 获取聚类中心（颜色）
        colors = kmeans.cluster_centers_.astype(int)

        # 计算每个颜色的像素数量
        labels = kmeans.labels_
        counts = np.bincount(labels)

        return colors, counts, total_pixels

    except Exception as e:
        # 如果K-means失败，尝试简单的颜色提取
        try:
            # 将图片转换为numpy数组
            data = np.array(image)
            data = data.reshape((-1, 3))
            total_pixels = len(data)

            # 简单的颜色统计方法
            unique_colors, counts = np.unique(data, axis=0, return_counts=True)

            # 按出现频率排序
            sorted_indices = np.argsort(counts)[::-1]
            top_colors = unique_colors[sorted_indices[:n_colors]]
            top_counts = counts[sorted_indices[:n_colors]]

            return top_colors, top_counts, total_pixels

        except Exception as e2:
            # 最后的备用方案
            basic_colors = np.array([[128, 128, 128], [255, 255, 255], [64, 64, 64]])
            basic_counts = np.array([100, 100, 100])
            return basic_colors, basic_counts, 300

def recognize_image_colors(params):
    """识别图片颜色（基于本地软件完整实现）"""
    try:
        # 检查依赖库是否可用
        if not DEPENDENCIES_AVAILABLE:
            return {"status": "error", "message": f"缺少必要的库: {IMPORT_ERROR}"}

        image_path = params["image_path"]
        color_count = params.get("color_count", 5)
        verbose_mode = params.get("verbose_mode", False)
        
        debug_log = []
        debug_log.append(f"🎨 Color Science颜色识别开始（基于本地软件完整实现）")
        debug_log.append(f"📁 图片路径: {image_path}")
        debug_log.append(f"🔢 目标颜色数量: {color_count}")
        debug_log.append(f"📊 配置参数: {params}")
        
        # 检查图片文件
        if not os.path.exists(image_path):
            return {"status": "error", "message": f"图片文件不存在: {image_path}"}

        # 加载图片
        try:
            image = Image.open(image_path)
            original_mode = image.mode
            original_size = image.size

            # 正确处理透明背景（基于原始svg_processor_gui.py实现）
            if image.mode in ('RGBA', 'LA'):
                debug_log.append(f"🔍 检测到透明图片格式: {image.mode}，正在处理透明背景")
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
                debug_log.append(f"✅ 透明背景已处理，转换为白色背景")
            elif image.mode != 'RGB':
                image = image.convert('RGB')
                debug_log.append(f"🔄 图片模式转换: {original_mode} → RGB")

            debug_log.append(f"✅ 图片加载成功，原始尺寸: {original_size}, 原始模式: {original_mode}, 转换后模式: {image.mode}")

            # 检查图片是否过大，如果是则缩放以提高处理速度
            max_size = 800  # 最大尺寸
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                debug_log.append(f"📏 图片过大，已缩放至: {new_size}")

        except Exception as e:
            return {"status": "error", "message": f"图片加载失败: {e}"}

        # 提取颜色 - 使用本地软件的算法（固定8个颜色进行聚类）
        cluster_count = 8  # 本地软件默认提取8个颜色
        debug_log.append(f"🔍 开始K-means聚类，聚类数量: {cluster_count}（本地软件默认8个）")
        colors, counts, total_pixels = extract_colors_kmeans(image, cluster_count)
        debug_log.append(f"📈 聚类完成，总像素数: {total_pixels}")

        # 创建颜色信息列表
        color_info_list = []
        debug_log.append(f"🎯 开始分析提取的颜色:")
        for i, (color, count) in enumerate(zip(colors, counts)):
            percentage = (count / total_pixels) * 100
            hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"

            # 使用Color Science分类（带调试信息）
            debug_log.append(f"  🔬 分析颜色{i+1}: {hex_color} - 占比{percentage:.1f}%")
            if verbose_mode:
                label = classify_color_science_with_debug(tuple(color), debug_log)
            else:
                label = classify_color_science(tuple(color))

            color_info = {
                "color_code": hex_color,
                "hex": hex_color,
                "rgb": tuple(int(c) for c in color),
                "percentage": percentage,
                "label": label,
                "colour_science": label
            }
            color_info_list.append(color_info)
            debug_log.append(f"  ✅ 颜色{i+1}: {hex_color} → {label} - 占比{percentage:.1f}%")

        # 按占比排序
        color_info_list.sort(key=lambda x: x["percentage"], reverse=True)
        debug_log.append(f"📊 颜色按占比排序完成")

        # 应用Color Science处理逻辑（基于本地软件真实实现）
        debug_log.append(f"🧪 开始应用Color Science处理逻辑（本地软件算法）")
        processed_colors = apply_color_science_processing_authentic(color_info_list, color_count, params, debug_log)

        debug_log.append(f"✅ Color Science颜色识别完成，最终输出{len(processed_colors)}个颜色")
        
        return {
            "status": "success",
            "image_path": image_path,
            "colors": processed_colors,
            "debug_log": debug_log if verbose_mode else [],
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        return {"status": "error", "message": f"识别失败: {e}"}

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"status": "error", "message": "用法: python color_science_recognizer.py <params_file>"}))
        sys.exit(1)
    
    params_file = sys.argv[1]
    
    try:
        with open(params_file, 'r', encoding='utf-8') as f:
            params = json.load(f)
        
        result = recognize_image_colors(params)
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        print(json.dumps({"status": "error", "message": f"参数解析失败: {e}"}, ensure_ascii=False))