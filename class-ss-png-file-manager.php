<?php
/**
 * PNG文件管理器类
 * 
 * 负责管理PNG文件的扫描、记录和处理状态跟踪
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SS_PNG_File_Manager {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * PNG文件根目录
     */
    private $png_root_dir;
    
    /**
     * JSON记录文件目录
     */
    private $json_records_dir;
    
    /**
     * 构造函数
     */
    private function __construct() {
        // PNG文件根目录
        $this->png_root_dir = '/www/wwwroot/printeeque.com/wp-content/uploads/png/';
        
        // JSON记录目录 - 使用WordPress函数前先检查是否存在
        if (function_exists('wp_upload_dir')) {
            $upload_dir = wp_upload_dir();
            $this->json_records_dir = $upload_dir['basedir'] . '/shoe-svg-generator/png-records/';
            
            // 确保JSON记录目录存在
            if (!file_exists($this->json_records_dir)) {
                if (function_exists('wp_mkdir_p')) {
                    wp_mkdir_p($this->json_records_dir);
                } else {
                    // 手动创建目录
                    if (!is_dir($this->json_records_dir)) {
                        mkdir($this->json_records_dir, 0755, true);
                    }
                }
            }
        } else {
            // WordPress环境不可用时的回退路径
            $this->json_records_dir = '/www/wwwroot/printeeque.com/wp-content/uploads/shoe-svg-generator/png-records/';
            if (!is_dir($this->json_records_dir)) {
                mkdir($this->json_records_dir, 0755, true);
            }
        }
        
        error_log('[PNG文件管理器] 初始化完成，PNG根目录: ' . $this->png_root_dir);
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 扫描PNG目录结构（支持多层级子文件夹）
     *
     * @return array 目录结构信息
     */
    public function scan_png_directory_structure() {
        $structure = array();

        if (!is_dir($this->png_root_dir)) {
            error_log('[PNG文件管理器] PNG根目录不存在: ' . $this->png_root_dir);
            return $structure;
        }

        // 递归扫描所有子文件夹
        $this->scan_directory_recursive($this->png_root_dir, '', $structure);

        error_log('[PNG文件管理器] 扫描完成，发现 ' . count($structure) . ' 个有效文件夹');
        return $structure;
    }

    /**
     * 递归扫描目录结构
     *
     * @param string $base_dir 基础目录
     * @param string $relative_path 相对路径
     * @param array &$structure 结构数组引用
     */
    private function scan_directory_recursive($base_dir, $relative_path, &$structure) {
        $current_dir = $base_dir . $relative_path;

        if (!is_dir($current_dir)) {
            return;
        }

        $subdirs = glob($current_dir . '*', GLOB_ONLYDIR);

        foreach ($subdirs as $subdir) {
            $folder_name = basename($subdir);
            $new_relative_path = $relative_path . $folder_name . '/';
            $folder_key = trim($relative_path . $folder_name, '/');

            // 检查当前文件夹是否包含PNG文件
            $png_files = glob($subdir . '/*.png');

            if (!empty($png_files)) {
                $structure[$folder_key] = array(
                    'folder_path' => $subdir,
                    'folder_name' => $folder_name,
                    'relative_path' => $folder_key,
                    'parent_path' => trim($relative_path, '/'),
                    'png_files' => array_map('basename', $png_files),
                    'file_count' => count($png_files)
                );

                error_log('[PNG文件管理器] 发现文件夹: ' . $folder_key . '，包含 ' . count($png_files) . ' 个PNG文件');
            }

            // 递归扫描子文件夹
            $this->scan_directory_recursive($base_dir, $new_relative_path, $structure);
        }
    }
    
    /**
     * 获取或创建产品类目（支持层级结构）
     *
     * @param string $folder_path 文件夹路径（如 "test/test1"）
     * @return int|false 类目ID或false
     */
    public function get_or_create_category($folder_path) {
        error_log('[PNG文件管理器] 开始处理类目路径: ' . $folder_path);

        // 分割路径为层级
        $path_parts = explode('/', trim($folder_path, '/'));
        $parent_id = 0;
        $current_path = '';

        foreach ($path_parts as $part) {
            $current_path .= ($current_path ? '/' : '') . $part;
            $category_name = ucfirst(strtolower($part));

            error_log('[PNG文件管理器] 处理类目层级: ' . $category_name . '，父类目ID: ' . $parent_id);

            // 检查类目是否已存在（考虑父类目）
            $existing_term = $this->find_category_by_name_and_parent($category_name, $parent_id);

            if ($existing_term) {
                error_log('[PNG文件管理器] 类目已存在: ' . $category_name . ' (ID: ' . $existing_term->term_id . ')');
                $parent_id = $existing_term->term_id;
                continue;
            }

            // 创建新类目
            $term_args = array(
                'slug' => sanitize_title($current_path)
            );

            if ($parent_id > 0) {
                $term_args['parent'] = $parent_id;
            }

            $term_result = wp_insert_term(
                $category_name,
                'product_cat',
                $term_args
            );

            if (is_wp_error($term_result)) {
                error_log('[PNG文件管理器] 创建类目失败: ' . $term_result->get_error_message());
                return false;
            }

            $parent_id = $term_result['term_id'];
            error_log('[PNG文件管理器] 成功创建类目: ' . $category_name . ' (ID: ' . $parent_id . ')，父类目: ' . ($term_args['parent'] ?? '无'));
        }

        return $parent_id;
    }

    /**
     * 根据名称和父类目查找类目
     *
     * @param string $name 类目名称
     * @param int $parent_id 父类目ID
     * @return object|false 类目对象或false
     */
    private function find_category_by_name_and_parent($name, $parent_id = 0) {
        $terms = get_terms(array(
            'taxonomy' => 'product_cat',
            'name' => $name,
            'parent' => $parent_id,
            'hide_empty' => false,
            'number' => 1
        ));

        if (!empty($terms) && !is_wp_error($terms)) {
            return $terms[0];
        }

        return false;
    }
    
    /**
     * 生成文件夹的JSON记录（支持多模板和多层级路径）
     *
     * @param string $folder_path 文件夹路径（可以是相对路径如 test/test1 或简单名称如 test1）
     * @param int $category_id 类目ID
     * @param int $template_id 模板产品ID（可选）
     * @return array 操作结果
     */
    public function generate_folder_json_record($folder_path, $category_id, $template_id = null) {
        // 构建完整的文件夹路径
        $full_folder_path = $this->png_root_dir . $folder_path;

        // 获取文件夹名称（用于JSON文件命名）
        $folder_name = basename($folder_path);

        error_log('[PNG文件管理器] 生成JSON记录 - 输入路径: ' . $folder_path . ', 完整路径: ' . $full_folder_path . ', 文件夹名: ' . $folder_name);

        if (!is_dir($full_folder_path)) {
            error_log('[PNG文件管理器] 文件夹不存在: ' . $full_folder_path);
            return array('success' => false, 'message' => '文件夹不存在: ' . $folder_path . ' (完整路径: ' . $full_folder_path . ')');
        }

        // 获取PNG文件列表
        $png_files = glob($full_folder_path . '/*.png');
        $files_data = array();
        $processed_files = array(); // 防止重复文件

        error_log('[PNG文件管理器] 扫描PNG文件: ' . $full_folder_path . '/*.png，找到 ' . count($png_files) . ' 个文件');

        foreach ($png_files as $file_path) {
            $filename = basename($file_path);

            // 【修复】防止重复文件
            if (in_array($filename, $processed_files)) {
                error_log('[PNG文件管理器] 跳过重复文件: ' . $filename);
                continue;
            }

            $processed_files[] = $filename;
            $files_data[] = array(
                'filename' => $filename,
                'file_path' => $file_path,
                'file_size' => filesize($file_path),
                'created_time' => filemtime($file_path),
                'templates' => array(), // 支持多个模板的处理记录
                'relative_path' => str_replace($this->png_root_dir, '', $file_path) // 存储相对路径
            );

            error_log('[PNG文件管理器] 添加文件到记录: ' . $filename . ' -> ' . $file_path);
        }

        // 生成JSON记录数据
        $json_data = array(
            'folder_name' => $folder_name,
            'folder_path' => $folder_path, // 添加完整的相对路径
            'category_id' => $category_id,
            'category_name' => get_term($category_id, 'product_cat')->name,
            'total_files' => count($files_data),
            'generated_time' => current_time('mysql'),
            'last_updated' => current_time('mysql'),
            'files' => $files_data
        );

        // 保存JSON文件（使用文件夹名称避免路径分隔符问题）
        $safe_folder_name = str_replace('/', '_', $folder_path); // 将路径分隔符替换为下划线
        $json_filename = "png_folder_{$safe_folder_name}_category_{$category_id}.json";
        $json_filepath = $this->json_records_dir . $json_filename;

        error_log('[PNG文件管理器] 准备保存JSON文件: ' . $json_filepath);

        if (file_put_contents($json_filepath, json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            error_log('[PNG文件管理器] 成功生成JSON记录: ' . $json_filename . '，包含 ' . count($files_data) . ' 个文件');
            return array(
                'success' => true,
                'message' => '成功生成JSON记录，包含 ' . count($files_data) . ' 个文件',
                'json_file' => $json_filepath,
                'files_count' => count($files_data)
            );
        } else {
            error_log('[PNG文件管理器] 保存JSON记录失败: ' . $json_filepath);
            return array('success' => false, 'message' => '保存JSON记录失败: ' . $json_filepath);
        }
    }
    
    /**
     * 获取文件夹的JSON记录（支持多层级路径）
     *
     * @param string $folder_path 文件夹路径（可以是相对路径如 test/test1 或简单名称如 test1）
     * @param int $category_id 类目ID
     * @return array|false JSON数据或false
     */
    public function get_folder_json_record($folder_path, $category_id) {
        // 尝试新的命名方式（支持多层级路径）
        $safe_folder_name = str_replace('/', '_', $folder_path);
        $json_filename = "png_folder_{$safe_folder_name}_category_{$category_id}.json";
        $json_filepath = $this->json_records_dir . $json_filename;

        if (file_exists($json_filepath)) {
            $json_content = file_get_contents($json_filepath);
            return json_decode($json_content, true);
        }

        // 如果新命名方式的文件不存在，尝试旧的命名方式（向后兼容）
        $folder_name = basename($folder_path);
        $old_json_filename = "png_folder_{$folder_name}_category_{$category_id}.json";
        $old_json_filepath = $this->json_records_dir . $old_json_filename;

        if (file_exists($old_json_filepath)) {
            error_log('[PNG文件管理器] 使用旧格式JSON记录: ' . $old_json_filename);
            $json_content = file_get_contents($old_json_filepath);
            return json_decode($json_content, true);
        }

        return false;
    }
    
    /**
     * 更新文件处理状态（支持多模板和多层级路径）
     *
     * @param string $folder_path 文件夹路径（可以是相对路径如 test/test1 或简单名称如 test1）
     * @param int $category_id 类目ID
     * @param string $filename 文件名
     * @param int $template_id 模板产品ID
     * @param int $product_id 产品ID
     * @return bool 更新是否成功
     */
    public function update_file_processed_status($folder_path, $category_id, $filename, $template_id, $product_id) {
        error_log('[PNG文件管理器] 更新文件处理状态: ' . $filename . ', 文件夹路径: ' . $folder_path . ', 模板ID: ' . $template_id . ', 产品ID: ' . $product_id);

        $json_record = $this->get_folder_json_record($folder_path, $category_id);

        if (!$json_record) {
            error_log('[PNG文件管理器] 未找到JSON记录: ' . $folder_path);
            return false;
        }

        // 更新文件状态
        $updated = false;
        foreach ($json_record['files'] as &$file_info) {
            if ($file_info['filename'] === $filename) {
                // 初始化templates数组（如果不存在）
                if (!isset($file_info['templates'])) {
                    $file_info['templates'] = array();
                }

                // 添加或更新模板处理记录
                $file_info['templates'][$template_id] = array(
                    'product_id' => $product_id,
                    'processed_time' => current_time('mysql'),
                    'template_name' => get_the_title($template_id)
                );

                error_log('[PNG文件管理器] 为文件 ' . $filename . ' 添加模板 ' . $template_id . ' 的处理记录');
                $updated = true;
                break;
            }
        }

        if ($updated) {
            $json_record['last_updated'] = current_time('mysql');

            // 保存更新后的JSON记录（使用新的命名方式）
            $safe_folder_name = str_replace('/', '_', $folder_path);
            $json_filename = "png_folder_{$safe_folder_name}_category_{$category_id}.json";
            $json_filepath = $this->json_records_dir . $json_filename;

            if (file_put_contents($json_filepath, json_encode($json_record, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
                error_log('[PNG文件管理器] 成功更新文件处理状态: ' . $filename . ' (模板: ' . $template_id . ')');
                return true;
            }
        }

        return false;
    }
    
    /**
     * 检查文件是否已处理（支持多模板）
     *
     * @param string $folder_name 文件夹名称
     * @param int $category_id 类目ID
     * @param string $filename 文件名
     * @param int $template_id 模板产品ID（可选）
     * @return bool 是否已处理
     */
    public function is_file_processed($folder_name, $category_id, $filename, $template_id = null) {
        $json_record = $this->get_folder_json_record($folder_name, $category_id);

        if (!$json_record) {
            return false;
        }

        foreach ($json_record['files'] as $file_info) {
            if ($file_info['filename'] === $filename) {
                // 如果指定了模板ID，检查该模板是否已处理
                if ($template_id !== null) {
                    if (isset($file_info['templates']) && isset($file_info['templates'][$template_id])) {
                        error_log('[PNG文件管理器] 文件 ' . $filename . ' 在模板 ' . $template_id . ' 下已处理');
                        return true;
                    }
                    return false;
                } else {
                    // 兼容旧版本：检查是否有processed字段
                    if (isset($file_info['processed'])) {
                        return $file_info['processed'];
                    }
                    // 新版本：检查是否有任何模板处理记录
                    return isset($file_info['templates']) && !empty($file_info['templates']);
                }
            }
        }

        return false;
    }
    
    /**
     * 获取未处理的文件列表（支持多模板和多层级路径，增强调试）
     *
     * @param string $folder_path 文件夹路径（可以是相对路径如 test/test1 或简单名称如 test1）
     * @param int $category_id 类目ID
     * @param int $template_id 模板产品ID（可选）
     * @return array 未处理的文件列表
     */
    public function get_unprocessed_files($folder_path, $category_id, $template_id = null) {
        error_log('[PNG文件管理器][关键修复] 获取未处理文件 - 文件夹路径: ' . $folder_path . ', 类目ID: ' . $category_id . ', 模板ID: ' . $template_id);

        // 直接基于实际文件系统操作
        $full_folder_path = $this->png_root_dir . $folder_path;
        
        if (!is_dir($full_folder_path)) {
            error_log('[PNG文件管理器][修复] 文件夹不存在: ' . $full_folder_path);
            return array();
        }

        // 获取实际存在的PNG文件（彻底解决369个文件被误判的问题）
        $real_png_files = glob($full_folder_path . '/*.png');
        
        if (empty($real_png_files)) {
            error_log('[PNG文件管理器][修复] 文件夹中无PNG文件: ' . $full_folder_path);
            return array();
        }

        // 提取实际文件名
        $real_files = array();
        foreach ($real_png_files as $file_path) {
            $real_files[basename($file_path)] = $file_path;
        }

        $real_count = count($real_files);
        error_log('[PNG文件管理器][修复] 实际发现 ' . $real_count . ' 个PNG文件');

        // 检查JSON记录的处理状态
        $json_record = $this->get_folder_json_record($folder_path, $category_id);
        $processed_files = array();
        
        if ($json_record) {
            error_log('[PNG文件管理器][修复] 检查JSON记录处理状态...');
            
            foreach ($json_record['files'] as $file_info) {
                $filename = $file_info['filename'];
                
                // 仅处理实际存在的文件
                if (!isset($real_files[$filename])) {
                    continue; // 跳过不存在的文件
                }
                
                // 检查该文件的处理状态
                $is_processed = false;
                if ($template_id !== null) {
                    $is_processed = isset($file_info['templates'][$template_id]);
                } else {
                    $is_processed = isset($file_info['templates']) && !empty($file_info['templates']);
                }
                
                if ($is_processed) {
                    $processed_files[$filename] = true;
                }
            }
        } else {
            error_log('[PNG文件管理器][修复] 无JSON记录，所有文件待处理');
        }

        // 构建基于真实文件的处理列表
        $unprocessed_files = array();
        $processed_count = count($processed_files);
        
        foreach ($real_files as $filename => $file_path) {
            if (!isset($processed_files[$filename])) {
                $unprocessed_files[] = array(
                    'filename' => $filename,
                    'file_path' => $file_path,
                    'file_size' => filesize($file_path),
                    'created_time' => filemtime($file_path),
                    'templates' => array()
                );
            }
        }

        error_log('[PNG文件管理器][🎯 最终修复结果] ' . 
                  '实际文件: ' . $real_count . 
                  ', 已处理: ' . $processed_count . 
                  ', 待处理: ' . count($unprocessed_files) . 
                  ' (模板ID: ' . $template_id . ')');

        return $unprocessed_files;
    }
    
    /**
     * 检查文件夹是否已全部处理完成（针对特定模板）
     *
     * @param string $folder_path 文件夹路径
     * @param int $category_id 类目ID
     * @param int $template_id 模板产品ID
     * @return bool 是否已全部处理
     */
    public function is_folder_fully_processed($folder_path, $category_id, $template_id) {
        $json_record = $this->get_folder_json_record($folder_path, $category_id);
        
        if (!$json_record) {
            return false;
        }
        
        if (empty($json_record['files'])) {
            return true; // 空文件夹视为已处理
        }
        
        $total_files = count($json_record['files']);
        $processed_files = 0;
        
        foreach ($json_record['files'] as $file_info) {
            if (isset($file_info['templates'][$template_id])) {
                $processed_files++;
            }
        }
        
        // 记录调试信息
        error_log('[PNG文件管理器] 文件夹处理状态检查: ' . $folder_path . 
                  ' - 总文件: ' . $total_files . 
                  ' - 已处理: ' . $processed_files . 
                  ' - 模板ID: ' . $template_id);
        
        return ($processed_files === $total_files); // 所有文件都已处理
    }
    
    /**
     * 获取未处理文件的数量（更高效的方法）
     *
     * @param string $folder_path 文件夹路径
     * @param int $category_id 类目ID
     * @param int $template_id 模板产品ID
     * @return int 未处理文件数量
     */
    public function get_unprocessed_files_count($folder_path, $category_id, $template_id) {
        $json_record = $this->get_folder_json_record($folder_path, $category_id);
        
        if (!$json_record || empty($json_record['files'])) {
            return 0;
        }
        
        $unprocessed_count = 0;
        $processed_count = 0;
        
        foreach ($json_record['files'] as $file_info) {
            if (!isset($file_info['templates'][$template_id])) {
                $unprocessed_count++;
            } else {
                $processed_count++;
            }
        }
        
        return $unprocessed_count;
    }
    
    /**
     * 删除文件夹的JSON记录
     * 
     * @param string $folder_name 文件夹名称
     * @param int $category_id 类目ID
     * @return bool 删除是否成功
     */
    public function delete_folder_json_record($folder_name, $category_id) {
        $json_filename = "png_folder_{$folder_name}_category_{$category_id}.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        if (file_exists($json_filepath)) {
            if (unlink($json_filepath)) {
                error_log('[PNG文件管理器] 成功删除JSON记录: ' . $json_filename);
                return true;
            }
        }
        
        return false;
    }
}
