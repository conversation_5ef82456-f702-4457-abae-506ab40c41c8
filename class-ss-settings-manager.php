<?php
/**
 * 设置管理器类
 * 
 * 管理插件设置，包括并行任务数等
 * 
 * @package Shoe_SVG_Generator
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

class SS_Settings_Manager {
    /**
     * 单例实例
     *
     * @var SS_Settings_Manager
     */
    private static $instance = null;

    /**
     * 设置页面标识
     *
     * @var string
     */
    private $page = 'ss-settings';

    /**
     * 设置组标识
     *
     * @var string
     */
    private $option_group = 'ss_settings_group';

    /**
     * 获取单例实例
     *
     * @return SS_Settings_Manager
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        // 添加设置页面
        add_action('admin_menu', array($this, 'add_settings_page'));

        // 注册设置
        add_action('admin_init', array($this, 'register_settings'));

        // 【废弃项 - 已移除】并发处理相关的 AJAX 处理
    }

    /**
     * 添加设置页面
     */
    public function add_settings_page() {
        add_submenu_page(
            'ss-svg-library',
            '设置',
            '设置',
            'manage_options',
            $this->page,
            array($this, 'render_settings_page')
        );
    }

    /**
     * 注册设置
     */
    public function register_settings() {
        // 注册设置组
        register_setting(
            $this->option_group,
            'ss_max_concurrent_preview_tasks',
            array(
                'type' => 'integer',
                'sanitize_callback' => array($this, 'sanitize_positive_int'),
                'default' => 10
            )
        );

        // 【废弃注册项 - 已移除】并发处理相关设置

        // 仅添加基础设置区域（移除废弃的性能设置）
        add_settings_section(
            'ss_performance_settings',
            '基础设置',
            array($this, 'render_performance_section'),
            $this->page
        );

        // 仅保留基础设置字段
        add_settings_field(
            'ss_max_concurrent_preview_tasks',
            '最大并行任务数',
            array($this, 'render_max_tasks_field'),
            $this->page,
            'ss_performance_settings'
        );

        // 【双色排列方案】新增颜色排列设置 - 修复空白页问题
        register_setting(
            $this->option_group,
            'ss_enable_dual_color_shuffle',
            array(
                'type' => 'boolean',
                'sanitize_callback' => array($this, 'sanitize_checkbox'),
                'default' => true // 【修复】默认开启双色排列
            )
        );

        register_setting(
            $this->option_group,
            'ss_dual_shuffle_probability',
            array(
                'type' => 'integer',
                'sanitize_callback' => 'absint',
                'default' => 50
            )
        );

        // 添加颜色设置新区域
        add_settings_section(
            'ss_color_settings',
            '颜色排列设置',
            array($this, 'render_color_section'),
            $this->page
        );

        add_settings_field(
            'ss_enable_dual_color_shuffle',
            '启用双色排列',
            array($this, 'render_dual_color_shuffle_field'),
            $this->page,
            'ss_color_settings'
        );

        add_settings_field(
            'ss_dual_shuffle_probability',
            '双色排列概率',
            array($this, 'render_dual_shuffle_probability_field'),
            $this->page,
            'ss_color_settings'
        );
    }

    /**
     * 渲染设置页面
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>SVG生成器设置</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields($this->option_group);
                do_settings_sections($this->page);
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * 渲染基础设置区域（已移除废弃的性能设置）
     */
    public function render_performance_section() {
        echo '<p>配置SVG生成的基础参数。双色排列功能已内置启用，可产生更多颜色变化。</p>';
    }

    /**
     * 渲染最大并行任务数字段
     */
    public function render_max_tasks_field() {
        $value = get_option('ss_max_concurrent_preview_tasks', 12);
        ?>
        <input type="number" name="ss_max_concurrent_preview_tasks" value="<?php echo esc_attr($value); ?>" min="1" max="50" />
        <p class="description">设置同时处理的最大SVG预览图生成任务数。根据服务器性能调整，建议值：5-20。</p>
        <?php
    }

    /**
     * 验证最大任务数
     *
     * @param mixed $value 输入值
     * @return int 验证后的值
     */
    public function sanitize_max_tasks($value) {
        $value = intval($value);
        
        // 确保值在合理范围内
        if ($value < 1) {
            $value = 1;
        } elseif ($value > 50) {
            $value = 50;
        }
        
        // 更新任务管理器的最大并行任务数
        $task_manager = SS_Preview_Task_Manager::get_instance();
        $task_manager->set_max_concurrent_tasks($value);
        
        return $value;
    }

    /**
     * 获取设置值
     *
     * @param string $option_name 选项名称
     * @param mixed $default 默认值
     * @return mixed 设置值
     */
    public function get_setting($option_name, $default = null) {
        return get_option($option_name, $default);
    }

    // 【废弃方法 - 已移除】render_enable_concurrent_field

    // 【废弃方法 - 已移除】render_max_workers_field

    // 【废弃方法 - 已移除】render_batch_size_field

    // 【废弃方法 - 已移除】render_system_info_field

    /**
     * 验证正整数
     */
    public function sanitize_positive_int($value) {
        return max(1, intval($value));
    }

    /**
     * 验证复选框值
     */
    public function sanitize_checkbox($value) {
        return isset($value) ? (bool)$value : false;
    }

    // 【双色排列方案】部分开始

    /**
     * 渲染颜色设置区域
     */
    public function render_color_section() {
        echo '<p>配置SVG颜色排列和双色排列功能。开启双色排列后，每个类目将随机使用两种颜色排列方案，增加颜色多样性。</p>';
    }

    /**
     * 渲染双色排列启用开关
     */
    public function render_dual_color_shuffle_field() {
        $value = get_option('ss_enable_dual_color_shuffle', true); // 明确设置为true
        ?>
        <input type="checkbox" name="ss_enable_dual_color_shuffle" value="1" <?php checked($value, true); ?> />
        <p class="description">启用双色排列功能后，系统将随机选择50%概率使用A或B两种颜色排列方案</p>
        <?php
    }

    /**
     * 渲染双色排列概率设置
     */
    public function render_dual_shuffle_probability_field() {
        $value = get_option('ss_dual_shuffle_probability', 50);
        ?>
        <input type="number" name="ss_dual_shuffle_probability" value="<?php echo esc_attr($value); ?>" min="10" max="90" step="10" />
        <p class="description">使用双色排列B方案的概率百分比（默认50%，设置10-90之间）</p>
        <?php
    }

    // 【双色排列方案】部分结束

}
