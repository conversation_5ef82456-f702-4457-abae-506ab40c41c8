<?php
/**
 * 前端管理类
 *
 * 处理前端功能，包括检测空类目、提取颜色和显示SVG设计列表
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class SS_Frontend_Manager {
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 颜色提取缓存
     */
    private $color_cache = array();

    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    public function __construct() {
        // 优化PHP设置以处理大型SVG文件
        $this->optimize_php_settings_for_svg();

        // 确保Color Science颜色识别器已加载
        if (!class_exists('SS_Color_Science_Recognizer')) {
            $plugin_dir = function_exists('plugin_dir_path') ? plugin_dir_path(__FILE__) : dirname(__FILE__) . '/';
            require_once $plugin_dir . 'class-ss-color-science-recognizer.php';
        }

        // 初始化钩子
        $this->init_hooks();

        error_log('【前端】SS_Frontend_Manager 初始化完成，已加载 Color Science 颜色识别器');
    }

    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 修改产品分类页面模板
        add_filter('template_include', array($this, 'ss_maybe_load_custom_template'), 99);

        // 注册前端脚本和样式
        add_action('wp_enqueue_scripts', array($this, 'ss_enqueue_frontend_scripts'));

        // AJAX处理函数
        add_action('wp_ajax_ss_load_more_designs', array($this, 'ss_load_more_designs'));
        add_action('wp_ajax_nopriv_ss_load_more_designs', array($this, 'ss_load_more_designs'));
        
        // 【新增】缓存预览图相关AJAX处理
        add_action('wp_ajax_ss_get_cached_previews', array($this, 'ss_get_cached_previews'));
        add_action('wp_ajax_nopriv_ss_get_cached_previews', array($this, 'ss_get_cached_previews'));

        // 添加临时数据清理计划任务
        add_action('ss_cleanup_temp_data', array($this, 'ss_cleanup_temp_data'));

        // 确保计划任务已注册
        if (!wp_next_scheduled('ss_cleanup_temp_data')) {
            wp_schedule_event(time(), 'hourly', 'ss_cleanup_temp_data');
        }

        // 添加检测类目模板的钩子
        add_action('template_redirect', array($this, 'ss_check_category_templates'));

        // 为SVG生成添加AJAX处理函数
        add_action('wp_ajax_ss_generate_svg_preview', array($this, 'ss_generate_svg_preview'));
        add_action('wp_ajax_nopriv_ss_generate_svg_preview', array($this, 'ss_generate_svg_preview'));

        // 添加SVG预览缓存清理
        add_action('ss_cleanup_svg_previews', array($this, 'ss_cleanup_old_previews'));
        if (!wp_next_scheduled('ss_cleanup_svg_previews')) {
            wp_schedule_event(time(), 'daily', 'ss_cleanup_svg_previews');
        }

        // 添加单张图片生成的AJAX处理函数
        add_action('wp_ajax_ss_generate_single_preview', array($this, 'ss_generate_single_preview'));
        add_action('wp_ajax_nopriv_ss_generate_single_preview', array($this, 'ss_generate_single_preview'));

        // 添加批量获取预览的AJAX处理
        add_action('wp_ajax_ss_get_preview_batch', array($this, 'ss_get_preview_batch'));
        add_action('wp_ajax_nopriv_ss_get_preview_batch', array($this, 'ss_get_preview_batch'));

        // 添加交互跟踪事件
        add_action('wp_ajax_ss_track_interaction', array($this, 'ss_track_interaction'));
        add_action('wp_ajax_nopriv_ss_track_interaction', array($this, 'ss_track_interaction'));

        // 获取预览状态
        add_action('wp_ajax_ss_check_previews_status', array($this, 'ss_check_previews_status'));
        add_action('wp_ajax_nopriv_ss_check_previews_status', array($this, 'ss_check_previews_status'));

        // 处理生成预览队列
        add_action('ss_process_preview_queue', array($this, 'ss_process_preview_queue'));

        // 添加自定义cron间隔
        add_filter('cron_schedules', array($this, 'ss_add_cron_interval'));

        // 重新生成预览
        add_action('wp_ajax_ss_regenerate_preview', array($this, 'ss_regenerate_preview'));
        add_action('wp_ajax_nopriv_ss_regenerate_preview', array($this, 'ss_regenerate_preview'));

        // 添加预览生成处理钩子
        add_action('ss_generate_preview_async', array($this, 'ss_generate_preview_async'), 10, 4);

        // 添加实时进度推送相关钩子
        add_action('wp_ajax_ss_progress_stream', array($this, 'ss_progress_stream'));
        add_action('wp_ajax_nopriv_ss_progress_stream', array($this, 'ss_progress_stream'));
        add_action('wp_ajax_ss_get_progress', array($this, 'ss_get_progress'));
        add_action('wp_ajax_nopriv_ss_get_progress', array($this, 'ss_get_progress'));

        // 添加并发处理启动钩子
        add_action('wp_ajax_ss_start_concurrent_processing', array($this, 'ss_start_concurrent_processing'));
        add_action('wp_ajax_nopriv_ss_start_concurrent_processing', array($this, 'ss_start_concurrent_processing'));

        // 添加后台并发处理钩子
        add_action('ss_process_concurrent_batch', array($this, 'ss_process_concurrent_batch'), 10, 3);
        add_action('ss_process_concurrent_preview_batch', array($this, 'ss_process_concurrent_preview_batch'), 10, 2);

        // 【新增】添加异步处理AJAX钩子
        add_action('wp_ajax_ss_async_process_preview_batch', array($this, 'ss_async_process_preview_batch'));
        add_action('wp_ajax_nopriv_ss_async_process_preview_batch', array($this, 'ss_async_process_preview_batch'));

        // 新增：获取类目模板列表的AJAX处理函数
        add_action('wp_ajax_ss_get_category_templates', array($this, 'ss_get_category_templates'));
        add_action('wp_ajax_nopriv_ss_get_category_templates', array($this, 'ss_get_category_templates'));

        // 新增：根据SVG和模板生成单个产品的AJAX处理函数
        add_action('wp_ajax_ss_generate_svg_product', array($this, 'ss_generate_svg_product'));
        add_action('wp_ajax_nopriv_ss_generate_svg_product', array($this, 'ss_generate_svg_product'));

        // 新增：检查SVG和模板组合是否已生成产品的AJAX处理函数
        add_action('wp_ajax_ss_check_svg_product_exists', array($this, 'ss_check_svg_product_exists'));
        add_action('wp_ajax_nopriv_ss_check_svg_product_exists', array($this, 'ss_check_svg_product_exists'));

        // 【修复】添加清除模板缓存的AJAX处理函数
        add_action('wp_ajax_ss_clear_template_cache', array($this, 'ajax_clear_template_cache'));
        add_action('wp_ajax_nopriv_ss_clear_template_cache', array($this, 'ajax_clear_template_cache'));

        // 【极速优化】模板配置更新
        add_action('wp_ajax_ss_update_template_configs', array($this, 'ajax_update_template_configs'));

        // 【新增功能】自动模板分配AJAX端点
        add_action('wp_ajax_ss_auto_assign_templates', array($this, 'ss_auto_assign_templates'));
        add_action('wp_ajax_nopriv_ss_auto_assign_templates', array($this, 'ss_auto_assign_templates'));

        // 【修复】确保产品图片gallery显示正确的大图
        add_filter('woocommerce_single_product_image_thumbnail_html', array($this, 'fix_product_gallery_large_image'), 10, 2);
        add_filter('wp_get_attachment_image_attributes', array($this, 'fix_attachment_image_attributes'), 10, 3);
    }

    /**
     * 检查是否需要加载自定义模板
     */
    public function ss_maybe_load_custom_template($template) {
        // 只在产品分类页面处理
        if (!is_product_category()) {
            return $template;
        }

        // 获取当前分类
        $term = get_queried_object();

        // 检查是否有预设颜色但没有产品类目图片
        $preset_colors = get_term_meta($term->term_id, 'ss_category_colors', true);
        $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);

        if (!empty($preset_colors) && empty($thumbnail_id)) {
            error_log('【前端】类目 "' . $term->name . '" (ID: ' . $term->term_id . ') 有预设颜色但没有产品类目图片，不加载自定义模板');
            return $template; // 使用默认模板，不加载自定义模板
        }

        // 同时检查是否为底层类目且为空类目（没有非模板产品）
        if ($this->is_leaf_category($term->term_id) && $this->is_empty_category($term->term_id)) {
            // 关键修改：立即返回模板，不等待SVG处理
            // 准备基本的SVG元数据（不处理图片）
            global $svg_designs;
            $svg_designs = $this->prepare_basic_svg_data($term);

            return WP_PLUGIN_DIR . '/shoe-svg-generator/templates/frontend/svg-design-list.php';
        }

        return $template;
    }

    /**
     * 新增：仅准备基本SVG数据，不处理图片
     */
    private function prepare_basic_svg_data($term) {
        // 获取SVG文件列表
        $svg_files = $this->get_svg_files();

        // 存储基本信息
        $basic_designs = array();
        foreach ($svg_files as $index => $file) {
            $file_name = basename($file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

            // 格式化显示名称
            $display_name = str_replace('-', ' ', $safe_name);
            $display_name = ucwords($display_name);

            // 防止名称为空
            if (empty(trim($display_name))) {
                $display_name = 'Design ' . ($index + 1);
            }

            $basic_designs[] = array(
                'id' => $index,
                'name' => $display_name,
                'file' => $file_name,
                'thumbnail' => plugins_url('assets/images/preview-placeholder.svg', dirname(__FILE__))
            );
        }

        return $basic_designs;
    }

    /**
     * 检查分类是否为空（没有产品）
     *
     * 返回true表示需要显示SVG列表
     * 返回false表示显示产品列表
     */
    private function is_empty_category($term_id) {
        // error_log("[" . date('Y-m-d H:i:s') . "] 开始检查类目是否需要显示SVG列表: term_id=" . $term_id);

        // 获取类目信息
        $term = get_term($term_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 无法获取类目信息");
            return false;
        }

        // 【模块4】最高优先级：检查是否为自定义匹配类目
        $is_custom_matching = get_term_meta($term_id, '_is_custom_matching_category', true);
        if (!empty($is_custom_matching)) {
            error_log('【模块4】检测到自定义匹配类目: ' . $term->name . ' (ID: ' . $term_id . ')，启用特殊处理');
            return true; // 自定义匹配类目始终显示SVG预览图页面
        }

        // 【修复重大bug】最高优先级：检查是否为前端生成SVG预览图的类目
        // 前端生成SVG预览图的类目应该始终显示SVG预览图页面，即使已经生成了产品
        $frontend_preview_generated = get_term_meta($term_id, 'ss_frontend_preview_generated', true);
        if (!empty($frontend_preview_generated)) {
            error_log('【前端】类目 "' . $term->name . '" (ID: ' . $term_id . ') 已标记为前端生成SVG预览图，始终显示SVG预览图页面');
            return true; // 始终显示SVG预览图页面，不管是否有产品
        }

        // 第二优先级：检查类目是否有任何产品存在（仅适用于后端生成的产品类目）
        // 这确保了只要有任何产品（通过WP CLI或其他后端方式生成的），都直接显示产品列表
        $has_any_products = $this->category_has_products($term_id);
        if ($has_any_products) {
            error_log('【前端】类目 "' . $term->name . '" (ID: ' . $term_id . ') 已有产品存在且非前端生成类目，直接显示产品列表');
            return false; // 直接显示产品列表，不触发SVG预览图生成
        }

        // 优先级0：检查是否有预设颜色但没有产品类目图片
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

        if (!empty($preset_colors) && empty($thumbnail_id)) {
            error_log('【前端】类目 "' . $term->name . '" (ID: ' . $term_id . ') 有预设颜色但没有产品类目图片，直接显示产品列表');
            return false; // 直接显示产品列表，不触发SVG预览图生成
        }

        // 优先级1：检查是否通过WP CLI命令生成过产品
        $wp_cli_generated = get_term_meta($term_id, 'ss_wp_cli_products_generated', true);
        if (!empty($wp_cli_generated)) {
            error_log("【前端】检测到类目 '" . $term->name . "' 已通过WP CLI命令生成产品，但没有找到产品");
            // 注意：由于我们已经在最高优先级检查了产品存在性，这里不需要再次检查
        }

        // 优先级2：检查是否为后台自动生成的产品
        $backend_generated = get_term_meta($term_id, 'ss_backend_products_generated', true);
        if (!empty($backend_generated)) {
            error_log("【前端】检测到类目 '" . $term->name . "' 已通过后台自动生成产品，但没有找到产品");
            // 注意：由于我们已经在最高优先级检查了产品存在性，这里不需要再次检查
        }

        // 注意：由于前端生成SVG预览图的类目已在最高优先级处理，这里不会再次执行
        // 以下逻辑主要用于处理其他特殊情况或向后兼容

        // 检查是否为模板产品类目
        $has_templates = $this->category_has_templates($term_id);
        if ($has_templates) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 类目 '" . $term->name . "' 是模板类目");

            // 检查SVG文件是否存在
            $svg_files = $this->get_svg_files();
            if (!empty($svg_files)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 找到 " . count($svg_files) . " 个SVG文件");
                return true;
            }
        }

        // 如果没有预览文件也不是模板类目，检查是否有非模板产品
        $args = array(
            'post_type'      => 'product',
            'post_status'    => 'publish',
            'posts_per_page' => 1,
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
            'meta_query'     => array(
                array(
                    'relation' => 'OR',
                    array(
                        'key'     => '_ss_is_template',
                        'compare' => 'NOT EXISTS',
                    ),
                    array(
                        'key'     => '_ss_is_template',
                        'value'   => '1',
                        'compare' => '!=',
                    ),
                ),
                array(
                    'key'     => '_ss_is_temp_product',
                    'compare' => 'NOT EXISTS',
                ),
            ),
        );

        $query = new WP_Query($args);
        $has_regular_products = ($query->post_count > 0);

        // error_log(sprintf('[%s] 类目 "%s" (ID: %d) 检查结果: %s (找到 %d 个非模板产品)', date('Y-m-d H:i:s'), $term->name, $term_id, $has_regular_products ? '有常规产品' : '无常规产品', $query->post_count));

        // 如果没有常规产品，返回true以显示SVG列表
        return !$has_regular_products;
    }

    /**
     * 检查类目是否包含产品
     * 用于验证后台生成的产品是否存在
     */
    private function category_has_products($term_id) {
        $args = array(
            'post_type'      => 'product',
            'post_status'    => 'publish',
            'posts_per_page' => 1,
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'term_id',
                    'terms'    => $term_id,
                ),
            ),
            'meta_query'     => array(
                // 排除模板产品
                array(
                    'relation' => 'OR',
                    array(
                        'key'     => '_ss_is_template',
                        'compare' => 'NOT EXISTS',
                    ),
                    array(
                        'key'     => '_ss_is_template',
                        'value'   => '1',
                        'compare' => '!=',
                    ),
                ),
            ),
        );

        $query = new WP_Query($args);
        $has_products = ($query->post_count > 0);

        error_log(sprintf('【前端】检查类目 ID:%d 是否有产品: %s (找到 %d 个产品)', $term_id, $has_products ? '是' : '否', $query->post_count));

        return $has_products;
    }

    /**
     * 准备SVG设计数据
     */
    private function prepare_svg_designs_data($term) {
        // 获取分类的预设颜色
        $preset_colors = get_term_meta($term->term_id, 'ss_category_colors', true);

        // 如果没有预设颜色，尝试从分类图片中提取
        if (empty($preset_colors)) {
            $preset_colors = $this->extract_colors_from_category_image($term->term_id);
        }

        // 如果仍然没有颜色，跳过该类目
        if (empty($preset_colors)) {
            error_log('【前端】类目没有颜色，跳过处理');
            return array(); // 不使用默认颜色，直接跳过
        }

        // 获取SVG文件列表
        $all_svg_files = $this->get_svg_files();

        // 存储所有SVG文件信息，但不立即处理
        $all_designs = array();
        foreach ($all_svg_files as $index => $file) {
            $all_designs[] = array(
                'id' => $index,
                'file' => $file,
                'processed' => false
            );
        }

        // 将完整文件列表存储在会话中
        $this->store_designs_in_session($term->term_id, array(
            'designs' => $all_designs,
            'colors' => $preset_colors,
            'category_name' => $term->name
        ));

        // 获取并返回第一页的数据
        $first_page_designs = $this->get_processed_designs($term->term_id, 1, 12);

        // 添加调试信息
        // error_log('处理的设计数量: ' . count($first_page_designs));
        // error_log('所有设计数量: ' . count($all_designs));

        return $first_page_designs;
    }

    /**
     * 从分类图片中提取颜色
     *
     * 使用Color Science颜色识别器进行颜色提取，保持与shoe-svg-generator.php的逻辑一致
     *
     * @param int $term_id 分类ID
     * @return array 提取的颜色数组
     */
    public function extract_colors_from_category_image($term_id) {
        // 【修复】检查内存缓存
        if (isset($this->color_cache[$term_id])) {
            error_log('【前端】使用内存缓存的颜色数据，类目ID=' . $term_id);
            return $this->color_cache[$term_id];
        }

        // 【修复】检查数据库缓存（预设颜色）
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (!empty($preset_colors) && is_array($preset_colors)) {
            error_log('【前端】使用数据库缓存的预设颜色，类目ID=' . $term_id . ': ' . implode(', ', $preset_colors));
            $this->color_cache[$term_id] = $preset_colors; // 缓存到内存
            return $preset_colors;
        }

        // 获取分类图片ID
        $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

        if (empty($thumbnail_id)) {
            error_log('【前端】类目ID=' . $term_id . '没有缩略图，无法提取颜色');
            $this->color_cache[$term_id] = array(); // 缓存空结果
            return array();
        }

        // 获取图片路径
        $image_path = get_attached_file($thumbnail_id);

        if (!file_exists($image_path)) {
            error_log('【前端】类目ID=' . $term_id . '的缩略图文件不存在: ' . $image_path);
            $this->color_cache[$term_id] = array(); // 缓存空结果
            return array();
        }

        error_log('【前端】开始从类目ID=' . $term_id . '的图片提取颜色: ' . $image_path);

        // 【修复】使用Color Science颜色识别器提取颜色，关闭调试模式减少日志
        $color_recognizer = SS_Color_Science_Recognizer::get_instance();
        $colors = $color_recognizer->extract_colors_from_image($image_path, 5, '前端管理器');

        if (!empty($colors)) {
            error_log('【前端】从类目图片成功提取颜色: ' . implode(', ', $colors));

            // 【修复】同时缓存到内存和数据库
            $this->color_cache[$term_id] = $colors;
            update_term_meta($term_id, 'ss_category_colors', $colors);

            return $colors;
        }

        error_log('【前端】从类目图片提取颜色失败，跳过该类目');
        $this->color_cache[$term_id] = array(); // 缓存空结果
        return array(); // 不使用默认颜色，直接返回空数组
    }

    /**
     * 从图片中提取颜色
     *
     * 使用Color Science颜色识别器进行颜色提取，保持与shoe-svg-generator.php的逻辑一致
     *
     * @param string $image_path 图片路径
     * @param int $num_colors 要提取的颜色数量
     * @return array 提取的颜色数组
     */
    public function ss_extract_colors_from_image($image_path, $num_colors = 8) {
        error_log('【前端】从图片提取颜色: ' . $image_path);

        // 使用Color Science颜色识别器提取颜色，启用调试模式
        $color_recognizer = SS_Color_Science_Recognizer::get_instance();
        $colors = $color_recognizer->extract_colors_with_debug($image_path, $num_colors, '前端管理器');

        // 如果颜色识别失败，跳过处理
        if (empty($colors)) {
            error_log('【前端】颜色提取失败，跳过该图片');
            return array(); // 不使用默认颜色，直接返回空数组
        } else {
            error_log('【前端】成功提取颜色: ' . implode(', ', $colors));
        }

        return $colors;
    }

    // 以下函数已被移除，请直接使用 SS_Color_Analyzer 类的方法
    //
    // 示例：
    // $color_analyzer = new SS_Color_Analyzer();
    //
    // 检查颜色是否为黑白灰：
    // $rgb = array(hexdec(substr($hex, 1, 2)), hexdec(substr($hex, 3, 2)), hexdec(substr($hex, 5, 2)));
    // $is_grayscale = $color_analyzer->is_grayscale_color($rgb);
    //
    // 调整颜色：
    // $variations = $color_analyzer->generate_saturation_variations($hex, 0, 1);
    // $adjusted_color = $variations[0];
    //
    // 调整亮度：
    // $rgb = array(hexdec(substr($hex, 1, 2)), hexdec(substr($hex, 3, 2)), hexdec(substr($hex, 5, 2)));
    // $hsl = $color_analyzer->rgb_to_hsl($rgb);
    // $hsl[2] = min(100, max(0, $hsl[2] + $percentage));
    // $new_rgb = $color_analyzer->hsl_to_rgb($hsl);
    // $new_hex = sprintf("#%02x%02x%02x", $new_rgb[0], $new_rgb[1], $new_rgb[2]);

    /**
     * 获取SVG文件列表
     */
    public function get_svg_files() {
        $upload_dir = wp_upload_dir();
        $svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/svg/';

        // 确保目录存在
        if (!file_exists($svg_dir)) {
            wp_mkdir_p($svg_dir);
            return array();
        }

        // 获取所有SVG文件
        $svg_files = glob($svg_dir . '*.svg');

        return $svg_files;
    }

    /**
     * 获取并处理指定页的设计
     */
    private function get_processed_designs($term_id, $page, $per_page) {
        // 获取存储的数据
        $stored_data = get_transient('ss_svg_designs_' . $term_id);
        if (empty($stored_data)) {
            return array();
        }

        $all_designs = $stored_data['designs'];
        $preset_colors = $stored_data['colors'];
        $category_name = $stored_data['category_name'];

        // 计算当前页的范围
        $start = ($page - 1) * $per_page;
        $end = min($start + $per_page, count($all_designs));

        // 处理当前页的设计
        $processed_designs = array();
        for ($i = $start; $i < $end; $i++) {
            $design = $all_designs[$i];

            // 如果设计还未处理，进行处理
            if (!$design['processed']) {
                $processed_file = $this->process_svg_for_design($design['file'], $preset_colors, $category_name);
                if ($processed_file) {
                    $all_designs[$i]['processed'] = true;
                    $all_designs[$i]['processed_file'] = $processed_file;
                    $all_designs[$i]['thumbnail'] = $this->get_svg_thumbnail_url($processed_file);
                }
            }

            // 如果处理成功，添加到结果中
            if (!empty($all_designs[$i]['processed_file'])) {
                // 格式化显示名称
                $raw_name = basename($design['file'], '.svg');
                $display_name = str_replace('-', ' ', $raw_name);
                $display_name = ucwords($display_name);

                // 防止空名称
                if (empty(trim($display_name))) {
                    $display_name = 'Design ' . ($design['id'] + 1);
                }

                $processed_designs[] = array(
                    'id' => $design['id'],
                    'name' => $display_name,
                    'file' => $all_designs[$i]['processed_file'],
                    'thumbnail' => $all_designs[$i]['thumbnail']
                );
            }
        }

        // 更新存储的数据
        $stored_data['designs'] = $all_designs;
        set_transient('ss_svg_designs_' . $term_id, $stored_data, 24 * HOUR_IN_SECONDS);

        return $processed_designs;
    }

    /**
     * 处理单个SVG设计
     */
    private function process_svg_for_design($svg_file, $preset_colors, $category_name) {
        // 安全的分类名称
        $safe_category_name = $this->get_safe_category_name($category_name);

        // 生成处理后的文件名
        $svg_filename = basename($svg_file);
        $processed_svg_filename = $safe_category_name . '_' . $svg_filename;

        // 使用新的 SVG 文件管理器检查文件
        if (class_exists('SS_SVG_File_Manager')) {
            $svg_file_manager = SS_SVG_File_Manager::get_instance();

            // 通过类目名称获取类目ID
            $term = get_term_by('name', $category_name, 'product_cat');
            if ($term && !is_wp_error($term)) {
                // 【修复】添加调试日志
                error_log("[前端SVG处理][修复] 查找SVG文件: {$svg_filename}, 类目ID: {$term->term_id}");
                
                // 检查文件是否存在（优先从OSS，回退到本地）
                $existing_file_path = $svg_file_manager->get_svg_file_path($svg_filename, $term->term_id);
                if ($existing_file_path) {
                    error_log("[前端SVG处理] 找到已存在的处理后文件: {$existing_file_path}");
                    return $existing_file_path;
                } else {
                    error_log("[前端SVG处理][修复] SVG文件管理器未找到文件: {$svg_filename}");
                }
            } else {
                error_log("[前端SVG处理][修复] 无法找到类目: {$category_name}");
            }
        }

        // 如果新系统中没有找到，回退到传统方式
        $upload_dir = wp_upload_dir();
        $processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
        $processed_svg_file = $processed_svg_dir . $processed_svg_filename;

        // 确保目录存在
        if (!file_exists($processed_svg_dir)) {
            wp_mkdir_p($processed_svg_dir);
        }

        // 如果文件已存在，直接返回
        if (file_exists($processed_svg_file)) {
            return $processed_svg_file;
        }

        // 处理SVG文件
        if ($this->process_single_svg($svg_file, $processed_svg_file, $preset_colors, $category_name)) {
            return $processed_svg_file;
        }

        return false;
    }

    /**
     * 处理单个SVG文件
     */
    private function process_single_svg($input_file, $output_file, $preset_colors, $category_name) {
        // 【新增】检查并修复输入SVG文件的结构问题
        $repair_result = $this->check_and_repair_svg_structure($input_file);
        if (!$repair_result['success']) {
            error_log('[SVG处理] 输入文件修复失败: ' . $repair_result['error']);
            return false;
        }

        if ($repair_result['repaired']) {
            error_log('[SVG处理] 输入文件已修复: ' . basename($input_file));
        }

        // 【修复】使用改进的Python脚本，解决XML语法错误和并发写入问题
        $python_script = '/www/wwwroot/matchbeast.com/wp-content/plugins/shoe-svg-generator/python/process_svg_fixed.py';

        $python_executable = 'python3';
        $input_file_arg = escapeshellarg($input_file);
        $output_file_arg = escapeshellarg($output_file);
        $colors_param = implode(',', $preset_colors);
        $colors_param_arg = escapeshellarg($colors_param);
        $category_name_arg = escapeshellarg($category_name);

        // 构建命令
        $command = $python_executable . ' ' . escapeshellarg($python_script) . ' ' .
                   $input_file_arg . ' ' . $output_file_arg . ' ' .
                   $colors_param_arg . ' ' . $category_name_arg;

        // 执行命令
        $output = array();
        $return_var = 0;
        exec($command . ' 2>&1', $output, $return_var);

        // 记录执行结果
        if ($return_var !== 0) {
            // error_log('处理SVG文件失败: ' . implode("\n", $output));
            return false;
        }

        // 【新增】检查并修复输出SVG文件的结构问题
        if (file_exists($output_file)) {
            $output_repair_result = $this->check_and_repair_svg_structure($output_file);
            if (!$output_repair_result['success']) {
                error_log('[SVG处理] 输出文件修复失败: ' . $output_repair_result['error']);
                return false;
            }

            if ($output_repair_result['repaired']) {
                error_log('[SVG处理] 输出文件已修复: ' . basename($output_file));
            }
        }

        // 生成缩略图
        $this->generate_svg_thumbnail($output_file);

        return true;
    }

    /**
     * 生成SVG缩略图
     */
    private function generate_svg_thumbnail($svg_file) {
        // 【修复】确保目标目录存在 - 使用新的预览图路径结构
        $upload_dir = wp_upload_dir();
        // 从SVG文件路径中提取类目名称
        $category_name = basename(dirname($svg_file));
        $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';

        if (!file_exists($preview_dir)) {
            wp_mkdir_p($preview_dir);
        }

        // 【修复】获取文件名 - 使用新的文件命名格式
        $file_name = basename($svg_file, '.svg');

        // 【修复】生成缩略图文件名(.webp格式) - 移除类目前缀
        $thumbnail_file = $preview_dir . $file_name . '_preview.webp';

        // 如果缩略图已存在，跳过生成
        if (file_exists($thumbnail_file)) {
            return $thumbnail_file;
        }

        // 使用Imagick处理SVG并生成webp缩略图
        try {
            $imagick = new Imagick();
            $imagick->setBackgroundColor(new ImagickPixel('transparent')); // 使用透明背景
            $imagick->readImage($svg_file);
            $imagick->setImageFormat('webp');
            $imagick->thumbnailImage(500, 500, true, true);
            $imagick->setImageCompressionQuality(85);
            $imagick->writeImage($thumbnail_file);
            $imagick->clear();
            $imagick->destroy();

            return $thumbnail_file;
        } catch (Exception $e) {
            // error_log('生成SVG缩略图失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 权限修复函数 - 确保目录和文件所有者为www用户
     */
    private function ensure_www_permissions($path, $is_file = false) {
        if (!file_exists($path)) {
            return false;
        }
        
        // 设置基本权限
        $mode = $is_file ? 0644 : 0755;
        @chmod($path, $mode);
        
        // 尝试设置www用户所有权（优先使用www用户）
        $www_users = ['www', 'www-data', 'apache', 'nginx', 'httpd'];
        
        foreach ($www_users as $www_user) {
            if (function_exists('posix_getpwnam')) {
                $user_info = posix_getpwnam($www_user);
                if ($user_info !== false) {
                    $www_uid = $user_info['uid'];
                    $www_gid = $user_info['gid'];
                    
                    // 尝试使用PHP函数设置所有者
                    if (@chown($path, $www_uid) && @chgrp($path, $www_gid)) {
                        error_log("[权限修复] 成功设置所有者: " . basename($path) . " -> {$www_user}");
                        return true;
                    }
                }
            }
            
            // 尝试使用系统命令
            $cmd = "chown {$www_user}:{$www_user} " . escapeshellarg($path) . " 2>/dev/null";
            $result = shell_exec($cmd);
            if ($result === null || empty(trim($result))) {
                error_log("[权限修复] 使用系统命令设置所有者成功: " . basename($path) . " -> {$www_user}");
                return true;
            }
        }
        
        error_log("[权限修复] 无法设置www用户所有权: " . basename($path));
        return false;
    }

    /**
     * 创建目录并设置正确权限
     */
    private function create_directory_with_permissions($dir_path) {
        if (file_exists($dir_path)) {
            $this->ensure_www_permissions($dir_path, false);
            return true;
        }
        
        // 创建目录
        $created = wp_mkdir_p($dir_path);
        if ($created) {
            @chmod($dir_path, 0755);
            $this->ensure_www_permissions($dir_path, false);
            error_log("[权限修复] 创建目录并设置权限: {$dir_path}");
            return true;
        }
        
        error_log("[权限修复] 创建目录失败: {$dir_path}");
        return false;
    }

    /**
     * 创建文件并设置正确权限
     */
    private function create_file_with_permissions($file_path, $content = '') {
        $result = file_put_contents($file_path, $content);
        if ($result !== false) {
            @chmod($file_path, 0644);
            $this->ensure_www_permissions($file_path, true);
            error_log("[权限修复] 创建文件并设置权限: " . basename($file_path));
            return true;
        }
        
        error_log("[权限修复] 创建文件失败: " . basename($file_path));
        return false;
    }

    /**
     * 递归设置整个目录树的权限
     */
    private function fix_directory_tree_permissions($dir_path) {
        if (!file_exists($dir_path) || !is_dir($dir_path)) {
            return false;
        }

        // 首先设置当前目录权限
        $this->ensure_www_permissions($dir_path, false);
        error_log("[权限修复] 递归修复目录权限: {$dir_path}");

        // 递归处理所有子目录和文件
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir_path, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                $this->ensure_www_permissions($item->getPathname(), false);
                error_log("[权限修复] 递归修复子目录权限: " . $item->getPathname());
            } else {
                $this->ensure_www_permissions($item->getPathname(), true);
                error_log("[权限修复] 递归修复文件权限: " . $item->getPathname());
            }
        }

        return true;
    }

    /**
     * 创建类目完整目录结构并设置权限
     */
    private function create_category_directory_structure($category_name) {
        $upload_dir = wp_upload_dir();
        $safe_category_name = $this->get_safe_category_name($category_name);
        
        // 主类目目录
        $category_main_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/';
        
        // 预览图子目录
        $previews_dir = $category_main_dir . 'previews/';
        
        // 创建主目录
        if (!file_exists($category_main_dir)) {
            $this->create_directory_with_permissions($category_main_dir);
        } else {
            $this->ensure_www_permissions($category_main_dir, false);
        }
        
        // 创建预览图子目录
        if (!file_exists($previews_dir)) {
            $this->create_directory_with_permissions($previews_dir);
        } else {
            $this->ensure_www_permissions($previews_dir, false);
        }
        
        // 递归修复整个目录树的权限
        $this->fix_directory_tree_permissions($category_main_dir);
        
        error_log("[权限修复] 完整类目目录结构创建完成: {$category_main_dir}");
        
        return $category_main_dir;
    }

    /**
     * 获取安全的分类名称 - 【修复】保持与实际目录结构一致
     */
    public function get_safe_category_name($category_name) {
        // 【修复】使用sanitize_title但保持连字符，与实际目录结构一致
        // 实际路径示例：/processed_svg/Nike-Nike-Air-Presto-Essential-Binary/
        $safe_name = sanitize_title($category_name);

        // 记录日志
        error_log("[路径修复] 转换类目名称：{$category_name} -> {$safe_name}");

        return $safe_name;
    }

    /**
     * 【新增】检查SVG文件完整性
     */
    private function check_svg_integrity($svg_file) {
        if (!file_exists($svg_file)) {
            error_log('[SVG完整性] 文件不存在: ' . $svg_file);
            return false;
        }

        $file_size = filesize($svg_file);
        if ($file_size === false || $file_size === 0) {
            error_log('[SVG完整性] 文件为空或无法读取: ' . $svg_file);
            return false;
        }

        // 【修复】跳过所有XML解析检查，直接返回true
        // 这样可以确保所有SVG文件都能被处理，即使有XML格式问题
        error_log('[SVG完整性] 跳过完整性检查，允许处理: ' . $svg_file . ' (大小: ' . round($file_size / 1024, 2) . 'KB)');
        return true;
    }

    /**
     * 获取SVG文件的尺寸信息
     */
    private function get_svg_dimensions($svg_file) {
        $dimensions = ['width' => 0, 'height' => 0];

        if (!file_exists($svg_file)) {
            return $dimensions;
        }

        // 方法1：使用inkscape获取尺寸
        $temp_dir = sys_get_temp_dir() . '/';
        $svg_info_path = $temp_dir . 'svg_info_' . uniqid() . '.txt';
        $svg_info_command = sprintf(
            'inkscape --query-width --query-height %s > %s 2>/dev/null',
            escapeshellarg($svg_file),
            escapeshellarg($svg_info_path)
        );

        exec($svg_info_command, $info_output, $info_return_var);

        if (file_exists($svg_info_path) && filesize($svg_info_path) > 0) {
            $svg_info = file_get_contents($svg_info_path);
            if (preg_match('/Width: ([\d\.]+).*Height: ([\d\.]+)/s', $svg_info, $matches) ||
                preg_match('/([\d\.]+)[\s\n]+([\d\.]+)/', $svg_info, $matches)) {
                $dimensions['width'] = floatval($matches[1]);
                $dimensions['height'] = floatval($matches[2]);
            }
            @unlink($svg_info_path);
        }

        // 方法2：如果inkscape失败，尝试解析viewBox
        if ($dimensions['width'] <= 0 || $dimensions['height'] <= 0) {
            $viewbox_command = sprintf('grep -o \'viewBox="[^"]*"\' %s 2>/dev/null', escapeshellarg($svg_file));
            exec($viewbox_command, $viewbox_output, $viewbox_return);

            if (!empty($viewbox_output) && preg_match('/viewBox="[^"]*\s+([0-9.]+)\s+([0-9.]+)"/', $viewbox_output[0], $vb_matches)) {
                $dimensions['width'] = floatval($vb_matches[1]);
                $dimensions['height'] = floatval($vb_matches[2]);
            }
        }

        // 方法3：如果还是失败，尝试解析SVG文件的width和height属性
        if ($dimensions['width'] <= 0 || $dimensions['height'] <= 0) {
            $svg_content = file_get_contents($svg_file);
            if (preg_match('/width="([0-9.]+)"/', $svg_content, $w_matches) &&
                preg_match('/height="([0-9.]+)"/', $svg_content, $h_matches)) {
                $dimensions['width'] = floatval($w_matches[1]);
                $dimensions['height'] = floatval($h_matches[1]);
            }
        }

        return $dimensions;
    }

    /**
     * 【新增】检测CPU核心数
     */
    private function detect_cpu_cores() {
        $cores = 1; // 默认值

        if (function_exists('shell_exec')) {
            // Linux/Unix 系统
            $output = shell_exec('nproc 2>/dev/null');
            if ($output) {
                $cores = intval(trim($output));
            } else {
                // 备选方法
                $output = shell_exec('grep -c ^processor /proc/cpuinfo 2>/dev/null');
                if ($output) {
                    $cores = intval(trim($output));
                }
            }
        }

        return max(1, $cores);
    }

    /**
     * 获取SVG缩略图URL
     */
    private function get_svg_thumbnail_url($svg_file) {
        // 修正：使用与generate_svg_preview_file方法一致的文件名生成规则
        $file_name = basename($svg_file);
        $category_name = basename(dirname($svg_file));

        $upload_dir = wp_upload_dir();

        // 【优化】使用新的预览目录和文件格式(.webp) - frontend-gallery路径
        $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
        $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';

        // 获取安全的文件名(去掉.svg后缀)
        $safe_file_name = pathinfo($file_name, PATHINFO_FILENAME);

        // 【优化】构建完整的文件名 - 移除类目前缀
        $thumbnail_file = $preview_dir . $safe_file_name . '_preview.webp';
        $thumbnail_url = $preview_url . $safe_file_name . '_preview.webp';

        // 检查文件是否存在
        if (file_exists($thumbnail_file)) {
            return $thumbnail_url;
        }

        // 如果文件不存在，返回默认图片
        return plugins_url('assets/images/default-preview.png', dirname(__FILE__));
    }

    /**
     * 将设计数据存储在会话中
     */
    private function store_designs_in_session($term_id, $designs) {
        // 使用transient存储设计数据，有效期24小时
        set_transient('ss_svg_designs_' . $term_id, $designs, 24 * HOUR_IN_SECONDS);
    }

    /**
     * 注册前端脚本和样式
     */
    public function ss_enqueue_frontend_scripts() {
        if (!is_product_category()) {
            return;
        }

        $term = get_queried_object();
        // 同时检查是否为底层类目和空类目
        if (!$this->is_leaf_category($term->term_id) || !$this->is_empty_category($term->term_id)) {
            return;
        }

        // 注册和加载样式
        wp_enqueue_style(
            'ss-frontend-styles',
            plugin_dir_url(dirname(__FILE__)) . 'shoe-svg-generator/css/ss-frontend-styles.css',
            array(),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'shoe-svg-generator/css/ss-frontend-styles.css')
        );

        // 注册和加载脚本
        wp_enqueue_script(
            'ss-frontend-scripts',
            plugin_dir_url(dirname(__FILE__)) . 'shoe-svg-generator/js/ss-frontend-scripts.js',
            array('jquery'),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'shoe-svg-generator/js/ss-frontend-scripts.js'),
            true
        );

        // 本地化脚本
        wp_localize_script('ss-frontend-scripts', 'ss_frontend_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ss_frontend_nonce'),
            'load_more_text' => 'Load More',
            'svg_preview_title' => 'Design Preview',
            'no_more_text' => 'No more designs',
            'error_text' => 'Failed to load',
            'loading_text' => 'Loading...',
            'default_preview' => plugins_url('assets/images/default-preview.png', dirname(__FILE__)),
            'placeholder_image' => plugins_url('assets/images/preview-placeholder.svg', dirname(__FILE__)),
            'performance_tracking' => true,
            'connection_type' => isset($_SERVER['HTTP_CONNECTION']) ? $_SERVER['HTTP_CONNECTION'] : 'unknown'
        ));
    }

    /**
     * AJAX加载更多设计
     */
    public function ss_load_more_designs() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = 12;
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        if ($page < 1 || $category_id === 0) {
            wp_send_json_error(array('message' => '参数无效'));
        }

        // 获取并处理下一页的设计
        $designs = $this->get_processed_designs($category_id, $page, $per_page);

        if (empty($designs)) {
            wp_send_json_error(array('message' => '没有更多设计'));
        }

        // 获取存储的数据以检查是否还有更多
        $stored_data = get_transient('ss_svg_designs_' . $category_id);
        $total_designs = count($stored_data['designs']);
        $has_more = ($page * $per_page) < $total_designs;

        wp_send_json_success(array(
            'designs' => $designs,
            'has_more' => $has_more
        ));
    }

    /**
     * 清理临时数据
     */
    public function ss_cleanup_temp_data() {
        global $wpdb;

        // 获取48小时前的时间戳
        $expiry_time = time() - (48 * HOUR_IN_SECONDS);

        // 查找标记为临时的产品
        $temp_products = $wpdb->get_col($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta}
            WHERE meta_key = '_ss_is_temp_product'
            AND meta_value = '1'
            AND post_id IN (
                SELECT post_id FROM {$wpdb->postmeta}
                WHERE meta_key = '_ss_temp_creation_time'
                AND meta_value < %d
            )",
            $expiry_time
        ));

        // 删除临时产品
        if (!empty($temp_products)) {
            foreach ($temp_products as $product_id) {
                wp_delete_post($product_id, true);
            }

            // error_log('已清理 ' . count($temp_products) . ' 个临时产品');
        }

        // 清理transient
        $wpdb->query(
            "DELETE FROM {$wpdb->options}
            WHERE option_name LIKE '_transient_ss_svg_designs_%'
            AND option_value < {$expiry_time}"
        );
    }

    /**
     * 检测类目是否有关联的产品模板
     */
    public function ss_check_category_templates() {
        // 只在产品分类页面处理
        if (!is_product_category()) {
            return;
        }

        $term = get_queried_object();
        if (!$term || !isset($term->term_id)) {
            return;
        }

        // 检查是否有预设颜色但没有产品类目图片
        $preset_colors = get_term_meta($term->term_id, 'ss_category_colors', true);
        $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);

        if (!empty($preset_colors) && empty($thumbnail_id)) {
            error_log('【前端】类目 "' . $term->name . '" (ID: ' . $term->term_id . ') 有预设颜色但没有产品类目图片，跳过SVG预览图生成');
            return; // 直接返回，不触发SVG预览图生成
        }

        // 确保同时满足两个条件：是底层类目且是空类目
        if (!$this->is_leaf_category($term->term_id) || !$this->is_empty_category($term->term_id)) {
            return;
        }

        // 检查是否有关联的模板产品
        $has_templates = $this->category_has_templates($term->term_id);

        // 如果有模板，添加前端数据
        if ($has_templates) {
            // 准备SVG数据
            $svg_data = $this->prepare_category_svg_data($term->term_id);

            // 添加前端数据
            wp_localize_script('ss-frontend-scripts', 'ss_category_data', array(
                'has_templates' => true,
                'term_id' => $term->term_id,
                'term_name' => $term->name,
                'svg_data' => $svg_data
            ));
        }
    }

    /**
     * 检查分类是否有关联的模板产品
     */
    private function category_has_templates($term_id) {
        // 获取选择的模板
        $selected_templates = get_term_meta($term_id, 'ss_selected_templates', true);
        $has_templates = !empty($selected_templates) && is_array($selected_templates);

        // 【新增功能】如果类目没有模板产品，自动添加所有模板产品
        if (!$has_templates) {
            error_log("[自动模板分配] 类目 {$term_id} 没有模板产品，开始自动分配");
            $auto_assign_result = $this->auto_assign_all_templates_to_category($term_id);

            if ($auto_assign_result['success']) {
                error_log("[自动模板分配] 成功为类目 {$term_id} 分配了 {$auto_assign_result['count']} 个模板产品");
                return true; // 分配成功后返回true
            } else {
                error_log("[自动模板分配] 为类目 {$term_id} 分配模板产品失败: {$auto_assign_result['message']}");
                return false;
            }
        }

        return $has_templates;
    }

    /**
     * 【新增功能】自动为类目分配所有模板产品
     * 当类目没有模板产品时，自动将所有模板产品添加到该类目下
     */
    private function auto_assign_all_templates_to_category($category_id) {
        $start_time = microtime(true);

        // 获取类目信息
        $term = get_term($category_id);
        if (!$term || is_wp_error($term)) {
            return ['success' => false, 'message' => '无效的类目ID'];
        }

        error_log("[自动模板分配] 开始为类目 '{$term->name}' (ID: {$category_id}) 分配模板产品");

        // 获取所有模板产品
        $all_template_products = $this->get_all_template_products();

        if (empty($all_template_products)) {
            return ['success' => false, 'message' => '没有找到模板产品'];
        }

        error_log("[自动模板分配] 找到 " . count($all_template_products) . " 个模板产品");

        // 批量分配模板产品到类目
        $assigned_count = 0;
        $failed_count = 0;

        foreach ($all_template_products as $template_id) {
            // 将模板产品添加到类目（不覆盖现有类目）
            $result = wp_set_object_terms($template_id, $category_id, 'product_cat', true);

            if (!is_wp_error($result)) {
                $assigned_count++;
            } else {
                $failed_count++;
                error_log("[自动模板分配] 分配模板产品 {$template_id} 到类目 {$category_id} 失败: " . $result->get_error_message());
            }
        }

        // 保存模板产品列表到类目元数据
        update_term_meta($category_id, 'ss_selected_templates', $all_template_products);

        // 清除相关缓存
        wp_cache_delete('template_products_' . $category_id, 'ss_svg_generator');

        $execution_time = microtime(true) - $start_time;
        error_log("[自动模板分配] 完成分配，成功: {$assigned_count}, 失败: {$failed_count}, 耗时: " . round($execution_time, 3) . "秒");

        return [
            'success' => true,
            'count' => $assigned_count,
            'failed' => $failed_count,
            'total' => count($all_template_products),
            'execution_time' => $execution_time
        ];
    }

    /**
     * 【新增功能】获取所有模板产品ID
     */
    public function get_all_template_products() {
        // 使用缓存避免重复查询
        $cache_key = 'all_template_products_ids';
        $cached_templates = wp_cache_get($cache_key, 'ss_svg_generator');

        if ($cached_templates !== false) {
            return $cached_templates;
        }

        global $wpdb;

        // 使用优化的SQL查询获取所有模板产品ID
        $template_ids = $wpdb->get_col("
            SELECT p.ID
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'product'
            AND p.post_status = 'publish'
            AND pm.meta_key = '_ss_is_template'
            AND pm.meta_value = '1'
            ORDER BY p.ID ASC
        ");

        // 缓存结果30分钟
        wp_cache_set($cache_key, $template_ids, 'ss_svg_generator', 1800);

        return $template_ids;
    }

    /**
     * 【优化】准备类目相关的SVG数据 - 立即加载框架并智能缓存
     */
    private function prepare_category_svg_data($term_id) {
        // 【新增】立即返回框架数据，确保页面立即渲染
        $framework_data = $this->get_immediate_framework_data($term_id);
        
        // 获取分类预设颜色
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (empty($preset_colors)) {
            $preset_colors = $this->extract_colors_from_category_image($term_id);
        }

        // 如果仍然没有颜色，跳过该类目
        if (empty($preset_colors)) {
            error_log('【前端】类目没有颜色，跳过SVG处理');
            return $framework_data; // 返回框架数据而不是空数组
        }

        // 获取SVG文件列表（只获取元数据，不处理图片）
        $svg_files = $this->get_svg_files();
        $term = get_term($term_id);
        $category_name = $this->get_safe_category_name($term->name);
        
        // 【新增】检查是否为重复访问，如果是则尝试加载缓存的预览图
        $cached_previews = $this->get_cached_category_previews($term_id, $category_name);
        if (!empty($cached_previews)) {
            error_log("[缓存优化] 类目 {$term_id} 检测到缓存预览图 " . count($cached_previews) . " 个，直接使用");
            return array_merge($framework_data, ['cached_previews' => $cached_previews]);
        }

        // 只准备元数据，不处理图片
        $preview_data = array();
        foreach ($svg_files as $index => $file) {
            $file_name = basename($file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

            // 格式化显示名称
            $display_name = str_replace('-', ' ', $safe_name);
            $display_name = ucwords($display_name);

            if (empty(trim($display_name))) {
                $display_name = 'Design ' . ($index + 1);
            }

            // 计算优先级 - 前12个为第一批加载，其余按顺序降低优先级
            $priority = $index < 12 ? 1 : ceil(($index - 11) / 6) + 1;

            $preview_data[] = array(
                'id' => $index,
                'file' => $file_name,
                'original_path' => $file,
                'name' => $display_name,
                'raw_name' => $safe_name,
                'priority' => $priority,
                'loading_state' => 'pending' // 状态：pending, loading, loaded, error
            );
        }

        return array(
            'previews' => $preview_data,
            'colors' => $preset_colors,
            'total' => count($svg_files),
            'category_name' => $category_name,
            'term_id' => $term_id
        );
    }

    /**
     * 新增：添加API端点获取SVG预览批次数据
     */
    public function ss_get_preview_batch() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;
        $batch = isset($_POST['batch']) ? intval($_POST['batch']) : 1;
        $visible_indexes = isset($_POST['visible']) ? array_map('intval', $_POST['visible']) : array();

        if ($term_id === 0) {
            wp_send_json_error(['message' => 'Invalid term ID']);
            return;
        }

        // 获取类目数据
        $term = get_term($term_id);
        $category_name = $this->get_safe_category_name($term->name);
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (empty($preset_colors)) {
            $preset_colors = $this->extract_colors_from_category_image($term_id);
        }

        if (empty($preset_colors)) {
            error_log('【前端】类目没有颜色，跳过批次处理');
            wp_send_json_error(['message' => '类目没有颜色，跳过处理']);
            return;
        }

        // 处理可视区域中的项目（优先级最高）
        $results = array();
        if (!empty($visible_indexes)) {
            $svg_files = $this->get_svg_files();
            foreach ($visible_indexes as $index) {
                if (isset($svg_files[$index])) {
                    $preview_url = $this->process_and_get_preview($svg_files[$index], $category_name, $preset_colors[0], $term_id);
                    if ($preview_url) {
                        $results[] = [
                            'index' => $index,
                            'preview_url' => $preview_url,
                            'state' => 'loaded'
                        ];
                    }
                }
            }
        }

        wp_send_json_success(['previews' => $results]);
    }

    /**
     * 处理并获取预览URL - 优化版，仅在需要时处理
     */
    private function process_and_get_preview($svg_file, $category_name, $primary_color, $provided_term_id = 0) {
        // 检查预览是否已存在
        $upload_dir = wp_upload_dir();
        $file_name = basename($svg_file);
        $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

        // 【模块4】检查是否为自定义匹配类目，使用专用路径
        $is_custom_matching = false;
        $category_slug = '';
        if ($provided_term_id) {
            $is_custom_matching = get_term_meta($provided_term_id, '_is_custom_matching_category', true);
            if (!empty($is_custom_matching)) {
                $term = get_term($provided_term_id);
                $category_slug = $term->slug;
            }
        }

        if (!empty($is_custom_matching) && !empty($category_slug)) {
            // 自定义匹配类目使用专用路径，每个类目有独立文件夹
            $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $safe_name . '_preview.webp';
            $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $safe_name . '_preview.webp';
            error_log('【模块4】自定义匹配类目预览图路径: ' . $preview_path);
        } else {
            // 【优化】使用新的预览图路径 - frontend-gallery/{category}/previews/
            $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';
            $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';
        }

        // 如果预览已存在，直接返回URL
        if (file_exists($preview_path)) {
            return $preview_url;
        }

        // 生成预览
        $result = $this->generate_svg_preview_file($svg_file, $category_name, $primary_color, true);
        return $result ? $preview_url : false;
    }

    /**
     * 清理旧的SVG预览文件 - 支持新的frontend-gallery路径结构
     * 【修复】确保不删除已生成产品使用的预览图
     */
    public function ss_cleanup_old_previews() {
        error_log("[预览清理][修复] 开始清理旧预览文件，保护已生成产品的预览图");

        // 【修复】获取所有已生成产品使用的预览图文件名，避免误删
        $protected_preview_files = $this->get_protected_preview_files();

        // 【优化】清理旧的预览目录
        $old_preview_dir = wp_upload_dir()['basedir'] . '/shoe-svg-generator/previews/';
        if (file_exists($old_preview_dir)) {
            $files = glob($old_preview_dir . '*_preview.webp');
            $now = time();
            foreach ($files as $file) {
                $filename = basename($file);
                // 【修复】检查是否是受保护的预览图
                if (in_array($filename, $protected_preview_files)) {
                    error_log("[预览清理][修复] 跳过删除受保护的预览图: {$filename}");
                    continue;
                }

                if ($now - filemtime($file) > 30 * DAY_IN_SECONDS) {
                    unlink($file);
                    error_log("[预览清理][修复] 删除过期预览图: {$filename}");
                }
            }
        }

        // 【优化】清理新的frontend-gallery目录结构
        $frontend_gallery_dir = wp_upload_dir()['basedir'] . '/shoe-svg-generator/frontend-gallery/';
        if (file_exists($frontend_gallery_dir)) {
            $category_dirs = glob($frontend_gallery_dir . '*/previews/', GLOB_ONLYDIR);
            $now = time();
            foreach ($category_dirs as $preview_dir) {
                $files = glob($preview_dir . '*_preview.webp');
                foreach ($files as $file) {
                    $filename = basename($file);
                    // 【修复】检查是否是受保护的预览图
                    if (in_array($filename, $protected_preview_files)) {
                        error_log("[预览清理][修复] 跳过删除受保护的预览图: {$filename}");
                        continue;
                    }

                    if ($now - filemtime($file) > 30 * DAY_IN_SECONDS) {
                        unlink($file);
                        error_log("[预览清理][修复] 删除过期预览图: {$filename}");
                    }
                }
            }
        }

        error_log("[预览清理][修复] 预览文件清理完成，已保护 " . count($protected_preview_files) . " 个已生成产品的预览图");
    }

    /**
     * 【修复】获取所有已生成产品使用的预览图文件名
     * 用于保护这些文件不被误删
     */
    private function get_protected_preview_files() {
        global $wpdb;

        $protected_files = array();

        try {
            // 查询所有已生成的产品（非模板产品）
            $generated_products = $wpdb->get_results("
                SELECT p.ID, p.post_title
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm_template ON p.ID = pm_template.post_id AND pm_template.meta_key = '_ss_is_template'
                LEFT JOIN {$wpdb->postmeta} pm_temp ON p.ID = pm_temp.post_id AND pm_temp.meta_key = '_ss_is_temp_product'
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND (pm_template.meta_value IS NULL OR pm_template.meta_value != '1')
                AND (pm_temp.meta_value IS NULL OR pm_temp.meta_value != '1')
            ");

            foreach ($generated_products as $product) {
                // 获取产品的画廊图片
                $gallery_ids = get_post_meta($product->ID, '_product_image_gallery', true);
                if (!empty($gallery_ids)) {
                    $gallery_array = explode(',', $gallery_ids);

                    foreach ($gallery_array as $image_id) {
                        $image_url = wp_get_attachment_url($image_id);
                        if ($image_url && strpos($image_url, '_preview.webp') !== false) {
                            $filename = basename($image_url);
                            $protected_files[] = $filename;
                            error_log("[预览保护][修复] 保护产品 '{$product->post_title}' 的预览图: {$filename}");
                        }
                    }
                }
            }

            // 去重
            $protected_files = array_unique($protected_files);

            error_log("[预览保护][修复] 找到 " . count($protected_files) . " 个需要保护的预览图文件");

        } catch (Exception $e) {
            error_log("[预览保护][修复] 获取受保护预览图时出错: " . $e->getMessage());
        }

        return $protected_files;
    }

    /**
     * 检查是否为底层类目（没有子类目）
     */
    private function is_leaf_category($term_id) {
        // 查找是否有以当前类目为父类的子类目
        $child_terms = get_terms(array(
            'taxonomy' => 'product_cat',
            'parent' => $term_id,
            'hide_empty' => false,
            'fields' => 'ids',
            'number' => 1, // 只需要知道是否存在至少一个子类目
        ));

        $is_leaf = empty($child_terms) || is_wp_error($child_terms);

        // 添加调试日志
        $term = get_term($term_id, 'product_cat');
        // error_log(sprintf(
        //     '检查类目 "%s" (ID: %d) 是否为底层类目: %s (子类目: %s)',
        //     $term->name,
        //     $term_id,
        //     $is_leaf ? '是' : '否',
        //     is_wp_error($child_terms) ? '错误' : implode(', ', $child_terms)
        // ));

        return $is_leaf;
    }

    /**
     * 添加新的AJAX处理函数，用于逐一生成图片
     */
    public function ss_generate_single_preview() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $file_path = isset($_POST['file_path']) ? sanitize_text_field($_POST['file_path']) : '';
        $category_name = isset($_POST['category_name']) ? sanitize_text_field($_POST['category_name']) : '';
        $index = isset($_POST['index']) ? intval($_POST['index']) : 0;
        $color = isset($_POST['color']) ? sanitize_hex_color($_POST['color']) : '';

        if (empty($file_path) || empty($category_name)) {
            wp_send_json_error(array('message' => 'Missing required parameters'));
        }

        // 检查文件路径安全性
        if (strpos($file_path, '../') !== false) {
            wp_send_json_error(array('message' => 'Invalid file path'));
        }

        // 生成预览
        $preview_url = $this->generate_svg_preview_file($file_path, $category_name, $color, true);

        if ($preview_url) {
            wp_send_json_success(array(
                'preview_url' => $preview_url,
                'index' => $index,
                'file' => basename($file_path)
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Failed to generate preview',
                'index' => $index
            ));
        }
    }

    /**
     * 跟踪用户交互事件
     */
    public function ss_track_interaction() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $event = isset($_POST['event']) ? sanitize_text_field($_POST['event']) : '';
        $data = isset($_POST['data']) ? $_POST['data'] : array();

        // 记录交互数据
        if (!empty($event)) {
            // 这里可以实现交互数据的存储逻辑
            // error_log('用户交互: ' . $event . ' - 数据: ' . json_encode($data));
        }

        wp_send_json_success();
    }

    /**
     * 获取预览状态 - 使用并发处理器实现多线程处理
     */
    public function ss_check_previews_status() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;
        $visible_indexes = isset($_POST['visible_indexes']) ? sanitize_text_field($_POST['visible_indexes']) : '';
        $display_indexes = isset($_POST['display_indexes']) ? sanitize_text_field($_POST['display_indexes']) : '';
        $loading_stage = isset($_POST['loading_stage']) ? intval($_POST['loading_stage']) : 1;
        // 添加首次访问检测参数
        $is_first_visit = isset($_POST['is_first_visit']) ? filter_var($_POST['is_first_visit'], FILTER_VALIDATE_BOOLEAN) : false;

        if ($term_id === 0) {
            wp_send_json_error(['message' => 'Invalid term ID']);
            return;
        }

        // 获取分类数据
        $term = get_term($term_id);
        if (!$term || is_wp_error($term)) {
            wp_send_json_error(['message' => 'Invalid term']);
            return;
        }

        // 检查是否有预设颜色但没有产品类目图片
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

        if (!empty($preset_colors) && empty($thumbnail_id)) {
            error_log('【前端】[ss_check_previews_status] 类目 "' . $term->name . '" (ID: ' . $term_id . ') 有预设颜色但没有产品类目图片，跳过SVG预览图生成');
            wp_send_json_error(['message' => 'Category has preset colors but no thumbnail image']);
            return;
        }

        // 【新增】检查是否启用并发处理
        $use_concurrent = get_option('ss_enable_concurrent_processing', true);

        error_log('[前端调试] 并发处理状态: ' . ($use_concurrent ? '启用' : '禁用'));
        error_log('[前端调试] 是否首次访问: ' . ($is_first_visit ? '是' : '否'));
        error_log('[前端调试] 加载阶段: ' . $loading_stage);

        if ($use_concurrent && $is_first_visit && $loading_stage === 1) {
            error_log('[前端] 首次访问且启用并发处理，启动全量SVG预览图生成');
            $result = $this->start_concurrent_preview_generation($term_id, $term, true); // 【修改】传递true参数表示生成所有预览图
            error_log('[前端调试] 全量生成启动结果: ' . ($result ? '成功' : '失败'));
        } else {
            error_log('[前端调试] 跳过全量生成 - 并发: ' . ($use_concurrent ? '是' : '否') . ', 首次: ' . ($is_first_visit ? '是' : '否') . ', 阶段: ' . $loading_stage);
        }

        // 从元数据检查是否已生成过预览
        $previously_generated = get_term_meta($term_id, 'ss_frontend_preview_generated', true);

        // 判断是否为首次访问(从前端参数或元数据判断)
        $is_first_time = $is_first_visit || empty($previously_generated);
        error_log('访问类型: ' . ($is_first_time ? '首次访问' : '二次访问') . ' (类目ID: ' . $term_id . ', 类目名称: ' . $term->name . ')');

        // 如果是首次访问，标记类目已通过前端生成SVG预览图
        if ($is_first_time) {
            $this->mark_category_frontend_preview_generated($term_id);
        }

        $category_name = $this->get_safe_category_name($term->name);
        $svg_files = $this->get_svg_files();

        // 【修复】确保upload_dir在使用前定义
        $upload_dir = wp_upload_dir();

        // 【修复】对于二次访问，进行快速预检查，统计已存在的预览图数量
        $quick_check_completed = 0;
        if (!$is_first_time && !empty($svg_files)) {
            error_log('二次访问检测：开始快速预检查已存在的预览图');
            foreach ($svg_files as $index => $file) {
                $file_name = basename($file);
                $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
                $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';

                if (file_exists($preview_path)) {
                    $quick_check_completed++;
                }
            }
            error_log('二次访问检测：快速预检查完成，已存在预览图数量: ' . $quick_check_completed . '/' . count($svg_files));
        }

        // 确保存在处理目录
        $processing_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processing/';
        $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/previews/';

        if (!file_exists($processing_dir)) {
            wp_mkdir_p($processing_dir);
        }
        if (!file_exists($preview_dir)) {
            wp_mkdir_p($preview_dir);
        }

        // 解析可见索引
        $indexes_to_check = [];
        if (!empty($visible_indexes)) {
            $indexes_to_check = array_map('intval', explode(',', $visible_indexes));
            error_log('检查可见索引: ' . implode(', ', $indexes_to_check) . ' (类目ID: ' . $term_id . ', 类目名称: ' . $term->name . ')');
        }

        // 【重构】解析索引信息
        $requested_indexes = [];
        $requested_display_indexes = [];

        if (!empty($visible_indexes)) {
            $requested_indexes = array_map('intval', explode(',', $visible_indexes));
        }

        if (!empty($display_indexes)) {
            $requested_display_indexes = array_map('intval', explode(',', $display_indexes));
        }

        error_log('请求检查的索引: original=' . implode(',', $requested_indexes) . ', display=' . implode(',', $requested_display_indexes) . ', stage=' . $loading_stage . ' (类目: ' . $term->name . ')');

        // 获取任务管理器实例
        $task_manager = SS_Preview_Task_Manager::get_instance();

        // 获取类目任务状态
        $task_status = $task_manager->get_category_tasks_status($term_id);
        error_log('类目任务状态: 待处理=' . $task_status['pending'] . ', 处理中=' . $task_status['in_progress'] . ', 已完成=' . $task_status['completed'] . ', 总计=' . $task_status['total']);

        // 【优化】只处理前端明确请求的索引
        $indexes_to_check = $requested_indexes;

        if (empty($indexes_to_check)) {
            error_log('没有要检查的索引，返回空结果');
            wp_send_json_success([
                'previews' => [],
                'completed_count' => 0,
                'total_count' => 0,
                'is_first_visit' => $is_first_time,
                'task_status' => []
            ]);
            return;
        }

        // 处理请求的索引
        $previews = [];
        $current_batch_completed = 0;

        foreach ($indexes_to_check as $array_index => $index) {
            if (!isset($svg_files[$index])) {
                error_log('索引不存在: ' . $index);
                continue;
            }

            $file = $svg_files[$index];
            $file_name = basename($file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

            // 【修复】检查是否为自定义匹配类目，使用专用路径
            $is_custom_matching = get_term_meta($term_id, '_is_custom_matching_category', true);
            if (!empty($is_custom_matching)) {
                $term_obj = get_term($term_id);
                $category_slug = $term_obj->slug;
                $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $safe_name . '_preview.webp';
                $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $safe_name . '_preview.webp';
                error_log('【自定义匹配】使用专用预览路径: ' . $preview_path);
            } else {
                // 普通类目使用frontend-gallery路径
                $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';
                $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';
            }

            $status = 'pending';

            // 【重构】基于display-index判断优先级，而不是原始index
            $display_index = isset($requested_display_indexes[$array_index]) ? $requested_display_indexes[$array_index] : 999;
            $is_high_priority = $display_index < 8; // 前8张显示的图片为高优先级

            error_log('处理索引: original=' . $index . ', display=' . $display_index . ', 高优先级=' . ($is_high_priority ? '是' : '否'));

            if (file_exists($preview_path)) {
                $status = 'completed';
                error_log('预览已存在: index=' . $index . ', file=' . $file_name . ' (已完成)');
            } else {
                $processing_marker = $processing_dir . $category_name . '_' . $safe_name . '.processing';

                if (file_exists($processing_marker)) {
                    // 检查处理标记是否已超时
                    $marker_time = (int)file_get_contents($processing_marker);
                    // 【修复】缩短超时时间从5分钟到2分钟，提高响应速度
                    if (time() - $marker_time > 120) { // 2分钟超时
                        error_log('处理标记超时，重新添加到队列: ' . basename($file) . ' (超时时间: ' . (time() - $marker_time) . '秒)');
                        @unlink($processing_marker);
                        // 重新添加到队列，使用高优先级确保快速处理
                        $priority = 'high'; // 【修复】超时重试时使用高优先级
                        $this->queue_preview_generation($category_name, $file, $index, $priority);
                        $status = 'pending';
                    } else {
                        $status = 'processing';
                        $elapsed = time() - $marker_time;
                        error_log('预览正在处理: index=' . $index . ', file=' . $file_name . ' (已处理' . $elapsed . '秒)');
                    }
                } else {
                    // 【优化】判断是否需要快速处理
                    // 1. 前8张图始终快速处理
                    // 2. 二次访问时，所有缺失图片都快速处理（不限于前8张）
                    $should_fast_process = $is_high_priority || (!$is_first_time && $quick_check_completed > 0);

                    if ($should_fast_process) {
                        $reason = $is_high_priority ? '前8张显示图片' : '二次访问所有缺失图片';
                        error_log($reason . '直接同步处理: original-index=' . $index . ', display-index=' . $display_index . ', file=' . $file_name);

                        // 获取预设颜色
                        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
                        if (empty($preset_colors)) {
                            $preset_colors = $this->extract_colors_from_category_image($term_id);
                        }

                        if (!empty($preset_colors)) {
                            // 直接同步生成预览
                            $result = $this->generate_svg_preview_file($file, $category_name, $preset_colors[0], true, $term_id);
                            if ($result) {
                                error_log('前8张显示图片同步生成成功: original-index=' . $index . ', display-index=' . $display_index . ', file=' . $file_name);
                                $status = 'completed'; // 立即标记为完成
                            } else {
                                error_log('前8张显示图片同步生成失败: original-index=' . $index . ', display-index=' . $display_index . ', file=' . $file_name);
                                $status = 'processing'; // 标记为处理中，稍后重试
                            }
                        } else {
                            error_log('前8张显示图片无法获取颜色，跳过: original-index=' . $index . ', display-index=' . $display_index . ', file=' . $file_name);
                            continue; // 跳过没有颜色的文件
                        }
                    } else {
                        // 其他图片添加到处理队列
                        $priority = $is_first_time ? 'normal' : 'high';
                        $result = $this->queue_preview_generation($category_name, $file, $index, $priority);
                        error_log('添加到队列(' . $priority . '优先级): index=' . $index . ', file=' . $file_name . ', 结果=' . ($result ? '成功' : '失败'));

                        // 如果添加失败，尝试直接生成
                        if (!$result) {
                            error_log('添加到队列失败，尝试直接生成: index=' . $index . ', file=' . $file_name);
                            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
                            if (empty($preset_colors)) {
                                $preset_colors = $this->extract_colors_from_category_image($term_id);
                            }
                            if (empty($preset_colors)) {
                                error_log('添加到队列失败且无法提取颜色，跳过: ' . $file_name);
                                continue; // 跳过没有颜色的文件
                            }
                            $this->generate_svg_preview_file($file, $category_name, $preset_colors[0], true, $term_id);
                        }
                    }
                }
            }

            // 【优化】重新检查文件是否存在（可能在同步处理中刚刚生成）
            if (file_exists($preview_path)) {
                $status = 'completed';
                $current_batch_completed++;
            }

            // 二次访问时，确保即使状态为'pending'或'processing'，只要文件存在就返回URL
            if (!$is_first_time && file_exists($preview_path)) {
                $previews[] = [
                    'index' => $index,
                    'file' => $file_name,
                    'status' => 'completed', // 强制标记为已完成
                    'preview_url' => $preview_url,
                ];
            } else {
                $previews[] = [
                    'index' => $index,
                    'file' => $file_name,
                    'status' => $status,
                    'preview_url' => ($status === 'completed') ? $preview_url : '',
                ];
            }
        }

        // 【优化】返回当前批次的进度，而不是所有文件的进度
        $current_batch_total = count($indexes_to_check);

        error_log('当前批次进度: ' . $current_batch_completed . '/' . $current_batch_total . ' (类目: ' . $term->name . ', 请求索引: ' . implode(',', $indexes_to_check) . ')');

        wp_send_json_success([
            'previews' => $previews,
            'completed_count' => $current_batch_completed,
            'total_count' => $current_batch_total,
            'is_first_visit' => $is_first_time,
            'task_status' => $task_status,
            'requested_indexes' => $indexes_to_check // 添加请求的索引信息，便于前端调试
        ]);
    }

    /**
     * 将预览添加到生成队列 - 使用任务管理器实现并行处理
     */
    private function queue_preview_generation($category_name, $svg_file, $index, $priority = 'normal') {
        $upload_dir = wp_upload_dir();
        $file_name = basename($svg_file);
        $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

        // 创建处理标记目录
        $processing_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processing/';
        // 【修复】使用新的预览图路径结构
        $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';

        if (!file_exists($processing_dir)) {
            $created = wp_mkdir_p($processing_dir);
            if (!$created) {
                error_log('无法创建处理目录: ' . $processing_dir);
                return false;
            }
        }

        // 【修复】检查预览是否已存在 - 使用新的文件名格式
        $preview_path = $preview_dir . $safe_name . '_preview.webp';
        if (file_exists($preview_path)) {
            error_log('预览已存在无需处理: ' . $category_name . '_' . $safe_name);
            return true; // 预览已存在，无需处理
        }

        // 创建处理标记文件
        $processing_marker = $processing_dir . $category_name . '_' . $safe_name . '.processing';

        // 如果标记不存在，则添加到队列
        if (!file_exists($processing_marker)) {
            // 创建标记文件（使用权限修复函数）
            $result = $this->create_file_with_permissions($processing_marker, time());
            if (!$result) {
                error_log('无法创建处理标记: ' . $processing_marker);
                return false;
            }

            // 首先尝试直接使用name查找
            $term_args = array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'name' => str_replace('_', ' ', $category_name)
            );

            $terms = get_terms($term_args);
            $term_id = 0;

            // 如果找不到，尝试使用其他方法
            if (empty($terms) || is_wp_error($terms)) {
                error_log('尝试使用slug查找类目: ' . $category_name);

                // 尝试使用slug查找
                $unsanitized_name = str_replace('_', '-', $category_name);
                $term = get_term_by('slug', $unsanitized_name, 'product_cat');

                if ($term && !is_wp_error($term)) {
                    $term_id = $term->term_id;
                    error_log('通过slug找到类目: ' . $term->name . ' (ID: ' . $term_id . ')');
                } else {
                    // 尝试模糊匹配
                    error_log('尝试模糊匹配类目名称: ' . $category_name);
                    $partial_name = str_replace('_', ' ', $category_name);

                    $like_terms = get_terms(array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => false,
                        'name__like' => $partial_name
                    ));

                    if (!empty($like_terms) && !is_wp_error($like_terms)) {
                        foreach ($like_terms as $like_term) {
                            error_log('找到潜在匹配类目: ' . $like_term->name . ' (ID: ' . $like_term->term_id . ')');
                            $safe_like_term = $this->get_safe_category_name($like_term->name);

                            if ($safe_like_term === $category_name) {
                                $term_id = $like_term->term_id;
                                error_log('匹配成功！使用类目: ' . $like_term->name . ' (ID: ' . $term_id . ')');
                                break;
                            }
                        }
                    }
                }
            } else {
                $term_id = $terms[0]->term_id;
                error_log('直接找到类目: ' . $terms[0]->name . ' (ID: ' . $term_id . ')');
            }

            // 如果找不到类目ID，记录错误并返回
            if ($term_id === 0) {
                error_log('无法找到匹配的类目ID，无法添加到任务队列: ' . $category_name);
                @unlink($processing_marker); // 删除处理标记
                return false;
            }

            // 使用任务管理器添加预览任务
            $task_manager = SS_Preview_Task_Manager::get_instance();

            // 添加调试日志
            error_log('【调试】尝试添加预览任务: 类目=' . $category_name . ', 文件=' . basename($svg_file));

            // 获取类目预设颜色和缩略图ID
            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
            $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

            // 检查是否有预设颜色但没有产品类目图片
            if (!empty($preset_colors) && empty($thumbnail_id)) {
                error_log('【前端】[queue_preview_generation] 类目 "' . $term->name . '" (ID: ' . $term_id . ') 有预设颜色但没有产品类目图片，跳过SVG预览图生成');
                @unlink($processing_marker); // 删除处理标记
                return false;
            }

            if (empty($preset_colors)) {
                error_log('【调试】类目没有预设颜色，尝试从图片提取');
                $preset_colors = $this->extract_colors_from_category_image($term_id);
            }
            if (empty($preset_colors)) {
                error_log('【调试】无法提取颜色，跳过该SVG');
                @unlink($processing_marker); // 删除处理标记
                return false; // 跳过没有颜色的SVG
            } else {
                error_log('【调试】使用颜色: ' . implode(', ', $preset_colors));
            }

            // 不再对已有的预设颜色进行处理，因为它们已经通过颜色分析类处理过
            // 只记录日志，不做额外处理
            if (!empty($preset_colors)) {
                error_log('【调试】使用已有的预设颜色，不再重复处理: ' . implode(', ', $preset_colors));
            }

            // 检查是否需要并行处理
            $parallel_processing = true; // 启用并行处理

            // 如果是同一个类目的第一个SVG，直接处理以提高响应速度
            $is_first_svg = ($index === 0);

            if ($is_first_svg) {
                error_log('【调试】这是类目的第一个SVG，尝试直接处理以提高响应速度');
                $direct_result = $this->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);

                if ($direct_result) {
                    error_log('【调试】直接处理第一个SVG预览成功: ' . basename($svg_file));
                    // 标记类目已生成SVG预览图
                    $mark_result = $this->mark_category_frontend_preview_generated($term_id);
                    error_log('【调试】标记类目已生成SVG预览图: ' . ($mark_result ? '成功' : '失败'));
                    return true;
                } else {
                    error_log('【调试】直接处理第一个SVG预览失败');
                }
            }

            // 检查Action Scheduler数据库表是否存在
            global $wpdb;
            $table_name = $wpdb->prefix . 'actionscheduler_actions';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            // 如果Action Scheduler数据库表不存在，强制使用直接处理
            if (!$table_exists) {
                error_log('【调试】Action Scheduler数据库表不存在，强制使用直接处理');
                $parallel_processing = false;
            }

            // 检查是否有足够的任务槽位可用
            if ($parallel_processing) {
                // 获取当前正在处理的任务数量
                $task_manager = SS_Preview_Task_Manager::get_instance();
                $all_tasks_status = $task_manager->get_all_tasks_status();
                $current_tasks = $all_tasks_status['pending'] + $all_tasks_status['in_progress'];
                $max_tasks = $task_manager->get_max_concurrent_tasks();

                // 如果当前任务数量接近最大值，暂时使用直接处理
                if ($current_tasks >= $max_tasks * 0.8) {
                    error_log('【调试】当前任务数量(' . $current_tasks . ')接近最大值(' . $max_tasks . ')，暂时使用直接处理');
                    $parallel_processing = false;
                } else {
                    error_log('【调试】当前任务数量: ' . $current_tasks . '/' . $max_tasks . '，使用并行处理');
                }

                // 检查是否有足够的内存可用
                $memory_limit = ini_get('memory_limit');
                $memory_limit_bytes = $this->return_bytes($memory_limit);
                $memory_usage = memory_get_usage(true);
                $memory_usage_percent = ($memory_usage / $memory_limit_bytes) * 100;

                error_log('【调试】当前内存使用: ' . round($memory_usage_percent, 2) . '% (' . $this->format_bytes($memory_usage) . '/' . $memory_limit . ')');

                // 如果内存使用超过70%，暂时使用直接处理
                if ($memory_usage_percent > 70) {
                    error_log('【调试】内存使用过高，暂时使用直接处理');
                    $parallel_processing = false;
                }
            }

            // 使用Action Scheduler进行并行处理
            if ($parallel_processing) {
                error_log('【调试】使用Action Scheduler进行并行处理: ' . basename($svg_file));

                // 添加调试信息
                error_log('【调试】Action Scheduler状态: ' . (function_exists('as_schedule_single_action') ? '可用' : '不可用'));

                // 使用任务管理器添加任务
                $task_id = $task_manager->add_preview_task($category_name, $svg_file, $index, $term_id, $priority);

                if ($task_id) {
                    error_log('【调试】成功添加预览任务: ID=' . $task_id . ', 类目=' . $category_name . ', 文件=' . basename($svg_file));

                    // 尝试立即运行Action Scheduler
                    if (function_exists('as_schedule_single_action')) {
                        error_log('【调试】尝试立即运行Action Scheduler');
                        // 使用WordPress原生调度立即触发Action Scheduler运行
                        wp_schedule_single_event(time(), 'action_scheduler_run_queue');
                    }

                    // 尝试直接执行当前任务
                    error_log('【调试】尝试直接执行当前任务: ' . $task_id);
                    try {
                        // 获取任务详情
                        global $wpdb;
                        $task_details = $wpdb->get_row($wpdb->prepare(
                            "SELECT hook, args FROM {$wpdb->prefix}actionscheduler_actions WHERE action_id = %d",
                            $task_id
                        ));

                        if ($task_details && $task_details->hook === 'ss_process_svg_preview') {
                            error_log('【调试】找到任务详情，准备直接执行');
                            $args = maybe_unserialize($task_details->args);
                            if (is_array($args) && count($args) >= 4) {
                                error_log('【调试】直接执行任务: ' . basename($args[0]));
                                // 直接执行任务
                                $task_manager->process_svg_preview($args[0], $args[1], $args[2], $args[3]);

                                // 标记任务为已完成
                                if (function_exists('as_mark_complete_action')) {
                                    as_mark_complete_action($task_id);
                                    error_log('【调试】标记任务为已完成: ' . $task_id);
                                }
                            } else {
                                error_log('【调试】任务参数无效: ' . print_r($args, true));
                            }
                        } else {
                            error_log('【调试】未找到任务详情或钩子不匹配');
                        }
                    } catch (Exception $e) {
                        error_log('【调试】直接执行任务时出错: ' . $e->getMessage());
                    }

                    return true;
                } else {
                    error_log('【调试】添加预览任务失败，尝试直接处理: ' . basename($svg_file));

                    // 如果添加任务失败，尝试直接处理
                    $direct_result = $this->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);

                    if ($direct_result) {
                        error_log('【调试】直接处理SVG预览成功: ' . basename($svg_file));
                        // 标记类目已生成SVG预览图
                        $mark_result = $this->mark_category_frontend_preview_generated($term_id);
                        error_log('【调试】标记类目已生成SVG预览图: ' . ($mark_result ? '成功' : '失败'));
                        return true;
                    } else {
                        error_log('【调试】直接处理SVG预览也失败: ' . basename($svg_file));
                        @unlink($processing_marker); // 删除处理标记
                        return false;
                    }
                }
            } else {
                // 不使用并行处理，直接处理SVG预览
                error_log('【调试】不使用并行处理，直接处理SVG预览: ' . basename($svg_file));
                $direct_result = $this->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);

                if ($direct_result) {
                    error_log('【调试】直接处理SVG预览成功: ' . basename($svg_file));
                    // 标记类目已生成SVG预览图
                    $mark_result = $this->mark_category_frontend_preview_generated($term_id);
                    error_log('【调试】标记类目已生成SVG预览图: ' . ($mark_result ? '成功' : '失败'));
                    return true;
                } else {
                    error_log('【调试】直接处理SVG预览失败: ' . basename($svg_file));
                    @unlink($processing_marker); // 删除处理标记
                    return false;
                }
            }
        } else {
            // 处理标记存在，检查是否已经超时
            $marker_time = (int)file_get_contents($processing_marker);
            // 【修复】缩短超时时间从5分钟到2分钟
            if (time() - $marker_time > 120) { // 2分钟超时
                $elapsed = time() - $marker_time;
                error_log('处理标记超时，重新添加到队列: ' . basename($svg_file) . ' (超时时间: ' . $elapsed . '秒)');
                @unlink($processing_marker);
                // 递归调用自身，重新添加到队列，使用高优先级
                return $this->queue_preview_generation($category_name, $svg_file, $index, 'high');
            }
        }

        return true;
    }

    /**
     * 处理生成预览队列 - 优化并记录生成数量
     */
    public function ss_process_preview_queue() {
        $upload_dir = wp_upload_dir();
        $processing_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processing/';

        // 确保目录存在
        if (!file_exists($processing_dir)) {
            return;
        }

        // 获取所有处理标记
        $markers = glob($processing_dir . '*.processing');

        // 增加同时处理数量 - 为前8个项目提供更多并发
        $max_concurrent = 15; // 增加并发数量
        $processed = 0;

        // 记录每个分类处理的文件数量
        $category_process_count = array();

        foreach ($markers as $marker) {
            if ($processed >= $max_concurrent) {
                break;
            }

            // 从标记文件名获取信息
            $filename = basename($marker, '.processing');
            list($category_name, $svg_name) = explode('_', $filename, 2);

            // 获取类目ID
            $term_id = 0;
            $term_args = array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'name__like' => str_replace('_', ' ', $category_name)
            );

            $terms = get_terms($term_args);
            if (!empty($terms) && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    if ($this->get_safe_category_name($term->name) === $category_name) {
                        $term_id = $term->term_id;
                        break;
                    }
                }
            }

            // 寻找匹配的SVG文件
            $svg_file = $upload_dir['basedir'] . '/shoe-svg-generator/svg/' . $svg_name . '.svg';

            if (file_exists($svg_file) && $term_id > 0) {
                // 增加分类处理计数
                if (!isset($category_process_count[$term_id])) {
                    $category_process_count[$term_id] = 0;
                }
                $category_process_count[$term_id]++;

                // 获取预设颜色和缩略图ID
                $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
                $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

                // 检查是否有预设颜色但没有产品类目图片
                if (!empty($preset_colors) && empty($thumbnail_id)) {
                    error_log('【前端】[ss_process_preview_queue] 类目 "' . $term->name . '" (ID: ' . $term_id . ') 有预设颜色但没有产品类目图片，跳过SVG预览图生成');
                    @unlink($marker); // 删除处理标记
                    continue; // 跳过此SVG文件的处理
                }

                if (empty($preset_colors)) {
                    $preset_colors = $this->extract_colors_from_category_image($term_id);
                }

                if (empty($preset_colors)) {
                    error_log('【前端】[ss_process_preview_queue] 类目没有颜色，跳过SVG: ' . basename($svg_file));
                    @unlink($marker); // 删除处理标记
                    continue; // 跳过没有颜色的SVG
                }

                // 生成预览，启用颜色分析类，并传递类目ID
                $this->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);

                // 删除处理标记
                @unlink($marker);

                $processed++;
            }
        }

        // 更新每个类目的处理计数
        foreach ($category_process_count as $term_id => $count) {
            $current_count = get_term_meta($term_id, 'ss_frontend_preview_count', true);
            if (!is_numeric($current_count)) {
                $current_count = 0;
            }

            $new_count = $current_count + $count;
            update_term_meta($term_id, 'ss_frontend_preview_count', $new_count);

            $term = get_term($term_id, 'product_cat');
            // error_log('类目 "' . $term->name . '" (ID: ' . $term_id . ') 已处理 ' . $new_count . ' 个预览图');
        }

        // 如果还有更多标记，立即重新触发队列处理
        if (count($markers) > $processed) {
            wp_schedule_single_event(time() + 1, 'ss_process_preview_queue');
        }
    }

    /**
     * 添加自定义cron间隔
     */
    public function ss_add_cron_interval($schedules) {
        $schedules['ss_every_minute'] = array(
            'interval' => 60,
            'display'  => __('Every Minute')
        );
        return $schedules;
    }

    /**
     * 标记类目已通过前端生成SVG预览图
     *
     * @param int $term_id 类目ID
     * @return bool 是否成功标记
     */
    public function mark_category_frontend_preview_generated($term_id) {
        // 获取类目信息
        $term = get_term($term_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            // error_log("[冲突检测] 无法获取类目信息，ID: " . $term_id);
            return false;
        }

        // 检查是否已通过WP CLI生成过产品
        $wp_cli_generated = get_term_meta($term_id, 'ss_wp_cli_products_generated', true);
        if (!empty($wp_cli_generated)) {
            // error_log("[冲突检测] 类目 '" . $term->name . "' (ID: " . $term_id . ") 已通过WP CLI生成产品，不应生成SVG预览图");
            return false;
        }

        // 标记类目已生成SVG预览图
        update_term_meta($term_id, 'ss_frontend_preview_generated', current_time('timestamp'));
        // error_log("[冲突检测] 已标记类目 '" . $term->name . "' (ID: " . $term_id . ") 已生成SVG预览图");

        return true;
    }

    /**
     * 重新生成预览
     */
    public function ss_regenerate_preview() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;
        $index = isset($_POST['index']) ? intval($_POST['index']) : 0;

        if ($term_id === 0) {
            wp_send_json_error(['message' => 'Invalid term ID']);
            return;
        }

        // 获取分类数据
        $term = get_term($term_id);
        $category_name = $this->get_safe_category_name($term->name);
        $svg_files = $this->get_svg_files();

        if (isset($svg_files[$index])) {
            $svg_file = $svg_files[$index];
            $file_name = basename($svg_file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

            // 【优化】删除现有预览 - 使用新的预览图路径 frontend-gallery
            $upload_dir = wp_upload_dir();
            $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';

            if (file_exists($preview_path)) {
                @unlink($preview_path);
            }

            // 添加到生成队列，并传递类目ID
            $this->queue_preview_generation($category_name, $svg_file, $index, 'normal', $term_id);

            wp_send_json_success();
        } else {
            wp_send_json_error(['message' => 'Invalid index']);
        }
    }

    /**
     * 异步生成预览 - 修复
     */
    public function ss_generate_preview_async($svg_file, $category_name, $index, $term_id = 0) {
        // error_log('异步生成预览：文件=' . basename($svg_file) . ' 类目=' . $category_name . ' term_id=' . $term_id);

        // 如果提供了term_id，直接使用
        if ($term_id > 0) {
            $term = get_term($term_id, 'product_cat');
            if ($term && !is_wp_error($term)) {
                // error_log('使用提供的term_id找到类目: ' . $term->name);
            } else {
                // error_log('无法使用提供的term_id找到类目，尝试其他方法');
                $term_id = 0;
            }
        }

        // 如果没有提供term_id或者提供的无效，尝试查找
        if ($term_id === 0) {
            // 首先尝试直接使用name查找
            $term_args = array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'name' => str_replace('_', ' ', $category_name)
            );

            $terms = get_terms($term_args);

            if (!empty($terms) && !is_wp_error($terms)) {
                $term = $terms[0];
                $term_id = $term->term_id;
                // error_log('直接找到类目: ' . $term->name . ' (ID: ' . $term_id . ')');
            } else {
                // 尝试模糊匹配
                $partial_name = str_replace('_', ' ', $category_name);

                $like_terms = get_terms(array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => false,
                    'name__like' => $partial_name
                ));

                if (!empty($like_terms) && !is_wp_error($like_terms)) {
                    foreach ($like_terms as $like_term) {
                        $safe_like_term = $this->get_safe_category_name($like_term->name);

                        if ($safe_like_term === $category_name) {
                            $term = $like_term;
                            $term_id = $like_term->term_id;
                            // error_log('通过模糊匹配找到类目: ' . $like_term->name . ' (ID: ' . $term_id . ')');
                            break;
                        }
                    }
                }
            }
        }

        // 如果找到了term_id，获取颜色
        $color = null; // 不使用默认颜色

        if ($term_id > 0) {
            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);

            if (!empty($preset_colors) && is_array($preset_colors)) {
                $color = $preset_colors[0];
                error_log('使用类目预设颜色: ' . $color);
            } else {
                // 确保使用与后端相同的参数提取颜色
                $extracted_colors = $this->extract_colors_from_category_image($term_id); // 已修改为使用相同参数
                if (!empty($extracted_colors)) {
                    $color = $extracted_colors[0];
                    error_log('使用从图片提取的颜色: ' . $color);
                }
            }

            // 只有当没有预设颜色时，才使用提取的颜色
            if (empty($preset_colors) && !empty($extracted_colors)) {
                // 直接使用从图片提取的颜色
                $preset_colors = $extracted_colors;
                error_log('使用从图片提取的颜色: ' . implode(', ', $preset_colors));

                // 更新类目的预设颜色
                if ($term_id > 0) {
                    update_term_meta($term_id, 'ss_category_colors', $preset_colors);
                    error_log('更新类目预设颜色: ' . implode(', ', $preset_colors));
                }
            } else if (!empty($preset_colors)) {
                error_log('使用已有的预设颜色，不再重复处理: ' . implode(', ', $preset_colors));
            }
        } else {
            // error_log('无法找到类目，使用默认颜色: ' . $color);
        }

        // 生成预览，启用颜色分析类，并传递类目ID
        $result = $this->generate_svg_preview_file($svg_file, $category_name, $color, true, $term_id);

        // 记录生成结果
        if ($result) {
            // error_log('成功生成预览: ' . basename($svg_file) . ' 类别: ' . $category_name);
        } else {
            // error_log('生成预览失败: ' . basename($svg_file) . ' 类别: ' . $category_name);
        }

        // 清除处理标记
        $upload_dir = wp_upload_dir();
        $file_name = basename($svg_file);
        $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
        $processing_marker = $upload_dir['basedir'] . '/shoe-svg-generator/processing/' .
                          $category_name . '_' . $safe_name . '.processing';

        if (file_exists($processing_marker)) {
            @unlink($processing_marker);
        }
    }

    /**
     * 生成SVG预览文件 - 使用Python脚本处理SVG填色并添加类目标记
     *
     * @param string $svg_file SVG文件路径
     * @param string $category_name 分类名称
     * @param string $color 主颜色
     * @param bool $use_color_analyzer 是否使用颜色分析类生成变体颜色
     * @param int $provided_term_id 可选的类目ID，如果提供则直接使用，不再查询
     * @return string|bool 成功返回预览URL，失败返回false
     */
    public function generate_svg_preview_file($svg_file, $category_name, $color = null, $use_color_analyzer = true, $provided_term_id = 0) {
        // 检查是否有预设颜色但没有产品类目图片
        if ($provided_term_id > 0) {
            $preset_colors = get_term_meta($provided_term_id, 'ss_category_colors', true);
            $thumbnail_id = get_term_meta($provided_term_id, 'thumbnail_id', true);

            if (!empty($preset_colors) && empty($thumbnail_id)) {
                $term = get_term($provided_term_id);
                error_log('【前端】[generate_svg_preview_file] 类目 "' . $term->name . '" (ID: ' . $provided_term_id . ') 有预设颜色但没有产品类目图片，跳过SVG预览图生成');
                return false; // 跳过SVG预览图生成
            }
        }

        // 【优化】确保目标目录存在 - 使用新的类目专属预览目录结构
        $upload_dir = wp_upload_dir();

        // 【模块4】检查是否为自定义匹配类目，使用专用路径
        $is_custom_matching = false;
        $category_slug = '';
        if ($provided_term_id) {
            $is_custom_matching = get_term_meta($provided_term_id, '_is_custom_matching_category', true);
            if (!empty($is_custom_matching)) {
                $term = get_term($provided_term_id);
                $category_slug = $term->slug;
            }
        }

        if (!empty($is_custom_matching) && !empty($category_slug)) {
            // 自定义匹配类目使用专用路径，每个类目有独立文件夹
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/';
            error_log('【模块4】自定义匹配类目使用专用预览目录: ' . $preview_dir);
        } else {
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
        }

        $temp_dir = $upload_dir['basedir'] . '/shoe-svg-generator/temp/';

        // 【修复】根据类目类型设置处理后SVG目录
        if (!empty($is_custom_matching) && !empty($category_slug)) {
            // 自定义匹配类目使用专用路径
            $processed_svg_base_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/svg/';
            error_log('【修复】自定义匹配类目使用专用SVG目录: ' . $processed_svg_base_dir);
        } else {
            // 普通类目使用默认路径
            $processed_svg_base_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
        }

        // 添加调试日志
        error_log('【调试】开始检查目录结构');
        error_log('【调试】预览目录: ' . $preview_dir . ' (存在: ' . (file_exists($preview_dir) ? '是' : '否') . ')');
        error_log('【调试】临时目录: ' . $temp_dir . ' (存在: ' . (file_exists($temp_dir) ? '是' : '否') . ')');
        error_log('【调试】处理后SVG基础目录: ' . $processed_svg_base_dir . ' (存在: ' . (file_exists($processed_svg_base_dir) ? '是' : '否') . ')');

        // 确保完整的类目目录结构存在并设置正确权限
        error_log('【调试】创建完整类目目录结构');
        $this->create_category_directory_structure($category_name);

        if (!file_exists($temp_dir)) {
            error_log('【调试】创建临时目录');
            $this->create_directory_with_permissions($temp_dir);
        } else {
            $this->ensure_www_permissions($temp_dir, false);
        }

        if (!file_exists($processed_svg_base_dir)) {
            error_log('【调试】创建处理后SVG基础目录');
            $this->create_directory_with_permissions($processed_svg_base_dir);
        } else {
            $this->ensure_www_permissions($processed_svg_base_dir, false);
        }

        // 获取更友好的类目名称用于目录命名
        $category_term_name = '';
        $term_args = array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'name__like' => str_replace('_', ' ', $category_name)
        );

        $terms = get_terms($term_args);
        if (!empty($terms) && !is_wp_error($terms)) {
            foreach ($terms as $term) {
                if ($this->get_safe_category_name($term->name) === $category_name) {
                    $category_term_name = str_replace(' ', '-', $term->name);
                    break;
                }
            }
        }

        // 如果无法获取类目名称，则使用安全的类目名称
        if (empty($category_term_name)) {
            $category_term_name = str_replace('_', '-', $category_name);
        }

        // 【修复】根据类目类型创建特定目录
        if (!empty($is_custom_matching) && !empty($category_slug)) {
            // 自定义匹配类目直接使用slug作为目录名
            $category_svg_dir = $processed_svg_base_dir . $category_slug . '/';
            error_log('【修复】自定义匹配类目SVG目录: ' . $category_svg_dir . ' (存在: ' . (file_exists($category_svg_dir) ? '是' : '否') . ')');
        } else {
            // 普通类目使用原有逻辑
            $category_svg_dir = $processed_svg_base_dir . $category_term_name . '/';
            error_log('【调试】普通类目SVG目录: ' . $category_svg_dir . ' (存在: ' . (file_exists($category_svg_dir) ? '是' : '否') . ')');
        }

        if (!file_exists($category_svg_dir)) {
            error_log('【调试】创建类目特定目录');
            $this->create_directory_with_permissions($category_svg_dir);
        } else {
            $this->ensure_www_permissions($category_svg_dir, false);
        }

        // 创建必要的目录（使用权限修复函数）
        foreach ([$preview_dir, $temp_dir] as $dir) {
            if (!file_exists($dir)) {
                $this->create_directory_with_permissions($dir);
            } else {
                $this->ensure_www_permissions($dir, false);
            }
        }

        // 处理文件名
        $file_name = basename($svg_file);
        $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

        // 【优化】生成新的预览文件路径和URL - 移除类目前缀，直接使用文件名
        $preview_file = $preview_dir . $safe_name . '_preview.webp';

        // 【模块4】根据类目类型生成对应的URL
        if (!empty($is_custom_matching) && !empty($category_slug)) {
            $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $safe_name . '_preview.webp';
        } else {
            $preview_url = $upload_dir['baseurl'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/' . $safe_name . '_preview.webp';
        }

        // 如果预览已存在，直接返回URL
        if (file_exists($preview_file)) {
            // error_log('预览文件已存在，直接返回URL: ' . $preview_file);
            return $preview_url;
        }

        // error_log('开始生成预览文件: ' . $preview_file . ' (类目: ' . $category_name . ', 颜色: ' . $color . ')');

        // 获取分类ID，用于添加标记
        $term_id = $provided_term_id; // 首先使用提供的类目ID

        // 如果没有提供类目ID，则尝试查找
        if ($term_id <= 0) {
            error_log('【调试】未提供类目ID，尝试根据类目名称查找: ' . $category_name);
            $term_args = array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'name__like' => str_replace('_', ' ', $category_name)
            );

            $terms = get_terms($term_args);
            if (!empty($terms) && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    if ($this->get_safe_category_name($term->name) === $category_name) {
                        $term_id = $term->term_id;
                        error_log('【调试】找到匹配的类目ID: ' . $term_id . ' (类目名称: ' . $term->name . ')');
                        break;
                    }
                }
            }

            if ($term_id <= 0) {
                error_log('【调试】无法根据类目名称找到匹配的类目ID: ' . $category_name);
            }
        } else {
            error_log('【调试】使用提供的类目ID: ' . $term_id);
        }

        // 获取类目的颜色设置（可能是多个颜色）
        $colors = [$color]; // 默认只有一个颜色

        // 首先尝试获取预设颜色
        if ($term_id > 0) {
            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
            if (!empty($preset_colors) && is_array($preset_colors)) {
                // 【双色排列方案】生成双色排列并随机选择（默认开启）
                $enable_dual_shuffle = get_option('ss_enable_dual_color_shuffle', true); // 默认true
                $shuffle_probability = (int) get_option('ss_dual_shuffle_probability', 50);
                
                if ($enable_dual_shuffle && count($preset_colors) >= 5) {
                    // 加载Color Science识别器以使用双色排列功能
                    $color_recognizer = SS_Color_Science_Recognizer::get_instance();
                    
                    // 生成双色排列方案
                    $color_schemes = $color_recognizer->generate_dual_color_schemes($preset_colors);
                    
                    // 使用配置的概率和优化方法进行选择
                    $colors = $color_recognizer->select_random_color_scheme($color_schemes[0], $color_schemes[1], $shuffle_probability);
                } else {
                    $colors = $preset_colors;
                    if (!$enable_dual_shuffle) {
                        error_log('【双色排列方案】双色排列功能未启用，使用原始颜色: ' . implode(', ', $colors));
                    } else {
                        error_log('使用类目预设颜色: ' . implode(', ', $colors));
                    }
                }
            }
            // 【修复】只有当没有预设颜色且启用颜色分析类时，才从图片提取颜色
            else if ($use_color_analyzer && !empty($color) && $color !== 'null') {
                // 【修复】尝试从类目图片提取颜色，使用缓存避免重复提取
                $extracted_colors = $this->extract_colors_from_category_image($term_id);

                if (!empty($extracted_colors) && is_array($extracted_colors)) {
                    // 【双色排列方案】如果提取到5个颜色，应用双色排列（默认开启）
                    $enable_dual_shuffle = get_option('ss_enable_dual_color_shuffle', true); // 默认true
                    $shuffle_probability = (int) get_option('ss_dual_shuffle_probability', 50);
                    
                    if ($enable_dual_shuffle && count($extracted_colors) >= 5) {
                        $color_recognizer = SS_Color_Science_Recognizer::get_instance();
                        $color_schemes = $color_recognizer->generate_dual_color_schemes($extracted_colors);
                        
                        // 使用配置的概率和优化方法进行选择
                        $colors = $color_recognizer->select_random_color_scheme($color_schemes[0], $color_schemes[1], $shuffle_probability);
                    } else {
                        $colors = $extracted_colors;
                        if ($enable_dual_shuffle) {
                            error_log('没有预设颜色，从类目图片提取颜色: ' . implode(', ', $colors));
                        }
                    }
                    // 【修复】不再重复更新，extract_colors_from_category_image已经处理了缓存
                } else {
                    // 如果无法从图片提取颜色，跳过该SVG
                    error_log('无法从类目图片提取颜色，跳过该SVG');
                    return false; // 跳过没有颜色的SVG
                }
            }
        }
        // 如果没有找到类目ID，跳过处理
        else if ($use_color_analyzer && !empty($color) && $color !== 'null') {
            error_log('无类目ID，跳过SVG处理');
            return false; // 跳过没有类目ID的SVG
        }

        // 去掉颜色中的#号（Python脚本会自动添加）
        $color_params = array_map(function($c) {
            return ltrim($c, '#');
        }, $colors);

        // 修改：使用类目目录保存处理后的SVG，而不是临时目录
        $processed_svg_file = $category_svg_dir . $safe_name . '.svg';
        // 临时PNG文件路径
        $temp_png_path = $temp_dir . 'temp_' . $safe_name . '_' . uniqid() . '.png';

        try {
            // 【修复】调用改进的Python脚本处理SVG填色
            $python_script = WP_PLUGIN_DIR . '/shoe-svg-generator/python/process_svg_fixed.py';
            $color_str = implode(',', $color_params);

            // 构建命令，输出到类目特定目录
            $cmd = sprintf('python3 %s %s %s %s %s 2>&1',
                escapeshellarg($python_script),
                escapeshellarg($svg_file),
                escapeshellarg($processed_svg_file),
                escapeshellarg($color_str),
                escapeshellarg($category_name)
            );

            // 添加调试日志
            error_log('【调试】执行Python命令: ' . $cmd);
            error_log('【调试】Python脚本路径: ' . $python_script . ' (存在: ' . (file_exists($python_script) ? '是' : '否') . ')');
            error_log('【调试】SVG源文件路径: ' . $svg_file . ' (存在: ' . (file_exists($svg_file) ? '是' : '否') . ')');
            error_log('【调试】处理后SVG目标路径: ' . $processed_svg_file);
            error_log('【调试】目标目录权限: ' . substr(sprintf('%o', fileperms(dirname($processed_svg_file))), -4));
            error_log('【调试】目标目录所有者: ' . fileowner(dirname($processed_svg_file)));
            error_log('【调试】当前PHP进程用户: ' . get_current_user() . ', 用户ID: ' . getmyuid() . ', 组ID: ' . getmygid());
            error_log('【调试】当前工作目录: ' . getcwd());

            // 尝试确保目标目录存在并可写
            $target_dir = dirname($processed_svg_file);
            if (!file_exists($target_dir)) {
                error_log('【调试】目标目录不存在，尝试创建: ' . $target_dir);
                $created = wp_mkdir_p($target_dir);
                error_log('【调试】创建目录结果: ' . ($created ? '成功' : '失败'));
                if ($created) {
                    @chmod($target_dir, 0755);
                }
            }

            if (!is_writable($target_dir)) {
                error_log('【调试】目标目录不可写，尝试修复权限: ' . $target_dir);
                @chmod($target_dir, 0755);
                error_log('【调试】修复权限后: ' . (is_writable($target_dir) ? '可写' : '仍不可写'));

                // 如果仍然不可写，尝试使用系统命令修改权限
                if (!is_writable($target_dir)) {
                    $chmod_cmd = 'chmod -R 755 ' . escapeshellarg($target_dir);
                    error_log('【调试】尝试使用系统命令修改权限: ' . $chmod_cmd);
                    $chmod_output = shell_exec($chmod_cmd);
                    error_log('【调试】系统命令输出: ' . $chmod_output);
                    error_log('【调试】修改权限后再次检查: ' . (is_writable($target_dir) ? '可写' : '仍不可写'));
                }
            }

            // 【修复】执行命令并检查返回值
            error_log('【调试】开始执行Python命令...');
            $output = shell_exec($cmd . ' 2>&1; echo "EXIT_CODE:$?"');
            error_log('【调试】Python执行结果: ' . $output);

            // 【修复】解析退出码
            $exit_code = 0;
            if (preg_match('/EXIT_CODE:(\d+)$/', $output, $matches)) {
                $exit_code = intval($matches[1]);
                $output = preg_replace('/EXIT_CODE:\d+$/', '', $output);
            }

            // 【修复】检查Python脚本是否报告了错误
            $has_python_error = (strpos($output, 'Error:') !== false) || ($exit_code !== 0);

            if (!file_exists($processed_svg_file) || $has_python_error) {
                if ($has_python_error) {
                    error_log('【调试】Python脚本报告错误，退出码: ' . $exit_code);
                } else {
                    error_log('【调试】Python处理SVG失败，未生成文件');
                }

                // 尝试直接复制一个测试文件来验证权限
                $test_file = $target_dir . '/test_write_permission.txt';
                $write_test = @file_put_contents($test_file, 'Test write permission');
                error_log('【调试】写入测试文件结果: ' . ($write_test !== false ? '成功' : '失败'));
                if ($write_test !== false) {
                    @unlink($test_file);
                }

                // 尝试使用系统命令直接执行Python脚本
                error_log('【调试】尝试使用系统命令直接执行Python脚本');
                $direct_cmd = 'cd ' . escapeshellarg(dirname($svg_file)) . ' && python3 ' .
                              escapeshellarg($python_script) . ' ' .
                              escapeshellarg($svg_file) . ' ' .
                              escapeshellarg($processed_svg_file) . ' ' .
                              escapeshellarg($color_str) . ' ' .
                              escapeshellarg($category_name) . ' 2>&1';
                error_log('【调试】直接执行命令: ' . $direct_cmd);
                $direct_output = shell_exec($direct_cmd);
                error_log('【调试】直接执行结果: ' . $direct_output);
                error_log('【调试】再次检查文件是否存在: ' . (file_exists($processed_svg_file) ? '是' : '否'));

                if (!file_exists($processed_svg_file)) {
                    return false;
                }
            }

            // 首先获取SVG尺寸信息，以计算正确的比例
            $svg_info_path = $temp_dir . 'svg_info_' . uniqid() . '.txt';
            $svg_info_command = sprintf(
                'inkscape --query-width --query-height %s > %s',
                escapeshellarg($processed_svg_file),
                escapeshellarg($svg_info_path)
            );

            // error_log('获取SVG尺寸信息: ' . $svg_info_command);
            exec($svg_info_command, $info_output, $info_return_var);

            $svg_ratio = 1; // 默认是正方形

            if (file_exists($svg_info_path) && filesize($svg_info_path) > 0) {
                $svg_dimensions = file_get_contents($svg_info_path);
                if (preg_match('/Width: ([\d\.]+).*Height: ([\d\.]+)/s', $svg_dimensions, $matches) ||
                    preg_match('/([\d\.]+)[\s\n]+([\d\.]+)/', $svg_dimensions, $matches)) {
                    $svg_width_orig = floatval($matches[1]);
                    $svg_height_orig = floatval($matches[2]);

                    if ($svg_width_orig > 0 && $svg_height_orig > 0) {
                        $svg_ratio = $svg_width_orig / $svg_height_orig;
                        // error_log(sprintf('SVG尺寸: %.2f x %.2f, 宽高比: %.2f', $svg_width_orig, $svg_height_orig, $svg_ratio));
                    }
                }
            } else {
                // 备用方法：尝试使用grep提取SVG的viewBox
                $viewbox_command = sprintf('grep -o \'viewBox="[^"]*"\' %s', escapeshellarg($processed_svg_file));
                exec($viewbox_command, $viewbox_output, $viewbox_return);

                if (!empty($viewbox_output) && preg_match('/viewBox="[^"]*\s+([0-9.]+)\s+([0-9.]+)"/', $viewbox_output[0], $vb_matches)) {
                    $svg_width_orig = floatval($vb_matches[1]);
                    $svg_height_orig = floatval($vb_matches[2]);

                    if ($svg_width_orig > 0 && $svg_height_orig > 0) {
                        $svg_ratio = $svg_width_orig / $svg_height_orig;
                        // error_log(sprintf('从viewBox提取SVG尺寸: %.2f x %.2f, 宽高比: %.2f', $svg_width_orig, $svg_height_orig, $svg_ratio));
                    }
                }
            }

            // 清理临时信息文件
            @unlink($svg_info_path);

            // 基于宽高比确定合适的转换尺寸
            $target_width = 600;
            $target_height = 600;
            error_log(sprintf('[合成][前端][预览图] 使用提高质量的预览图尺寸: %dx%d', $target_width, $target_height));

            // 只有当比例明显不是1:1时才进行调整（允许小误差）
            $is_square = ($svg_ratio >= 0.95 && $svg_ratio <= 1.05);

            if (!$is_square) {
                if ($svg_ratio > 1) {
                    // 宽大于高的情况
                    $target_height = intval(600 / $svg_ratio);
                    error_log(sprintf('[合成][前端][预览图] 宽大于高，调整目标高度为: %d', $target_height));
                } else {
                    // 高大于宽的情况
                    $target_width = intval(600 * $svg_ratio);
                    error_log(sprintf('[合成][前端][预览图] 高大于宽，调整目标宽度为: %d', $target_width));
                }
            }

            // 创建用于跟踪转换结果的变量
            $preview_success = false;

            // 【方案1】完整显示 + 自适应缩放：根据最长边自适应，保持宽高比
            $preview_size = 600; // 预览图固定尺寸

            // 【修复】使用已获取的SVG尺寸信息
            $svg_width = $svg_width_orig > 0 ? $svg_width_orig : 600;
            $svg_height = $svg_height_orig > 0 ? $svg_height_orig : 600;

            // 【关键修复】改为完整显示逻辑：选择较小的缩放比例，确保完整显示
            $scale_by_width = $preview_size / $svg_width;
            $scale_by_height = $preview_size / $svg_height;
            $scale = min($scale_by_width, $scale_by_height); // 选择较小的缩放比例以完整显示

            $target_width = intval($svg_width * $scale);
            $target_height = intval($svg_height * $scale);

            error_log(sprintf('[合成][前端][完整显示] SVG原始尺寸: %dx%d, 缩放比例: %.2f, 目标尺寸: %dx%d',
                $svg_width, $svg_height, $scale, $target_width, $target_height));

            // 第一步: 优先使用rsvg-convert将SVG转换为PNG (更可靠，特别是对大文件)
            $start_time = microtime(true);
            $rsvg_command = sprintf(
                'rsvg-convert -a -w %d -h %d -o %s -b transparent %s',
                $target_width,
                $target_height,
                escapeshellarg($temp_png_path),
                escapeshellarg($processed_svg_file)
            );

            error_log('[合成][前端][rsvg-convert] 执行命令: ' . $rsvg_command);
            exec($rsvg_command, $rsvg_output, $rsvg_return_var);

            $execution_time = microtime(true) - $start_time;
            // error_log(sprintf('[预览][rsvg-convert] 执行时间: %.2f秒', $execution_time));

            // 检查rsvg-convert结果
            if ($rsvg_return_var === 0 && file_exists($temp_png_path) && filesize($temp_png_path) > 0) {
                // 【方案1】使用convert居中放置到600x600画布，保持完整显示
                $convert_command = sprintf(
                    'convert %s -background transparent -gravity center -extent %dx%d -quality 90 %s',
                    escapeshellarg($temp_png_path),
                    $preview_size,
                    $preview_size,
                    escapeshellarg($preview_file)
                );

                error_log('[合成][前端][convert] 执行命令: ' . $convert_command);
                exec($convert_command, $convert_output, $convert_return_var);

                if ($convert_return_var === 0 && file_exists($preview_file) && filesize($preview_file) > 0) {
                    error_log('[预览] 使用rsvg-convert成功生成完整显示SVG预览图');

                    // 【新增】为SVG预览图添加水印
                    try {
                        $preview_image = new Imagick($preview_file);
                        $this->add_watermark_to_svg_image($preview_image);
                        $preview_image->writeImage($preview_file);
                        $preview_image->clear();
                        $preview_image->destroy();
                        error_log('[预览] 成功为rsvg-convert生成的预览图添加水印');
                    } catch (Exception $e) {
                        error_log('[预览] 为rsvg-convert生成的预览图添加水印失败: ' . $e->getMessage());
                    }

                    $preview_success = true;
                } else {
                    error_log('[预览][错误] convert处理预览图失败: ' . implode("\n", $convert_output));
                }
            } else {
                // error_log('[预览][错误] rsvg-convert生成预览图失败: ' . implode("\n", $rsvg_output));
            }

            // 清理临时PNG文件
            if (file_exists($temp_png_path)) {
                @unlink($temp_png_path);
                // 重新创建临时PNG文件用于下一步处理
                $temp_png_path = $temp_dir . 'temp_' . $safe_name . '_' . uniqid() . '.png';
            }

            // 第二步: 如果rsvg-convert失败，尝试使用Imagick直接处理
            if (!$preview_success) {
                error_log('[预览] 尝试使用Imagick直接处理SVG预览图');
                try {
                    $svg_preview = new Imagick();
                    $svg_preview->setBackgroundColor(new ImagickPixel('transparent'));
                    $svg_preview->readImage($processed_svg_file);

                    // 【方案1】使用完整显示逻辑调整图片大小
                    $svg_preview->resizeImage($target_width, $target_height, Imagick::FILTER_LANCZOS, 1);

                    // 【方案1】创建固定尺寸的画布
                    $canvas = new Imagick();
                    $canvas->newImage($preview_size, $preview_size, new ImagickPixel('transparent'));
                    $canvas->setImageFormat('webp');

                    // 计算居中位置（完整显示，不裁剪）
                    $x = ($preview_size - $target_width) / 2;
                    $y = ($preview_size - $target_height) / 2;

                    // 将调整后的图片合成到画布上
                    $canvas->compositeImage($svg_preview, Imagick::COMPOSITE_OVER, $x, $y);

                    error_log(sprintf('[合成][前端][Imagick] 使用完整显示逻辑，目标尺寸: %dx%d, 画布: %dx%d, 位置: (%.1f, %.1f)',
                        $target_width, $target_height, $preview_size, $preview_size, $x, $y));

                    // 【新增】为SVG预览图添加水印
                    $this->add_watermark_to_svg_image($canvas);

                    // 设置质量并保存
                    $canvas->setImageCompressionQuality(90);
                    $canvas->writeImage($preview_file);

                    // 清理资源
                    $svg_preview->clear();
                    $svg_preview->destroy();
                    $canvas->clear();
                    $canvas->destroy();

                    if (file_exists($preview_file) && filesize($preview_file) > 0) {
                        // error_log('[预览] 使用Imagick直接处理成功生成SVG预览图');
                        $preview_success = true;
                    } else {
                        // error_log('[预览][错误] Imagick生成文件失败');
                    }
                } catch (Exception $imagick_exception) {
                    // error_log('[预览][错误] Imagick处理SVG预览图失败: ' . $imagick_exception->getMessage());
                }
            }

            // 第三步: 如果前两种方式都失败，才尝试使用Inkscape作为最后的备用方案
            if (!$preview_success) {
                error_log('[预览] 尝试使用Inkscape作为最后备用方案生成预览图');

                $start_time = microtime(true);
                // 【方案1】使用完整显示的目标尺寸
                $inkscape_command = sprintf(
                    'inkscape --export-type=png --export-filename=%s --export-width=%d --export-height=%d --export-background=transparent --export-background-opacity=0 --export-area-page %s',
                    escapeshellarg($temp_png_path),
                    $target_width,
                    $target_height,
                    escapeshellarg($processed_svg_file)
                );

                // error_log('[预览][Inkscape备用] 执行命令: ' . $inkscape_command);

                // 执行Inkscape命令并捕获输出
                $descriptorspec = array(
                    1 => array("pipe", "w"),  // stdout
                    2 => array("pipe", "w")   // stderr
                );

                $process = proc_open($inkscape_command, $descriptorspec, $pipes);

                if (is_resource($process)) {
                    $stdout = stream_get_contents($pipes[1]);
                    $stderr = stream_get_contents($pipes[2]);
                    fclose($pipes[1]);
                    fclose($pipes[2]);
                    $return_var = proc_close($process);

                    $execution_time = microtime(true) - $start_time;
                    // error_log(sprintf('[预览][Inkscape] 执行时间: %.2f秒', $execution_time));
                    // if (!empty($stdout)) { error_log('[预览][Inkscape] 输出: ' . $stdout); }
                    // if (!empty($stderr)) { error_log('[预览][Inkscape] 错误: ' . $stderr); }

                    // 检查是否包含透明通道
                    if ($return_var === 0 && file_exists($temp_png_path) && filesize($temp_png_path) > 0) {
                        $check_alpha_command = sprintf('identify -format "%%[opaque]" %s', escapeshellarg($temp_png_path));
                        $is_opaque = exec($check_alpha_command);

                        if ($is_opaque === 'True') {
                            // error_log('[预览][警告] Inkscape生成的PNG没有透明背景，尝试强制添加透明通道');
                        }

                        // 【方案1】使用convert居中放置到600x600画布，保持完整显示
                        $convert_command = sprintf(
                            'convert %s -background transparent -alpha set -gravity center -extent %dx%d -quality 90 %s',
                            escapeshellarg($temp_png_path),
                            $preview_size,
                            $preview_size,
                            escapeshellarg($preview_file)
                        );

                        // error_log('[预览][Inkscape备用] convert命令: ' . $convert_command);
                        exec($convert_command, $convert_output, $convert_return_var);

                        if ($convert_return_var === 0 && file_exists($preview_file) && filesize($preview_file) > 0) {
                            // error_log('[预览] 使用Inkscape最后备用方案成功生成SVG预览图');

                            // 【新增】为SVG预览图添加水印
                            try {
                                $preview_image = new Imagick($preview_file);
                                $this->add_watermark_to_svg_image($preview_image);
                                $preview_image->writeImage($preview_file);
                                $preview_image->clear();
                                $preview_image->destroy();
                                error_log('[预览] 成功为Inkscape生成的预览图添加水印');
                            } catch (Exception $e) {
                                error_log('[预览] 为Inkscape生成的预览图添加水印失败: ' . $e->getMessage());
                            }

                            $preview_success = true;
                        } else {
                            // error_log('[预览][错误] Inkscape备用方案convert处理失败: ' . implode("\n", $convert_output));
                        }
                    } else {
                        // error_log('[预览][错误] Inkscape备用方案PNG生成失败');
                    }
                } else {
                    // error_log('[预览][错误] 无法执行Inkscape命令');
                }
            }

            // 清理临时文件
            if (file_exists($temp_png_path)) {
                @unlink($temp_png_path);
            }

            if (!$preview_success) {
                // error_log('[预览][警告] 所有预览图生成方法均失败');
                return false;
            }

            // 添加标记，表示该类目已经生成过预览图
            if ($term_id > 0) {
                // 使用类目ID和当前时间作为标记
                update_term_meta($term_id, 'ss_frontend_preview_generated', time());

                // 记录已处理的SVG文件
                $processed_designs = get_term_meta($term_id, 'ss_processed_designs', true);
                if (!is_array($processed_designs)) {
                    $processed_designs = [];
                }

                // 添加当前处理的文件到数组
                $processed_designs[] = $safe_name;
                $processed_designs = array_unique($processed_designs);

                // 更新元数据
                update_term_meta($term_id, 'ss_processed_designs', $processed_designs);

                // 【新增】记录SVG预览图使用的预设颜色类别，用于后续产品生成时的颜色过滤
                $this->record_svg_preview_color_category($safe_name, $term_id);

                // error_log('已为类目ID ' . $term_id . ' 添加前端预览生成标记');
                // error_log('已将处理后的SVG保存到类目文件夹: ' . $processed_svg_file);
            }

            return $preview_url;
        } catch (Exception $e) {
            // error_log('生成SVG预览错误: ' . $e->getMessage());
            // 清理临时文件
            if (file_exists($temp_png_path)) {
                @unlink($temp_png_path);
            }
            return false;
        }
    }

    /**
     * 【修正】记录SVG预览图使用的预设颜色类别
     * 检查类目是否使用了黑白灰预设颜色，用于后续产品生成时过滤相同颜色的变体
     *
     * @param string $svg_name SVG文件名（不含扩展名）
     * @param int $term_id 类目ID
     */
    private function record_svg_preview_color_category($svg_name, $term_id) {
        // 获取类目的预设颜色类别（检查是否使用了黑白灰预设）
        $preset_color_category = $this->get_category_preset_color_type($term_id);

        if ($preset_color_category) {
            // 获取现有的SVG颜色类别记录
            $svg_color_categories = get_term_meta($term_id, 'ss_svg_preview_color_categories', true);
            if (!is_array($svg_color_categories)) {
                $svg_color_categories = [];
            }

            // 记录SVG使用的预设颜色类别
            $svg_color_categories[$svg_name] = $preset_color_category;

            // 保存到数据库
            update_term_meta($term_id, 'ss_svg_preview_color_categories', $svg_color_categories);

            error_log("[颜色过滤] 记录SVG预览图使用预设颜色类别: {$svg_name} -> {$preset_color_category} (类目ID: {$term_id})");
        } else {
            error_log("[颜色过滤] 类目未使用黑白灰预设颜色，不记录: {$svg_name} (类目ID: {$term_id})");
        }
    }

    /**
     * 【新增】检查类目是否使用了黑白灰预设颜色类别
     *
     * @param int $term_id 类目ID
     * @return string|null 返回预设颜色类别（black/white/gray）或null
     */
    private function get_category_preset_color_type($term_id) {
        // 获取类目的预设颜色
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (empty($preset_colors) || !is_array($preset_colors)) {
            return null;
        }

        // 检查是否有Color Science的颜色类别信息
        $color_categories = get_term_meta($term_id, 'ss_color_categories', true);
        if (is_array($color_categories) && !empty($color_categories)) {
            // 检查第一个颜色的类别（通常SVG预览图使用第一个颜色）
            $first_color_category = reset($color_categories);
            if (in_array($first_color_category, ['black', 'white', 'gray'])) {
                error_log("[颜色过滤] 检测到类目使用预设颜色类别: {$first_color_category} (类目ID: {$term_id})");
                return $first_color_category;
            }
        }

        // 如果没有颜色类别信息，检查是否使用了内置的黑白灰预设颜色
        $bwg_presets = [
            'black' => ['#404040', '#353535', '#4a4a4a', '#3f3f3f', '#2a2a2a'],
            'white' => ['#f4f4f4', '#dfdfdf', '#e9e9e9', '#d4d4d4', '#b7b7b7'],
            'gray' => ['#dedede', '#b1b1b1', '#cdcdcd', '#959595', '#747474']
        ];

        $first_color = reset($preset_colors);
        foreach ($bwg_presets as $category => $colors) {
            if (in_array($first_color, $colors)) {
                error_log("[颜色过滤] 通过颜色值匹配检测到预设类别: {$category} (颜色: {$first_color}, 类目ID: {$term_id})");
                return $category;
            }
        }

        error_log("[颜色过滤] 类目未使用黑白灰预设颜色 (第一个颜色: {$first_color}, 类目ID: {$term_id})");
        return null;
    }

    /**
     * 【优化】获取SVG预览图使用的预设颜色类别 - 使用缓存提升性能
     *
     * @param string $svg_name SVG文件名（不含扩展名）
     * @param int $term_id 类目ID
     * @return string|null 返回预设颜色类别或null
     */
    public function get_svg_preview_color_category($svg_name, $term_id) {
        // 【优化】使用缓存
        $cache_key = "svg_color_category_{$term_id}_{$svg_name}";
        $cached_result = wp_cache_get($cache_key, 'ss_svg_colors');

        if ($cached_result !== false) {
            return $cached_result === 'null' ? null : $cached_result;
        }

        $svg_color_categories = get_term_meta($term_id, 'ss_svg_preview_color_categories', true);
        $result = null;

        if (is_array($svg_color_categories) && isset($svg_color_categories[$svg_name])) {
            $result = $svg_color_categories[$svg_name];
        }

        // 【优化】缓存结果
        wp_cache_set($cache_key, $result ?: 'null', 'ss_svg_colors', 300);

        return $result;
    }

    /**
     * 【新增】批量预加载产品生成所需的数据 - 减少重复查询
     *
     * @param int $template_id 模板产品ID
     * @param int $category_id 类目ID
     * @return array 预加载的数据
     */
    private function preload_product_generation_data($template_id, $category_id) {
        $cache_key = "product_gen_data_{$template_id}_{$category_id}";
        $cached_data = wp_cache_get($cache_key, 'ss_product_gen');

        if ($cached_data !== false) {
            return $cached_data;
        }

        $data = [];

        // 批量获取模板产品元数据
        $template_meta = get_post_meta($template_id);
        $data['template_image_id'] = isset($template_meta['_ss_template_image_id'][0]) ? $template_meta['_ss_template_image_id'][0] : '';
        $data['svg_area'] = isset($template_meta['_ss_svg_area'][0]) ? maybe_unserialize($template_meta['_ss_svg_area'][0]) : null;
        $data['shoe_area'] = isset($template_meta['_ss_shoe_area'][0]) ? maybe_unserialize($template_meta['_ss_shoe_area'][0]) : null;

        // 批量获取类目元数据
        $category_meta = get_term_meta($category_id);
        $data['category_image_id'] = isset($category_meta['thumbnail_id'][0]) ? $category_meta['thumbnail_id'][0] : '';
        $data['category_colors'] = isset($category_meta['ss_category_colors'][0]) ? maybe_unserialize($category_meta['ss_category_colors'][0]) : [];

        // 获取文件路径
        if ($data['template_image_id']) {
            $data['template_image_path'] = get_attached_file($data['template_image_id']);
        }
        if ($data['category_image_id']) {
            $data['category_image_path'] = get_attached_file($data['category_image_id']);
        }

        // 缓存数据
        wp_cache_set($cache_key, $data, 'ss_product_gen', 600); // 缓存10分钟

        return $data;
    }

    /**
     * 【修正】根据预设颜色类别确定需要禁用的颜色关键词
     * 基于Color Science确定的预设颜色类别（black/white/gray）
     *
     * @param string $color_category 预设颜色类别（black/white/gray）
     * @return array 需要禁用的颜色关键词数组
     */
    private function get_disabled_color_keywords_by_category($color_category) {
        // 预设颜色类别对应的关键词
        $category_keywords = [
            'black' => ['black'],
            'white' => ['white'],
            'gray' => ['gray', 'grey']
        ];

        if (isset($category_keywords[$color_category])) {
            error_log("[颜色过滤] 预设颜色类别 {$color_category} 对应关键词: " . implode(', ', $category_keywords[$color_category]));
            return $category_keywords[$color_category];
        }

        error_log("[颜色过滤] 未知的预设颜色类别: {$color_category}");
        return [];
    }



    /**
     * 【修正】过滤产品属性中的禁用颜色
     *
     * @param array $attributes 产品属性数组
     * @param array $disabled_color_keywords 禁用的颜色关键词
     * @return array 过滤后的属性数组
     */
    private function filter_product_attributes($attributes, $disabled_color_keywords) {
        if (empty($disabled_color_keywords) || empty($attributes)) {
            return $attributes;
        }

        error_log("[颜色过滤] 开始过滤产品属性");
        error_log("[颜色过滤] 禁用关键词: " . implode(', ', $disabled_color_keywords));

        $filtered_attributes = [];

        foreach ($attributes as $attribute_name => $attribute) {
            error_log("[颜色过滤] 检查属性: {$attribute_name}");

            // 检查是否是颜色属性
            if (strpos($attribute_name, 'pa_color') !== false || strpos($attribute_name, 'color') !== false) {
                error_log("[颜色过滤] 找到颜色属性: {$attribute_name}");

                // 获取属性的选项（颜色terms）
                $options = $attribute->get_options();
                $filtered_options = [];

                foreach ($options as $option) {
                    // 获取term信息
                    $term = get_term($option);
                    if ($term && !is_wp_error($term)) {
                        $should_exclude = false;

                        // 检查term slug是否包含禁用关键词
                        foreach ($disabled_color_keywords as $keyword) {
                            if (stripos($term->slug, $keyword) !== false) {
                                error_log("[颜色过滤] 排除颜色选项 '{$term->slug}' (包含关键词 '{$keyword}')");
                                $should_exclude = true;
                                break;
                            }

                            // 检查term name是否包含禁用关键词
                            if (stripos($term->name, $keyword) !== false) {
                                error_log("[颜色过滤] 排除颜色选项 '{$term->name}' (名称包含关键词 '{$keyword}')");
                                $should_exclude = true;
                                break;
                            }
                        }

                        if (!$should_exclude) {
                            $filtered_options[] = $option;
                            error_log("[颜色过滤] 保留颜色选项: {$term->name} ({$term->slug})");
                        }
                    }
                }

                // 创建新的属性对象，使用过滤后的选项
                if (!empty($filtered_options)) {
                    $filtered_attribute = clone $attribute;
                    $filtered_attribute->set_options($filtered_options);
                    $filtered_attributes[$attribute_name] = $filtered_attribute;
                    error_log("[颜色过滤] 颜色属性过滤完成: 原始" . count($options) . "个选项，过滤后" . count($filtered_options) . "个选项");
                } else {
                    error_log("[颜色过滤] 颜色属性所有选项都被过滤，跳过该属性");
                }
            } else {
                // 非颜色属性直接保留
                $filtered_attributes[$attribute_name] = $attribute;
                error_log("[颜色过滤] 保留非颜色属性: {$attribute_name}");
            }
        }

        error_log("[颜色过滤] 产品属性过滤完成: 原始" . count($attributes) . "个属性，过滤后" . count($filtered_attributes) . "个属性");
        return $filtered_attributes;
    }

    /**
     * 【优化】过滤默认属性中的禁用颜色，并自动设置新的默认变体
     *
     * @param array $default_attributes 默认属性数组
     * @param array $disabled_color_keywords 禁用的颜色关键词
     * @param array $available_color_options 可用的颜色选项（用于自动设置新默认值）
     * @return array 过滤后的默认属性数组
     */
    private function filter_default_attributes($default_attributes, $disabled_color_keywords, $available_color_options = []) {
        if (empty($disabled_color_keywords) || empty($default_attributes)) {
            return $default_attributes;
        }

        error_log("[颜色过滤] 开始过滤默认属性");
        $filtered_default_attributes = [];
        $need_new_default_color = false;
        $color_attribute_name = '';

        foreach ($default_attributes as $attribute_name => $attribute_value) {
            // 检查是否是颜色属性
            if (strpos($attribute_name, 'pa_color') !== false || strpos($attribute_name, 'color') !== false) {
                error_log("[颜色过滤] 检查默认颜色属性: {$attribute_name} = {$attribute_value}");
                $color_attribute_name = $attribute_name;

                $should_exclude = false;

                // 检查属性值是否包含禁用关键词
                foreach ($disabled_color_keywords as $keyword) {
                    if (stripos($attribute_value, $keyword) !== false) {
                        error_log("[颜色过滤] 排除默认颜色属性 '{$attribute_value}' (包含关键词 '{$keyword}')");
                        $should_exclude = true;
                        $need_new_default_color = true;
                        break;
                    }
                }

                // 获取term信息进行进一步检查
                if (!$should_exclude) {
                    $term = get_term_by('slug', $attribute_value, $attribute_name);
                    if ($term && !is_wp_error($term)) {
                        foreach ($disabled_color_keywords as $keyword) {
                            if (stripos($term->name, $keyword) !== false) {
                                error_log("[颜色过滤] 排除默认颜色属性 '{$term->name}' (名称包含关键词 '{$keyword}')");
                                $should_exclude = true;
                                $need_new_default_color = true;
                                break;
                            }
                        }
                    }
                }

                if (!$should_exclude) {
                    $filtered_default_attributes[$attribute_name] = $attribute_value;
                    error_log("[颜色过滤] 保留默认颜色属性: {$attribute_name} = {$attribute_value}");
                }
            } else {
                // 非颜色属性直接保留
                $filtered_default_attributes[$attribute_name] = $attribute_value;
                error_log("[颜色过滤] 保留非颜色默认属性: {$attribute_name} = {$attribute_value}");
            }
        }

        // 【新增】如果原默认颜色被禁用，自动设置新的默认颜色
        if ($need_new_default_color && !empty($color_attribute_name) && !empty($available_color_options)) {
            // 从可用颜色选项中找到第一个未被禁用的颜色
            foreach ($available_color_options as $color_option) {
                $term = get_term($color_option);
                if ($term && !is_wp_error($term)) {
                    $is_disabled = false;

                    // 检查这个颜色是否被禁用
                    foreach ($disabled_color_keywords as $keyword) {
                        if (stripos($term->slug, $keyword) !== false || stripos($term->name, $keyword) !== false) {
                            $is_disabled = true;
                            break;
                        }
                    }

                    if (!$is_disabled) {
                        $filtered_default_attributes[$color_attribute_name] = $term->slug;
                        error_log("[颜色过滤] 自动设置新的默认颜色: {$color_attribute_name} = {$term->slug} ({$term->name})");
                        break;
                    }
                }
            }
        }

        error_log("[颜色过滤] 默认属性过滤完成: 原始" . count($default_attributes) . "个，过滤后" . count($filtered_default_attributes) . "个");
        return $filtered_default_attributes;
    }

    /**
     * 确保目录存在且可写
     * 递归创建目录并设置正确的权限
     */
    private function ensure_directory($dir_path) {
        // 记录开始创建目录
        // error_log("[" . date('Y-m-d H:i:s') . "] 开始检查目录: " . $dir_path);

        // 获取当前PHP进程信息
        $process_user = function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid()) : 'unknown';
        $process_user_name = is_array($process_user) ? $process_user['name'] : $process_user;
        // error_log("[" . date('Y-m-d H:i:s') . "] 当前PHP进程用户: " . $process_user_name);

        // 检查父目录权限
        $parent_dir = dirname($dir_path);
        if (file_exists($parent_dir)) {
            $parent_perms = fileperms($parent_dir);
            $parent_owner = fileowner($parent_dir);
            $parent_group = filegroup($parent_dir);

            // error_log("[" . date('Y-m-d H:i:s') . "] 父目录信息:");
            // error_log("- 路径: " . $parent_dir);
            // error_log("- 权限: " . substr(sprintf('%o', $parent_perms), -4));
            // error_log("- 所有者: " . (function_exists('posix_getpwuid') ? posix_getpwuid($parent_owner)['name'] : $parent_owner));
            // error_log("- 组: " . (function_exists('posix_getgrgid') ? posix_getgrgid($parent_group)['name'] : $parent_group));

            // 检查父目录是否可写
            if (!is_writable($parent_dir)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 警告：父目录不可写");

                // 尝试修改父目录权限
                if (!@chmod($parent_dir, 0755)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 无法修改父目录权限");
                return false;
                }
            }
        }

        // 如果目录已存在，检查权限
        if (file_exists($dir_path)) {
            $dir_perms = fileperms($dir_path);
            $dir_owner = fileowner($dir_path);
            $dir_group = filegroup($dir_path);

            // error_log("[" . date('Y-m-d H:i:s') . "] 目标目录信息:");
            // error_log("- 路径: " . $dir_path);
            // error_log("- 权限: " . substr(sprintf('%o', $dir_perms), -4));
            // error_log("- 所有者: " . (function_exists('posix_getpwuid') ? posix_getpwuid($dir_owner)['name'] : $dir_owner));
            // error_log("- 组: " . (function_exists('posix_getgrgid') ? posix_getgrgid($dir_group)['name'] : $dir_group));

        if (!is_writable($dir_path)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 目录存在但不可写，尝试修改权限");

                // 尝试多种权限组合
                $permissions = [0755, 0775, 0777];
                foreach ($permissions as $perm) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 尝试设置权限: " . sprintf('%o', $perm));
                    if (@chmod($dir_path, $perm)) {
                        if (is_writable($dir_path)) {
                            // error_log("[" . date('Y-m-d H:i:s') . "] 成功设置权限: " . sprintf('%o', $perm));
                            return true;
                        }
                    }
                }

                // error_log("[" . date('Y-m-d H:i:s') . "] 所有权限尝试都失败");
                return false;
            }
            return true;
        }

        // 目录不存在，尝试创建
        // error_log("[" . date('Y-m-d H:i:s') . "] 目录不存在，尝试创建");

        // 首先尝试使用wp_mkdir_p
        if (wp_mkdir_p($dir_path)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] wp_mkdir_p成功创建目录");

            // 设置权限
            if (@chmod($dir_path, 0755)) {
                if (is_writable($dir_path)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 目录创建成功且可写");
                    return true;
                }
            }
        }

        // 如果wp_mkdir_p失败，尝试使用mkdir
        if (!file_exists($dir_path)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 尝试使用mkdir创建目录");

            // 确保父目录存在
            $parent_dir = dirname($dir_path);
            if (!file_exists($parent_dir)) {
                if (!$this->ensure_directory($parent_dir)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 无法创建父目录: " . $parent_dir);
                    return false;
                }
            }

            // 创建目录并设置权限
            if (@mkdir($dir_path, 0755, true)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] mkdir成功创建目录");

                // 验证目录权限
                if (is_writable($dir_path)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 目录创建成功且可写");
                    return true;
                }
            }

            // 如果仍然失败，尝试使用系统命令
            $command = "mkdir -p " . escapeshellarg($dir_path) . " 2>&1";
            $output = [];
            $return_var = -1;
            exec($command, $output, $return_var);

            if ($return_var === 0) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 系统命令成功创建目录");
            @chmod($dir_path, 0755);

                if (is_writable($dir_path)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 目录可写");
                    return true;
                }
            } else {
                // error_log("[" . date('Y-m-d H:i:s') . "] 系统命令创建目录失败: " . implode("\n", $output));
            }
        }

        // 最终检查
        if (file_exists($dir_path) && is_writable($dir_path)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 目录最终检查通过：存在且可写");
            return true;
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 所有创建目录的尝试都失败");
                return false;
            }

    /**
     * 【极速优化】获取类目下的模板列表 - 使用硬编码模板ID，极速响应
     * AJAX处理方法，返回类目下所有可用的产品模板
     */
    public function ss_get_category_templates() {
        $start_time = microtime(true); // 【性能监控】记录开始时间

        // 【极速优化】快速验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        // 获取类目ID
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        if (!$category_id) {
            wp_send_json_error(array('message' => '无效的类目ID'));
            return;
        }

        error_log("[模板查询][极速优化] 开始处理类目 {$category_id} 的模板查询");

        // 【极速优化】使用超级缓存，1小时有效期
        $cache_key = "fast_category_templates_{$category_id}";
        $cached_data = wp_cache_get($cache_key, 'ss_fast_templates');

        if ($cached_data !== false) {
            $cache_time = round((microtime(true) - $start_time) * 1000, 2); // 【性能监控】缓存命中时间
            error_log("[模板查询][极速优化] 使用超级缓存数据，类目 {$category_id}，缓存命中时间: {$cache_time}ms");

            // 【性能监控】添加缓存命中标记
            if (isset($cached_data['performance'])) {
                $cached_data['performance']['cache_used'] = true;
                $cached_data['performance']['cache_hit_time_ms'] = $cache_time;
            }

            wp_send_json_success($cached_data);
            return;
        }

        // 【极速优化】硬编码模板产品配置，避免所有数据库查询
        $template_configs = $this->get_hardcoded_template_configs();

        // 【极速优化】只检查哪些模板产品在当前类目中
        $available_templates = $this->get_available_templates_for_category($category_id, $template_configs);

        if (empty($available_templates)) {
            error_log("[模板查询][极速优化] 类目 {$category_id} 没有可用的模板产品");
            wp_send_json_error(array('message' => '该类目下没有模板产品'));
            return;
        }

        // 【修复】检测类目预设颜色类型并进行模板过滤
        $category_preset_color_type = $this->get_category_preset_color_type($category_id);
        if ($category_preset_color_type) {
            error_log("[模板过滤] 检测到类目 {$category_id} 使用预设颜色类型: {$category_preset_color_type}");

            $disabled_keywords = $this->get_disabled_color_keywords_by_category($category_preset_color_type);
            error_log("[模板过滤] 禁用关键词: " . implode(', ', $disabled_keywords));

            // 需要将模板配置转换为产品对象以便过滤
            $template_products_for_filtering = [];
            foreach ($available_templates as $template_config) {
                $product = get_post($template_config['id']);
                if ($product) {
                    $template_products_for_filtering[] = $product;
                }
            }

            // 应用过滤逻辑
            $filtered_template_products = $this->filter_incompatible_templates($template_products_for_filtering, $disabled_keywords);

            // 将过滤后的产品对象转换回模板配置格式
            $filtered_available_templates = [];
            foreach ($filtered_template_products as $filtered_product) {
                foreach ($available_templates as $template_config) {
                    if ($template_config['id'] == $filtered_product->ID) {
                        $filtered_available_templates[] = $template_config;
                        break;
                    }
                }
            }

            $available_templates = $filtered_available_templates;
            error_log("[模板过滤] 过滤后剩余模板数: " . count($available_templates));

            // 如果过滤后没有可用模板，返回错误
            if (empty($available_templates)) {
                error_log("[模板过滤] 类目 {$category_id} 过滤后没有可用的模板产品");
                wp_send_json_error(array('message' => '该类目下没有兼容的模板产品'));
                return;
            }
        }

        $processing_time = round((microtime(true) - $start_time) * 1000, 2); // 【性能监控】计算处理时间
        error_log("[模板查询][极速优化] 类目 {$category_id} 找到 " . count($available_templates) . " 个可用模板，处理时间: {$processing_time}ms");

        $response_data = array(
            'templates' => $available_templates,
            'performance' => array(
                'processing_time_ms' => $processing_time,
                'template_count' => count($available_templates),
                'cache_used' => false
            )
        );

        // 【极速优化】缓存结果1小时
        wp_cache_set($cache_key, $response_data, 'ss_fast_templates', 3600);

        wp_send_json_success($response_data);
    }

    /**
     * 【极速优化】获取模板产品配置
     * 优先使用缓存的配置，避免重复数据库查询
     */
    private function get_hardcoded_template_configs() {
        // 【极速优化】首先尝试从缓存获取配置
        $cached_configs = wp_cache_get('template_product_configs', 'ss_fast_templates');

        if ($cached_configs !== false) {
            return $cached_configs;
        }

        // 【极速优化】从选项表获取预生成的配置
        $stored_configs = get_option('ss_hardcoded_template_configs', false);

        if ($stored_configs !== false && !empty($stored_configs)) {
            // 缓存配置1小时
            wp_cache_set('template_product_configs', $stored_configs, 'ss_fast_templates', 3600);
            return $stored_configs;
        }

        // 【极速优化】如果没有预生成的配置，快速生成一次
        $configs = $this->generate_template_configs();

        // 保存到选项表和缓存
        update_option('ss_hardcoded_template_configs', $configs);
        wp_cache_set('template_product_configs', $configs, 'ss_fast_templates', 3600);

        return $configs;
    }

    /**
     * 【极速优化】快速生成模板产品配置
     * 只在必要时执行，结果会被缓存
     */
    private function generate_template_configs() {
        global $wpdb;

        // 【极速优化】使用单个SQL查询获取所有模板产品信息，包含优先级
        $template_products = $wpdb->get_results("
            SELECT p.ID, p.post_title, p.post_name,
                   dt.meta_value as display_title,
                   di.meta_value as display_image_id,
                   th.meta_value as thumbnail_id,
                   dp.meta_value as display_priority
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_ss_is_template' AND pm.meta_value = '1'
            LEFT JOIN {$wpdb->postmeta} dt ON p.ID = dt.post_id AND dt.meta_key = '_ss_template_display_title'
            LEFT JOIN {$wpdb->postmeta} di ON p.ID = di.post_id AND di.meta_key = '_ss_template_display_image'
            LEFT JOIN {$wpdb->postmeta} th ON p.ID = th.post_id AND th.meta_key = '_thumbnail_id'
            LEFT JOIN {$wpdb->postmeta} dp ON p.ID = dp.post_id AND dp.meta_key = '_ss_template_display_priority'
            WHERE p.post_type = 'product' AND p.post_status = 'publish'
            ORDER BY CAST(COALESCE(dp.meta_value, '0') AS UNSIGNED) ASC, p.ID ASC
        ");

        $configs = array();

        foreach ($template_products as $product) {
            // 确定显示标题
            $title = !empty($product->display_title) ? $product->display_title : $product->post_title;

            // 确定显示图片
            $image_url = wc_placeholder_img_src('medium'); // 默认占位符

            if (!empty($product->display_image_id)) {
                $temp_url = wp_get_attachment_image_url($product->display_image_id, 'medium');
                if ($temp_url) {
                    $image_url = $temp_url;
                }
            } elseif (!empty($product->thumbnail_id)) {
                $temp_url = wp_get_attachment_image_url($product->thumbnail_id, 'medium');
                if ($temp_url) {
                    $image_url = $temp_url;
                }
            }

            $configs[$product->ID] = array(
                'id' => intval($product->ID),
                'name' => $title,
                'image' => $image_url,
                'url' => get_permalink($product->ID),
                'priority' => intval($product->display_priority ?: 0)
            );
        }

        error_log("[模板配置][极速优化] 生成了 " . count($configs) . " 个模板产品配置");

        return $configs;
    }

    /**
     * 【极速优化】获取指定类目下可用的模板产品
     * 只检查模板产品是否分配给了当前类目
     */
    private function get_available_templates_for_category($category_id, $template_configs) {
        $available_templates = array();

        // 【极速优化】批量检查所有模板产品的类目关联
        $template_ids = array_keys($template_configs);

        if (empty($template_ids)) {
            return $available_templates;
        }

        // 【极速优化】使用单个SQL查询检查所有模板产品的类目关联
        global $wpdb;
        $ids_placeholder = implode(',', array_fill(0, count($template_ids), '%d'));
        $query_params = array_merge($template_ids, array($category_id));

        $assigned_template_ids = $wpdb->get_col($wpdb->prepare("
            SELECT DISTINCT object_id
            FROM {$wpdb->term_relationships}
            WHERE object_id IN ($ids_placeholder)
            AND term_taxonomy_id = (
                SELECT term_taxonomy_id
                FROM {$wpdb->term_taxonomy}
                WHERE term_id = %d AND taxonomy = 'product_cat'
            )
        ", $query_params));

        // 【极速优化】只返回分配给当前类目的模板产品
        foreach ($assigned_template_ids as $template_id) {
            if (isset($template_configs[$template_id])) {
                $available_templates[] = $template_configs[$template_id];
            }
        }

        // 【新增】按优先级排序模板产品
        usort($available_templates, function($a, $b) {
            // 首先按优先级排序（数值越小优先级越高）
            $priority_diff = $a['priority'] - $b['priority'];
            if ($priority_diff !== 0) {
                return $priority_diff;
            }
            // 如果优先级相同，按ID排序
            return $a['id'] - $b['id'];
        });

        error_log("[模板查询][极速优化] 类目 {$category_id} 中找到 " . count($available_templates) . " 个模板产品，已按优先级排序");

        return $available_templates;
    }

    /**
     * 【极速优化】更新模板产品配置
     * 当模板产品有变更时调用此方法
     * 【修复】确保不删除已生成产品的预览图，并保护画廊图片顺序
     */
    public function update_template_configs() {
        error_log("[模板配置][修复] 开始更新模板产品配置，确保不影响已生成产品的预览图和画廊图片顺序");

        // 【修复】在更新模板配置前，先保护已生成产品的画廊图片顺序
        if (class_exists('SS_SVG_Generator')) {
            $generator = SS_SVG_Generator::get_instance();
            $protected_count = $generator->protect_generated_products_gallery_order();
            error_log("[模板配置][修复] 已保护 {$protected_count} 个产品的画廊图片顺序");
        }

        // 清除所有相关缓存
        wp_cache_delete('template_product_configs', 'ss_fast_templates');
        wp_cache_flush_group('ss_fast_templates');
        delete_option('ss_hardcoded_template_configs');

        // 重新生成配置
        $configs = $this->generate_template_configs();

        // 保存新配置
        update_option('ss_hardcoded_template_configs', $configs);
        wp_cache_set('template_product_configs', $configs, 'ss_fast_templates', 3600);

        error_log("[模板配置][修复] 已更新模板产品配置，共 " . count($configs) . " 个模板产品，已生成产品的预览图和画廊图片顺序保持不变");

        return $configs;
    }

    /**
     * 【极速优化】AJAX处理：更新模板配置
     */
    public function ajax_update_template_configs() {
        // 验证权限
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => '权限不足'));
        }

        // 验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        $configs = $this->update_template_configs();

        wp_send_json_success(array(
            'message' => '模板配置已更新',
            'count' => count($configs)
        ));
    }

    /**
     * 【修复】清除模板产品相关缓存
     *
     * @param int $category_id 类目ID（可选）
     */
    public function clear_template_cache($category_id = null) {
        if ($category_id) {
            // 【修复】简化缓存清理逻辑，避免调用可能导致超时的方法
            $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);

            $cache_key = "category_templates_{$category_id}" . ($is_custom_matching ? '_custom_matching' : '');
            $template_cache_key = "template_products_{$category_id}" . ($is_custom_matching ? '_custom_matching' : '');
            $category_cache_key = "category_info_{$category_id}";

            wp_cache_delete($cache_key, 'ss_templates');
            wp_cache_delete($template_cache_key, 'ss_templates');
            wp_cache_delete($category_cache_key, 'ss_categories');

            error_log("[缓存清理][修复] 已清除类目 {$category_id} 的模板缓存");
        } else {
            // 清除所有模板相关缓存
            wp_cache_flush_group('ss_templates');
            wp_cache_flush_group('ss_categories');
            error_log("[缓存清理][修复] 已清除所有模板相关缓存");
        }
    }

    /**
     * 【修复】AJAX处理函数：清除模板缓存
     */
    public function ajax_clear_template_cache() {
        // 验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : null;

        $this->clear_template_cache($category_id);

        $message = $category_id ?
            "已清除类目 {$category_id} 的模板缓存" :
            "已清除所有模板相关缓存";

        wp_send_json_success(array('message' => $message));
    }

    /**
     * 【优化】获取类目下的模板产品 - 提升查询性能
     * 查询指定类目下设置为模板的产品
     *
     * @param int $category_id 类目ID
     * @return array 模板产品列表
     */
    private function get_category_template_products($category_id) {
        // 【修复】检查是否为自定义匹配类目
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);

        if ($is_custom_matching) {
            error_log("[模板查询][极速优化] 检测到自定义匹配类目 {$category_id}，返回精简的模板产品");

            // 【极速优化】自定义匹配类目返回更少的模板产品，大幅提升性能
            $args = array(
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 8, // 【极速优化】减少到8个，显著提升响应速度
                'fields' => 'ids', // 【极速优化】只获取ID，减少内存使用
                'no_found_rows' => true,
                'update_post_meta_cache' => false, // 【优化】禁用元数据缓存，减少内存使用
                'update_post_term_cache' => false,
                'meta_query' => array(
                    array(
                        'key' => '_ss_is_template',
                        'value' => '1',
                        'compare' => '='
                    )
                ),
                'meta_key' => '_ss_template_display_priority',
                'orderby' => 'meta_value_num',
                'order' => 'ASC'
            );
        } else {
            // 【优化】普通类目使用原有逻辑，查询分配给该类目的模板产品
            $args = array(
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'fields' => 'all', // 需要完整的post对象
                'no_found_rows' => true, // 不计算总数，提升性能
                'update_post_meta_cache' => true, // 预加载元数据
                'update_post_term_cache' => false, // 不需要分类缓存
                'tax_query' => array(
                    array(
                        'taxonomy' => 'product_cat',
                        'field' => 'term_id',
                        'terms' => $category_id
                    )
                ),
                'meta_query' => array(
                    array(
                        'key' => '_ss_is_template',
                        'value' => '1',
                        'compare' => '='
                    )
                ),
                'meta_key' => '_ss_template_display_priority',
                'orderby' => 'meta_value_num',
                'order' => 'ASC'
            );
        }

        $query = new WP_Query($args);

        error_log("[模板查询] 类目ID {$category_id} " . ($is_custom_matching ? '(自定义匹配)' : '(普通类目)') . " 的模板产品查询结果：{$query->post_count} 个产品");

        return $query->posts;
    }

    /**
     * 【优化】生成SVG产品 - 提升处理速度
     * AJAX处理方法，根据SVG和模板产品生成新产品
     */
    public function ss_generate_svg_product() {
        $start_time = microtime(true); // 【性能监控】记录开始时间

        // 【优化】快速验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        // 【优化】一次性获取和验证所有参数
        $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
        $svg_file = isset($_POST['svg_file']) ? sanitize_text_field($_POST['svg_file']) : '';
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        // 【修复】提取文件名，避免路径问题
        $svg_filename = basename($svg_file);
        error_log("[产品生成][修复] 原始SVG参数: {$svg_file}, 提取的文件名: {$svg_filename}");

        // 【优化】快速参数验证
        if (!$template_id || empty($svg_filename) || !$category_id) {
            wp_send_json_error(['message' => '缺少必要参数']);
            return;
        }

        try {

        // 【极速优化】检查合成图片缓存
        $cache_key = $this->get_composite_cache_key($template_id, $svg_file, $category_id);
        $cached_composite = $this->get_cached_composite_image($cache_key);

        if ($cached_composite) {
            error_log("[产品生成][极速优化] 使用缓存的合成图片，跳过图片处理");

            // 直接使用缓存的合成图片创建产品
            $product_result = $this->create_product_from_cached_composite(
                $template_id, $cached_composite, $svg_file, $category_id, $start_time
            );

            if ($product_result) {
                $total_time = round((microtime(true) - $start_time) * 1000, 2);
                error_log("[产品生成][极速优化] 缓存命中，总耗时: {$total_time}ms");

                wp_send_json_success([
                    'product_id' => $product_result['product_id'],
                    'product_url' => $product_result['product_url'],
                    'message' => '产品创建成功（缓存加速）',
                    'performance' => [
                        'total_time_ms' => $total_time,
                        'cache_hit' => true
                    ]
                ]);
                return;
            }
        }

        // 【优化】设置更合理的时间限制和内存限制
        @set_time_limit(120); // 【优化】减少到2分钟，提升用户体验
        @ini_set('memory_limit', '256M'); // 【优化】减少内存限制，避免服务器过载

        // 【优化】使用缓存获取类目信息
        $category_cache_key = "category_info_{$category_id}";
        $category_term = wp_cache_get($category_cache_key, 'ss_categories');

        if ($category_term === false) {
            $category_term = get_term($category_id, 'product_cat');
            if (!is_wp_error($category_term) && $category_term) {
                wp_cache_set($category_cache_key, $category_term, 'ss_categories', 300); // 缓存5分钟
            }
        }

        if (is_wp_error($category_term) || !$category_term) {
            wp_send_json_error(['message' => '无法获取类目信息']);
            return;
        }

        $category_name = $category_term->name;

        // 【优化】检查SVG表是否已创建（使用缓存）
        $table_exists_cache_key = 'svg_relations_table_exists';
        $table_exists = wp_cache_get($table_exists_cache_key, 'ss_tables');

        if ($table_exists === false) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'svg_product_relations';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            wp_cache_set($table_exists_cache_key, $table_exists, 'ss_tables', 3600); // 缓存1小时

            if (!$table_exists) {
                $this->create_svg_product_relations_table();
                wp_cache_set($table_exists_cache_key, true, 'ss_tables', 3600);
            }
        }

        // 【优化】使用更智能的缓存检查是否已存在相同的SVG产品 - 【修复】使用文件名
        $existing_product_cache_key = "existing_svg_product_{$template_id}_{$category_id}_" . md5($svg_filename);
        $existing_product = wp_cache_get($existing_product_cache_key, 'ss_svg_products');

        if ($existing_product === false) {
            // 【优化】添加超时控制，避免长时间查询
            $start_time = microtime(true);
            $existing_product = $this->get_existing_svg_product($template_id, $svg_filename, $category_id);
            $duration = microtime(true) - $start_time;

            // 【优化】根据查询时间调整缓存时间
            $cache_time = $duration > 1 ? 600 : 300; // 查询慢的缓存更久
            wp_cache_set($existing_product_cache_key, $existing_product ?: 'not_found', 'ss_svg_products', $cache_time);

            error_log("[产品生成][优化] 产品存在性检查耗时: " . round($duration * 1000, 2) . "ms");
        } else {
            error_log("[产品生成][优化] 使用缓存的产品存在性检查结果");
        }

        if ($existing_product && $existing_product !== 'not_found') {
            error_log("[产品生成][优化] 产品已存在，直接返回");
            wp_send_json_success([
                'product_id' => $existing_product['product_id'],
                'product_url' => get_permalink($existing_product['product_id']),
                'message' => '产品已存在'
            ]);
            return;
        }

        // 【极速优化】预检查所有必要资源，避免处理到一半才失败 - 【修复】使用文件名
        $precheck_result = $this->precheck_product_generation_resources($template_id, $svg_filename, $category_id);

        if (!$precheck_result['success']) {
            error_log("[产品生成][预检查] 资源检查失败: " . $precheck_result['message']);
            wp_send_json_error(['message' => $precheck_result['message']]);
            return;
        }

        // 使用预检查的数据，避免重复查询
        $template_product = $precheck_result['template_product'];
        $category_name = $precheck_result['category_name'];
        $category_term = $precheck_result['category_term'];

        // error_log("[" . date('Y-m-d H:i:s') . "] 开始生成SVG产品: template_id=" . $template_id . ", svg_file=" . $svg_file . ", category_id=" . $category_id);

        // 【优化】预计算常用值 - 【修复】使用文件名
        $svg_basename = pathinfo($svg_filename, PATHINFO_FILENAME);
        $safe_product_name = sanitize_title($svg_basename);
        $safe_category_name = $this->get_safe_category_name($category_name);

        // 【优化】批量获取所有必要的元数据
        $template_meta = get_post_meta($template_id);
        $category_meta = get_term_meta($category_id);

        // 验证模板配置
        $template_image_id = isset($template_meta['_ss_template_image_id'][0]) ? $template_meta['_ss_template_image_id'][0] : '';
        $svg_area = isset($template_meta['_ss_svg_area'][0]) ? maybe_unserialize($template_meta['_ss_svg_area'][0]) : null;
        $shoe_area = isset($template_meta['_ss_shoe_area'][0]) ? maybe_unserialize($template_meta['_ss_shoe_area'][0]) : null;

        if (empty($template_image_id) || !is_array($svg_area) || !is_array($shoe_area)) {
            wp_send_json_error(['message' => '模板产品配置不完整']);
            return;
        }

        // 【修复】验证类目配置 - 自定义匹配类目不强制要求类目图片
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
        $category_image_id = isset($category_meta['thumbnail_id'][0]) ? $category_meta['thumbnail_id'][0] : '';

        if (empty($category_image_id) && !$is_custom_matching) {
            wp_send_json_error(['message' => '类目配置不完整']);
            return;
        }

        if ($is_custom_matching) {
            error_log("[修复] 自定义匹配类目 {$category_id} 跳过类目图片验证");
        }

        // 【修复】批量获取文件路径并验证 - 支持自定义匹配类目的专用路径
        $upload_dir = wp_upload_dir();

        // 【修复】使用之前已检查的自定义匹配类目状态，使用专用预览图路径
        if ($is_custom_matching) {
            $category_slug = $category_term->slug;
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' .
                           basename($svg_filename, '.svg') . '_preview.webp';
            error_log("[修复] 自定义匹配类目 {$category_id} 使用专用预览图路径: {$preview_file}");
        } else {
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/previews/' .
                           basename($svg_filename, '.svg') . '_preview.webp';
            error_log("[修复] 普通类目 {$category_id} 使用标准预览图路径: {$preview_file}");
        }

        $template_image_path = get_attached_file($template_image_id);
        $category_image_path = $is_custom_matching ? '' : get_attached_file($category_image_id);

        // 【修复】根据类目类型验证不同的文件
        $required_files = [
            'preview' => $preview_file,
            'template' => $template_image_path
        ];

        // 【修复】只有非自定义匹配类目才验证类目图片
        if (!$is_custom_matching) {
            $required_files['category'] = $category_image_path;
        }

        foreach ($required_files as $type => $file_path) {
            if (empty($file_path) || !file_exists($file_path)) {
                wp_send_json_error(['message' => "无法找到{$type}文件"]);
                return;
            }
        }

        // 【修复】使用自定义图片合成功能 - 传递类目ID以确保正确检测自定义匹配类目
        $composite_result = $this->ss_composite_images(
            $template_image_path,  // 模板图片路径
            $preview_file,         // 预览图文件（而非SVG文件）
            $category_image_path,  // 类目图片路径
            $svg_area,             // SVG区域
            $shoe_area,            // 鞋子区域
            $safe_category_name,   // 安全类目名
            $safe_product_name,    // 安全产品名
            $category_id           // 【修复】传递类目ID
        );

        if (!$composite_result) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 图片合成失败");
            wp_send_json_error(['message' => '图片合成失败']);
                return;
            }

        // 【极速优化】缓存合成图片，加速后续相同组合的生成
        $this->cache_composite_image($cache_key, $composite_result);
        error_log("[产品生成][极速优化] 合成图片已缓存，键: {$cache_key}");

        // 创建产品 - 【修复】使用文件名
        $product_result = $this->create_product_from_template(
            $template_id,
            $composite_result,  // 直接传递合成图片路径
            $svg_filename,
            $safe_category_name,
            $safe_product_name,
            $category_id,
            $category_name
        );

        if (!$product_result) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 创建产品失败");
            wp_send_json_error(['message' => '创建产品失败']);
                    return;
        }

        // 记录SVG产品关系 - 【修复】使用文件名
        $this->record_svg_product_relation($template_id, $svg_filename, $category_id, $product_result['product_id']);

        // 【性能监控】记录总耗时
        $total_time = round((microtime(true) - $start_time) * 1000, 2);
        error_log("[产品生成][性能监控] 产品创建完成，总耗时: {$total_time}ms");

        // 【调试】记录返回的数据
        error_log("[产品生成][调试] 准备返回数据 - 产品ID: " . $product_result['product_id'] . ", URL: " . $product_result['product_url']);

        // 返回成功结果
        wp_send_json_success([
            'product_id' => $product_result['product_id'],
            'product_url' => $product_result['product_url'],
            'message' => '产品创建成功',
            'performance' => [
                'total_time_ms' => $total_time,
                'cache_hit' => false,
                'precheck_time_ms' => $precheck_result['precheck_time_ms'] ?? 0
            ]
        ]);

        } catch (Exception $e) {
            error_log("[产品生成][异常] 产品生成过程中发生异常: " . $e->getMessage());
            error_log("[产品生成][异常] 异常堆栈: " . $e->getTraceAsString());
            wp_send_json_error([
                'message' => '产品生成过程中发生错误: ' . $e->getMessage()
            ]);
        }
    }

    public function ss_composite_images($template_image_path, $preview_image_path, $category_image_path, $svg_area, $shoe_area, $safe_category_name, $safe_product_name, $category_id = null) {
        try {
            // 获取类目名称（使用连字符格式）
            $category_term_name = '';
            $category_term_id = null;
            $term_args = array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'name__like' => str_replace('_', ' ', $safe_category_name)
            );

            $terms = get_terms($term_args);
            if (!empty($terms) && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    if ($this->get_safe_category_name($term->name) === $safe_category_name) {
                        $category_term_name = str_replace(' ', '-', $term->name);
                        $category_term_id = $term->term_id;
                        break;
                    }
                }
            }

            // 如果无法获取类目名称，则使用安全的类目名称转换为连字符格式
            if (empty($category_term_name)) {
                $category_term_name = str_replace('_', '-', $safe_category_name);
            }

            // 【修复】检查是否为自定义匹配类目，优先使用传入的category_id
            $is_custom_matching = false;
            $effective_category_id = $category_id ?: $category_term_id;

            if ($effective_category_id) {
                $is_custom_matching = get_term_meta($effective_category_id, '_is_custom_matching_category', true);
                if (!empty($is_custom_matching)) {
                    error_log('【修复】自定义匹配类目检测: ' . $safe_category_name . ' (ID: ' . $effective_category_id . ')，将跳过类目图片植入');
                } else {
                    error_log('【修复】普通类目检测: ' . $safe_category_name . ' (ID: ' . $effective_category_id . ')，将正常处理类目图片');
                }
            } else {
                error_log('【修复】无法获取有效的类目ID，按普通类目处理');
            }

            // 【性能优化】直接使用SVG预览图，而不是原始SVG文件
            // 这样可以避免SVG转换过程，显著提升合成性能
            $processed_svg_path = $preview_image_path;

            error_log("[性能优化][前端合成] 直接使用SVG预览图进行合成: {$processed_svg_path}");

            // error_log("[合成] 开始图片合成");
            // error_log("[合成] 模板图片路径: " . $template_image_path);
            // error_log("[合成] SVG文件路径: " . $processed_svg_path);
            // error_log("[合成] 类目图片路径: " . $category_image_path);
            // error_log("[合成] SVG区域: " . print_r($svg_area, true));
            // error_log("[合成] 鞋子区域: " . print_r($shoe_area, true));

            // 【性能优化】检查文件是否存在 - 现在检查的是SVG预览图
            if (!file_exists($template_image_path)) {
                error_log('[合成][错误] 模板图片不存在: ' . $template_image_path);
                return false;
            }
            if (!file_exists($processed_svg_path)) {
                error_log('[合成][错误] SVG预览图不存在: ' . $processed_svg_path);
                return false;
            }
            // 【修复】只有非自定义匹配类目才检查类目图片
            if (!$is_custom_matching && !file_exists($category_image_path)) {
                error_log('[合成][错误] 类目图片不存在: ' . $category_image_path);
                return false;
            }
            if ($is_custom_matching) {
                error_log('[合成] 自定义匹配类目，跳过类目图片文件检查');
            }

            // 验证区域参数
            if (!isset($svg_area['x'], $svg_area['y'], $svg_area['width'], $svg_area['height'])) {
                // error_log('[合成][错误] SVG区域参数不完整: ' . print_r($svg_area, true));
                return false;
            }
            if (!isset($shoe_area['x'], $shoe_area['y'], $shoe_area['width'], $shoe_area['height'])) {
                // error_log('[合成][错误] 鞋子区域参数不完整: ' . print_r($shoe_area, true));
                return false;
            }

            $upload_dir = wp_upload_dir();

            // 【修复】根据类目类型使用不同的最终产品保存路径
            if ($is_custom_matching && $effective_category_id) {
                $category_term = get_term($effective_category_id);
                $category_slug = $category_term->slug;
                $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/products/' . $category_slug . '/';
                error_log("[修复][前端合成] 自定义匹配类目使用专用产品路径: {$output_dir}");
            } else {
                $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/final-products/' . $safe_category_name . '/images/';
                error_log("[修复][前端合成] 普通类目使用标准产品路径: {$output_dir}");
            }

            if (!file_exists($output_dir)) {
                wp_mkdir_p($output_dir);
                // error_log('[合成] 创建输出目录: ' . $output_dir);
            }

            // 【性能优化】由于现在直接使用SVG预览图，无需创建临时目录和转换
            // 获取SVG区域宽度
            $svg_width = intval($svg_area['width']);

            if ($svg_width <= 0) {
                error_log('[合成][错误] 无效的SVG区域宽度');
                return false;
            }

            // 【性能优化】直接使用SVG预览图，无需转换和缓存处理
            error_log('[性能优化][前端合成] 跳过SVG转换，直接使用预览图进行合成');

            try {
                // 加载模板图片
                $final_image = new Imagick($template_image_path);
                error_log('[性能优化][前端合成] 成功加载模板图片');

                // 【性能优化】直接加载SVG预览图
                $svg_image = new Imagick($processed_svg_path);

                // 设置透明选项
                $svg_image->setImageBackgroundColor(new ImagickPixel('transparent'));
                $svg_image->setImageAlphaChannel(Imagick::ALPHACHANNEL_ACTIVATE);

                // 【修复】先对SVG预览图进行自动裁剪，去除透明边缘，确保图片尽量填满置入区域
                error_log('[性能优化][前端合成] 开始裁剪SVG预览图的透明边缘');
                $cropped_svg = $this->crop_transparent_borders($svg_image);
                if ($cropped_svg) {
                    $svg_image->clear();
                    $svg_image->destroy();
                    $svg_image = $cropped_svg;
                    error_log('[性能优化][前端合成] SVG预览图裁剪完成');
                } else {
                    error_log('[性能优化][前端合成] SVG预览图裁剪失败，使用原图');
                }

                // 获取裁剪后的SVG预览图尺寸
                $svg_dimensions = $svg_image->getImageGeometry();
                $actual_svg_width = $svg_dimensions['width'];
                $actual_svg_height = $svg_dimensions['height'];
                $svg_ratio = $actual_svg_width / $actual_svg_height;
                $area_ratio = $svg_width / $svg_area['height'];

                error_log(sprintf('[性能优化][前端合成] 裁剪后SVG尺寸: %dx%d, 宽高比: %.2f', $actual_svg_width, $actual_svg_height, $svg_ratio));
                error_log(sprintf('[性能优化][前端合成] SVG区域尺寸: %dx%d, 宽高比: %.2f', $svg_width, $svg_area['height'], $area_ratio));

                // 【修复】计算缩放尺寸，确保SVG尽量填满置入区域（与Gallery逻辑一致）
                if ($svg_ratio > $area_ratio) {
                    // SVG更宽，以宽度为基准，确保不超出左右边界
                    $target_width = $svg_width;
                    $target_height = intval($svg_width / $svg_ratio);
                    error_log('[性能优化][前端合成] SVG较宽，以宽度为基准缩放，确保不超出左右边界');
                } else {
                    // SVG更高或相等，以高度为基准，确保不超出上下边界
                    $target_height = intval($svg_area['height']);
                    $target_width = intval($svg_area['height'] * $svg_ratio);
                    error_log('[性能优化][前端合成] SVG较高，以高度为基准缩放，确保不超出上下边界');
                }

                error_log(sprintf('[性能优化][前端合成] 修复后缩放尺寸: %dx%d（确保尽量填满 %dx%d 区域）', $target_width, $target_height, $svg_width, $svg_area['height']));

                // 调整SVG预览图大小
                $svg_image->resizeImage($target_width, $target_height, Imagick::FILTER_LANCZOS, 1);

                // 计算置入位置（水平居中，垂直居中）
                $svg_x = intval($svg_area['x'] + ($svg_width - $target_width) / 2);
                $svg_y = intval($svg_area['y'] + ($svg_area['height'] - $target_height) / 2);

                error_log(sprintf('[性能优化][前端合成] 置入位置: x=%d, y=%d（水平垂直居中，确保在区域内）', $svg_x, $svg_y));

                // 合成SVG预览图到模板中的SVG区域
                $final_image->compositeImage(
                    $svg_image,
                    Imagick::COMPOSITE_OVER,
                    $svg_x,
                    $svg_y
                );
                error_log('[性能优化][前端合成] 成功合成SVG预览图到SVG区域');

                // 【模块4】检查是否需要植入类目图片
                if (!$is_custom_matching) {
                    // 加载类目图片（鞋子图片）
                    $category_image = new Imagick($category_image_path);

                    // 获取类目图片实际尺寸
                    $category_dimensions = $category_image->getImageGeometry();
                    $category_width = $category_dimensions['width'];
                    $category_height = $category_dimensions['height'];
                    // error_log(sprintf('[合成] 类目图片原始尺寸: %dx%d', $category_width, $category_height));

                    // 处理鞋子图片（与SVG处理逻辑保持一致）
                    $shoe_width = intval($shoe_area['width']);
                    $shoe_height = intval($shoe_area['height']);
                    $shoe_ratio = $category_width / $category_height;

                    // 按最大宽度等比缩放高度
                    $target_shoe_width = $shoe_width;
                    $target_shoe_height = intval($shoe_width / $shoe_ratio);

                    // error_log(sprintf('[合成] 鞋子图片目标尺寸: %dx%d', $target_shoe_width, $target_shoe_height));

                    // 调整类目图片大小
                    $category_image->resizeImage(
                        $target_shoe_width,
                        $target_shoe_height,
                        Imagick::FILTER_LANCZOS,
                        1
                    );

                    // error_log('[合成] 成功调整类目图片大小');

                    // 合成类目图片到模板中的鞋子区域（贴近顶部置入）
                    $final_image->compositeImage(
                        $category_image,
                        Imagick::COMPOSITE_OVER,
                        intval($shoe_area['x']),
                        intval($shoe_area['y'])
                    );
                    // error_log('[合成] 成功合成类目图片到鞋子区域，位置: x=' . $shoe_area['x'] . ', y=' . $shoe_area['y']);
                } else {
                    error_log('【模块4】自定义匹配类目，跳过类目图片植入');
                }

                // 保存最终图片（SVG预览图已包含水印，无需重复添加）
                $final_image_name = basename($processed_svg_path, '.webp') . '_main_' . uniqid() . '.webp';
                $final_image_path = $output_dir . $final_image_name;

                $final_image->setImageFormat('webp');
                $final_image->setImageCompressionQuality(90);
                $final_image->writeImage($final_image_path);

                error_log('[性能优化][前端合成] 最终图片保存到: ' . $final_image_path);

                // 清理资源
                $svg_image->clear();
                $svg_image->destroy();

                // 【模块4】只有在非自定义匹配类目时才清理类目图片资源
                if (!$is_custom_matching && isset($category_image)) {
                    $category_image->clear();
                    $category_image->destroy();
                }

                $final_image->clear();
                $final_image->destroy();

                return $final_image_path;
            } catch (Exception $e) {
                error_log('[性能优化][前端合成][错误] Imagick处理异常: ' . $e->getMessage());
                return false;
            }
        } catch (Exception $e) {
            // error_log('[合成][错误] 发生异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建SVG产品关系表
     */
    private function create_svg_product_relations_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'svg_product_relations';

        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                template_id bigint(20) NOT NULL,
                svg_file varchar(255) NOT NULL,
                category_id bigint(20) NOT NULL,
                product_id bigint(20) NOT NULL,
                created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                PRIMARY KEY  (id),
                KEY template_svg_category (template_id, svg_file(191), category_id)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            // error_log("[" . date('Y-m-d H:i:s') . "] 创建SVG产品关系表");
        }
    }

    /**
     * 生成标准化格式的产品标题
     * 格式：预览图SVG名称 + 产品模板名称 + 产品类目名称
     */
    private function ss_generate_product_title($svg_file, $template_name, $category_name) {
        error_log("[" . date('Y-m-d H:i:s') . "] 开始生成产品标题");
        error_log("[" . date('Y-m-d H:i:s') . "] SVG文件名: " . $svg_file);
        error_log("[" . date('Y-m-d H:i:s') . "] 模板名称: " . $template_name);
        error_log("[" . date('Y-m-d H:i:s') . "] 类目名称: " . $category_name);

        // 移除文件扩展名
        $svg_base_name = pathinfo($svg_file, PATHINFO_FILENAME);

        // 清理和标准化文件名
        $svg_base_name = str_replace('-', ' ', $svg_base_name);
        $svg_base_name = str_replace('_', ' ', $svg_base_name);
        $svg_base_name = ucwords($svg_base_name);

        error_log("[" . date('Y-m-d H:i:s') . "] 处理后的SVG名称: " . $svg_base_name);

        // 直接使用模板名称，不再提取产品类型
        // 这样可以确保无论模板名称如何变化，都能正确使用完整的模板名称

        // 构建标准化的标题格式：SVG名称 + 产品模板名称 + 产品类目名称
        $title = sprintf(
            '%s %s %s',
            $svg_base_name,
            $template_name,
            $category_name
        );

        error_log("[" . date('Y-m-d H:i:s') . "] 生成的标题: " . $title);

        return $title;
    }

    private function create_product_from_template($template_id, $image_path, $svg_file, $safe_category_name, $safe_product_name, $category_id, $category_name = '') {
        // error_log("[" . date('Y-m-d H:i:s') . "] 开始从模板创建产品: template_id=" . $template_id . ", svg_file=" . $svg_file);

        // 获取模板产品
        $template_product = wc_get_product($template_id);
        if (!$template_product) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 无法获取模板产品");
            return false;
        }

        // 【修正】获取SVG预览图使用的预设颜色类别，用于过滤相同颜色的变体
        $svg_name = pathinfo(basename($svg_file), PATHINFO_FILENAME);
        $svg_color_category = $this->get_svg_preview_color_category($svg_name, $category_id);
        error_log("[颜色过滤] SVG {$svg_name} 使用的预设颜色类别: " . ($svg_color_category ?: '未使用预设颜色'));

        // 确定需要禁用的颜色关键词
        $disabled_color_keywords = [];
        if ($svg_color_category) {
            $disabled_color_keywords = $this->get_disabled_color_keywords_by_category($svg_color_category);
            error_log("[颜色过滤] 需要禁用的颜色关键词: " . implode(', ', $disabled_color_keywords));
        }

        // 记录产品类型
        $product_type = $template_product->get_type();
        // error_log("[" . date('Y-m-d H:i:s') . "] 模板产品类型: " . $product_type);

        // 如果类目名称为空，尝试获取
        if (empty($category_name)) {
            $category_term = get_term($category_id, 'product_cat');
            if (!is_wp_error($category_term) && $category_term) {
                $category_name = $category_term->name;
                // error_log("[" . date('Y-m-d H:i:s') . "] 获取类目名称: " . $category_name);
            } else {
                $category_name = str_replace('_', ' ', $safe_category_name);
                // error_log("[" . date('Y-m-d H:i:s') . "] 使用安全的类目名称替代: " . $category_name);
            }
        }

        // 获取模板产品名称
        $template_name = $template_product->get_name();

        // 生成标准化格式的产品标题
        $product_name = $this->ss_generate_product_title($svg_file, $template_name, $category_name);
        // error_log("[" . date('Y-m-d H:i:s') . "] 最终产品标题: " . $product_name);

        // 根据模板产品类型创建正确的产品对象
        if ($product_type === 'variable') {
            $new_product = new WC_Product_Variable();
            // error_log("[" . date('Y-m-d H:i:s') . "] 创建变体产品");
            } else {
            $new_product = new WC_Product_Simple();
            // error_log("[" . date('Y-m-d H:i:s') . "] 创建简单产品");
        }

        // 设置产品属性
        $new_product->set_name($product_name);
        $new_product->set_description($template_product->get_description());
        $new_product->set_short_description($template_product->get_short_description());
        $new_product->set_status('publish');
        $new_product->set_catalog_visibility('visible');
        $new_product->set_featured($template_product->get_featured());
        $new_product->set_virtual($template_product->is_virtual());
        $new_product->set_tax_status($template_product->get_tax_status());
        $new_product->set_tax_class($template_product->get_tax_class());
        $new_product->set_sold_individually($template_product->get_sold_individually());
        $new_product->set_backorders($template_product->get_backorders());
        $new_product->set_reviews_allowed($template_product->get_reviews_allowed());

        // 设置价格
        $new_product->set_regular_price($template_product->get_regular_price());
        $new_product->set_price($template_product->get_price());
        $new_product->set_sale_price($template_product->get_sale_price());

        // 设置类目
        $new_product->set_category_ids(array($category_id));

        // 复制标签
        $new_product->set_tag_ids($template_product->get_tag_ids());
        // error_log("[" . date('Y-m-d H:i:s') . "] 复制产品标签: " . implode(', ', $template_product->get_tag_ids()));

        // 【优化】批量处理变体产品属性
        if ($product_type === 'variable') {
            // 【优化】使用预加载的数据减少查询
            $preloaded_data = $this->preload_product_generation_data($template_id, $category_id);

            // 获取模板产品的属性
            $attributes = $template_product->get_attributes();
            if (!empty($attributes)) {
                // 【优化】过滤产品属性中的禁用颜色
                $filtered_attributes = $this->filter_product_attributes($attributes, $disabled_color_keywords);
                $new_product->set_attributes($filtered_attributes);
                error_log("[颜色过滤] 复制产品属性: 原始" . count($attributes) . "个，过滤后" . count($filtered_attributes) . "个");

                // 【优化】批量处理默认属性
                $default_attributes = $template_product->get_default_attributes();
                if (!empty($default_attributes)) {
                    // 获取可用的颜色选项用于自动设置新默认值
                    $available_color_options = [];
                    if (isset($filtered_attributes['pa_color']['options'])) {
                        $available_color_options = $filtered_attributes['pa_color']['options'];
                    }

                    // 过滤默认属性中的禁用颜色，并自动设置新默认值
                    $filtered_default_attributes = $this->filter_default_attributes($default_attributes, $disabled_color_keywords, $available_color_options);
                    $new_product->set_default_attributes($filtered_default_attributes);
                    error_log("[颜色过滤] 复制默认属性: " . print_r($filtered_default_attributes, true));
                }
            }
        }

        // 保存产品以获取ID
        $new_product_id = $new_product->save();

        if (!$new_product_id) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 产品保存失败");
            return false;
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 产品初步创建成功，ID: " . $new_product_id);

        // 【修复】将合成图片上传到媒体库，并设置为产品主图 - 传递category_id
        $image_title = $product_name . ' - 主图';
        $attachment_id = $this->attach_image_to_media_library($image_path, $image_title, $category_name, $category_id);

        if (!$attachment_id) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 无法上传图片到媒体库: " . $image_path);
            return false;
        }

        // 设置产品主图
        update_post_meta($new_product_id, '_thumbnail_id', $attachment_id);
        // error_log("[" . date('Y-m-d H:i:s') . "] 成功设置产品主图: 图片ID=" . $attachment_id);

        // 复制产品变体（如果有）
        if ($product_type === 'variable') {
            $template_variations = $this->get_product_variations($template_id);

            if (!empty($template_variations)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 开始复制产品变体: " . count($template_variations) . "个变体");

                foreach ($template_variations as $variation_id) {
                    $result = $this->duplicate_variation($variation_id, $new_product_id, $attachment_id);
                    if (!$result) {
                        // error_log("[" . date('Y-m-d H:i:s') . "] 复制变体失败: variation_id=" . $variation_id);
                    } else {
                        // error_log("[" . date('Y-m-d H:i:s') . "] 成功复制变体: " . $result);
                    }
                }

                // 更新产品以反映新变体
                WC_Product_Variable::sync($new_product_id);

                // 明确设置库存状态为"instock"，确保产品列表显示正确
                $new_product = wc_get_product($new_product_id);
                if ($new_product) {
                    $new_product->set_stock_status('instock');
                    update_post_meta($new_product_id, '_stock_status', 'instock');
                    $new_product->save();
                    wc_delete_product_transients($new_product_id);
                    error_log("[" . date('Y-m-d H:i:s') . "] 已明确设置产品库存状态为'instock'，产品ID: " . $new_product_id);
                }

                // error_log("[" . date('Y-m-d H:i:s') . "] 变体同步完成");
            } else {
                // error_log("[" . date('Y-m-d H:i:s') . "] 模板产品没有变体");
    }
        }

        // 查找SVG预览图，并添加到画廊
        $svg_preview_path = '';
        $upload_dir = wp_upload_dir();
        $svg_filename = basename($svg_file);
        $svg_basename = pathinfo($svg_filename, PATHINFO_FILENAME);

        // 【修复】检查是否为自定义匹配类目，使用专用预览图路径
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
        if ($is_custom_matching) {
            $category_term = get_term($category_id);
            $category_slug = $category_term->slug;
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/' . $svg_basename . '_preview.webp';
            error_log("[修复] 自定义匹配类目 {$category_id} 使用专用预览图路径: {$preview_file}");
        } else {
            // 【修复】使用新的预览图路径结构 - frontend-gallery/{category}/previews/
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/previews/' . $svg_basename . '_preview.webp';
            error_log("[修复] 普通类目 {$category_id} 使用标准预览图路径: {$preview_file}");
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 查找SVG预览图: " . $preview_file);

        // 检查预览图是否存在（预览图在生成时已添加水印）
        if (file_exists($preview_file)) {
            // 【修复】上传SVG预览图到媒体库 - 传递category_id
            $preview_title = $product_name . ' - 设计预览';
            $preview_attachment_id = $this->attach_image_to_media_library($preview_file, $preview_title, $category_name, $category_id);

            if ($preview_attachment_id) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 成功添加SVG预览图到媒体库: 图片ID=" . $preview_attachment_id);

                // 初始化画廊图片ID数组，将预览图放在最前面
                $gallery_image_ids = array($preview_attachment_id);

                // 【修复】生成Gallery模板合成图片 - 传递category_id
                $gallery_template_ids = $this->generate_gallery_template_images($template_id, $preview_file, $safe_category_name, $svg_basename, $category_id);
                if (!empty($gallery_template_ids)) {
                    error_log('[前端Gallery模板] 成功生成 ' . count($gallery_template_ids) . ' 个Gallery模板图片');
                    $gallery_image_ids = array_merge($gallery_image_ids, $gallery_template_ids);
                }

                // 添加模板产品的产品画廊
                $template_gallery_ids = $template_product->get_gallery_image_ids();
                if (!empty($template_gallery_ids)) {
                    $gallery_image_ids = array_merge($gallery_image_ids, $template_gallery_ids);
                }

                // error_log("[" . date('Y-m-d H:i:s') . "] 更新产品画廊: " . count($gallery_image_ids) . "张图片");
                update_post_meta($new_product_id, '_product_image_gallery', implode(',', $gallery_image_ids));
            } else {
                // error_log("[" . date('Y-m-d H:i:s') . "] 无法上传SVG预览图到媒体库");

                // 如果预览图上传失败，仍然复制原有画廊
                $gallery_image_ids = $template_product->get_gallery_image_ids();
                if (!empty($gallery_image_ids)) {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 复制原有产品画廊: " . count($gallery_image_ids) . "张图片");
                    update_post_meta($new_product_id, '_product_image_gallery', implode(',', $gallery_image_ids));
                }
            }
        } else {
            // error_log("[" . date('Y-m-d H:i:s') . "] SVG预览图不存在: " . $preview_file);

            // 直接复制模板产品的画廊
            $gallery_image_ids = $template_product->get_gallery_image_ids();
            if (!empty($gallery_image_ids)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 复制原有产品画廊: " . count($gallery_image_ids) . "张图片");
                update_post_meta($new_product_id, '_product_image_gallery', implode(',', $gallery_image_ids));
            } else {
                // error_log("[" . date('Y-m-d H:i:s') . "] 模板产品没有画廊图片");
            }
        }

        // 添加元数据以标识产品来源
        update_post_meta($new_product_id, '_ss_template_product_id', $template_id);
        update_post_meta($new_product_id, '_ss_svg_filename', basename($svg_file));
        update_post_meta($new_product_id, '_ss_creation_timestamp', current_time('timestamp'));

        // 删除模板相关的元数据
        delete_post_meta($new_product_id, '_ss_is_template');
        delete_post_meta($new_product_id, '_ss_template_image_id');
        delete_post_meta($new_product_id, '_ss_svg_area');
        delete_post_meta($new_product_id, '_ss_shoe_area');
        delete_post_meta($new_product_id, '_ss_gallery_templates');

        // 为产品分配随机排序值 - 已禁用随机排序
        // $random_menu_order = mt_rand(0, 1000000);
        // update_post_meta($new_product_id, '_menu_order', $random_menu_order);

        // 最终保存产品
        $new_product = wc_get_product($new_product_id);
        $new_product->save();

        // error_log("[" . date('Y-m-d H:i:s') . "] 产品创建成功: ID=" . $new_product_id);

        // 【修复】确保产品URL正确生成
        $product_url = get_permalink($new_product_id);
        if (!$product_url) {
            error_log("[产品创建][错误] 无法获取产品URL，产品ID: " . $new_product_id);
            // 尝试手动构建URL
            $product_url = home_url('/product/' . get_post_field('post_name', $new_product_id) . '/');
        }

        error_log("[产品创建][成功] 产品ID: " . $new_product_id . ", URL: " . $product_url);

        return array(
            'product_id' => $new_product_id,
            'product_url' => $product_url
        );
    }

    /**
     * 生成Gallery模板合成图片（前端版本）
     *
     * @param int $template_product_id 模板产品ID
     * @param string $svg_preview_path SVG预览图路径
     * @param string $safe_category_name 安全的分类名称
     * @param string $svg_basename SVG基础文件名
     * @return array 生成的图片附件ID数组
     */
    private function generate_gallery_template_images($template_product_id, $svg_preview_path, $safe_category_name, $svg_basename, $category_id = null) {
        $gallery_template_ids = array();

        // 获取模板产品的Gallery模板设置
        $gallery_templates = get_post_meta($template_product_id, '_ss_gallery_templates', true);

        if (empty($gallery_templates) || !is_array($gallery_templates)) {
            error_log('[前端Gallery模板] 模板产品 ' . $template_product_id . ' 没有Gallery模板设置');
            return $gallery_template_ids;
        }

        // 检查SVG预览图是否存在
        if (empty($svg_preview_path) || !file_exists($svg_preview_path)) {
            error_log('[前端Gallery模板] SVG预览图不存在: ' . $svg_preview_path);
            return $gallery_template_ids;
        }

        error_log('[前端Gallery模板] 开始处理 ' . count($gallery_templates) . ' 个Gallery模板');

        foreach ($gallery_templates as $index => $template) {
            if (empty($template['image_id']) || empty($template['svg_area'])) {
                error_log('[前端Gallery模板] 跳过无效的模板 ' . $index);
                continue;
            }

            // 获取模板图片路径
            $template_image_path = get_attached_file($template['image_id']);
            if (!$template_image_path || !file_exists($template_image_path)) {
                error_log('[前端Gallery模板] 模板图片不存在: ' . $template['image_id']);
                continue;
            }

            // 合成图片
            $composite_result = $this->composite_gallery_template_image(
                $template_image_path,
                $svg_preview_path,
                $template['svg_area'],
                $safe_category_name,
                $svg_basename,
                $index
            );

            if ($composite_result) {
                // 【修复】将合成图片添加到媒体库 - 传递category_id
                $attachment_title = 'Gallery Template ' . ($index + 1) . ' - ' . $svg_basename;
                $attachment_id = $this->attach_image_to_media_library($composite_result, $attachment_title, $safe_category_name, $category_id);
                if ($attachment_id) {
                    $gallery_template_ids[] = $attachment_id;
                    error_log('[前端Gallery模板] 成功生成Gallery模板图片 ' . $index . '，附件ID: ' . $attachment_id);
                } else {
                    error_log('[前端Gallery模板] 无法将Gallery模板图片添加到媒体库: ' . $composite_result);
                }
            } else {
                error_log('[前端Gallery模板] 合成Gallery模板图片失败: ' . $index);
            }
        }

        return $gallery_template_ids;
    }

    /**
     * 合成Gallery模板图片（前端版本）
     *
     * @param string $template_image_path 模板图片路径
     * @param string $svg_preview_path SVG预览图路径
     * @param array $svg_area SVG置入区域
     * @param string $safe_category_name 安全的分类名称
     * @param string $svg_basename SVG基础文件名
     * @param int $template_index 模板索引
     * @return string|false 合成图片路径或false
     */
    private function composite_gallery_template_image($template_image_path, $svg_preview_path, $svg_area, $safe_category_name, $svg_basename, $template_index) {
        try {
            error_log('[前端Gallery模板合成] 开始合成，模板: ' . basename($template_image_path) . '，SVG: ' . basename($svg_preview_path));

            // 检查必要参数
            if (!file_exists($template_image_path) || !file_exists($svg_preview_path)) {
                error_log('[前端Gallery模板合成] 文件不存在');
                return false;
            }

            if (!isset($svg_area['x'], $svg_area['y'], $svg_area['width'], $svg_area['height'])) {
                error_log('[前端Gallery模板合成] SVG区域参数不完整');
                return false;
            }

            // 【修复】创建输出目录 - 根据类目类型使用不同路径
            $upload_dir = wp_upload_dir();

            // 检查是否为自定义匹配类目（通过safe_category_name查找）
            $term = get_term_by('slug', str_replace('_', '-', $safe_category_name), 'product_cat');
            $is_custom_matching = false;
            if ($term && !is_wp_error($term)) {
                $is_custom_matching = get_term_meta($term->term_id, '_is_custom_matching_category', true);
            }

            if ($is_custom_matching && $term) {
                $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/products/' . $term->slug . '/';
                error_log('[修复][前端Gallery模板合成] 自定义匹配类目使用专用Gallery路径: ' . $output_dir);
            } else {
                $output_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/';
                error_log('[修复][前端Gallery模板合成] 普通类目使用标准Gallery路径: ' . $output_dir);
            }

            if (!file_exists($output_dir)) {
                wp_mkdir_p($output_dir);
            }

            // 生成输出文件名
            $output_filename = $svg_basename . '_frontend_gallery_' . $template_index . '_' . uniqid() . '.webp';
            $output_path = $output_dir . $output_filename;

            // 使用Imagick进行合成
            if (class_exists('Imagick')) {
                $template_image = new Imagick($template_image_path);
                $svg_image = new Imagick($svg_preview_path);

                // 调整SVG预览图大小以适应置入区域
                $target_width = intval($svg_area['width']);
                $target_height = intval($svg_area['height']);

                if ($target_width <= 0 || $target_height <= 0) {
                    error_log('[前端Gallery模板合成] 无效的目标尺寸');
                    return false;
                }

                // 【新增】先对SVG预览图进行自动裁剪，去除透明边缘
                error_log('[前端Gallery模板合成] 开始裁剪SVG预览图的透明边缘');
                $cropped_svg = $this->crop_transparent_borders($svg_image);
                if ($cropped_svg) {
                    $svg_image->clear();
                    $svg_image->destroy();
                    $svg_image = $cropped_svg;
                    error_log('[前端Gallery模板合成] SVG预览图裁剪完成');
                } else {
                    error_log('[前端Gallery模板合成] SVG预览图裁剪失败，使用原图');
                }

                // 获取裁剪后的SVG尺寸
                $svg_width = $svg_image->getImageWidth();
                $svg_height = $svg_image->getImageHeight();
                $svg_ratio = $svg_width / $svg_height;
                $area_ratio = $target_width / $target_height;

                error_log(sprintf('[前端Gallery模板合成] 裁剪后SVG尺寸: %dx%d, 宽高比: %.2f', $svg_width, $svg_height, $svg_ratio));
                error_log(sprintf('[前端Gallery模板合成] 目标区域尺寸: %dx%d, 宽高比: %.2f', $target_width, $target_height, $area_ratio));

                // 【修复】计算缩放尺寸，确保SVG完全包含在置入区域内（不超出边界）
                if ($svg_ratio > $area_ratio) {
                    // SVG更宽，以宽度为基准，确保不超出左右边界
                    $scaled_width = $target_width;
                    $scaled_height = intval($target_width / $svg_ratio);
                    error_log('[前端Gallery模板合成] SVG较宽，以宽度为基准缩放，确保不超出左右边界');
                } else {
                    // SVG更高或相等，以高度为基准，确保不超出上下边界
                    $scaled_height = $target_height;
                    $scaled_width = intval($target_height * $svg_ratio);
                    error_log('[前端Gallery模板合成] SVG较高，以高度为基准缩放，确保不超出上下边界');
                }

                error_log(sprintf('[前端Gallery模板合成] 修复后缩放尺寸: %dx%d（确保完全包含在 %dx%d 区域内）', $scaled_width, $scaled_height, $target_width, $target_height));

                // 调整SVG预览图大小
                $svg_image->resizeImage($scaled_width, $scaled_height, Imagick::FILTER_LANCZOS, 1);

                // 计算置入位置（水平居中，垂直靠近上方）
                $x = intval(intval($svg_area['x']) + ($target_width - $scaled_width) / 2);
                $y = intval($svg_area['y']); // 靠近上方边缘，不居中

                error_log(sprintf('[前端Gallery模板合成] 置入位置: x=%d, y=%d（水平居中，垂直靠上，确保在区域内）', $x, $y));

                // 合成图片（SVG预览图已包含水印，无需重复添加）
                $template_image->compositeImage($svg_image, Imagick::COMPOSITE_OVER, $x, $y);

                // 设置输出格式和质量
                $template_image->setImageFormat('webp');
                $template_image->setImageCompressionQuality(90);

                // 保存合成图片
                $template_image->writeImage($output_path);

                // 清理资源
                $template_image->clear();
                $template_image->destroy();
                $svg_image->clear();
                $svg_image->destroy();

                error_log('[前端Gallery模板合成] 成功合成图片: ' . $output_path);
                return $output_path;
            } else {
                error_log('[前端Gallery模板合成] Imagick扩展不可用');
                return false;
            }

        } catch (Exception $e) {
            error_log('[前端Gallery模板合成] 异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 将图片附加到媒体库
     * 将本地图片添加到WordPress媒体库，并转换为webp格式
     * 【优化】支持指定类目文件夹保存路径
     */
    private function attach_image_to_media_library($image_path, $title, $category_name = null, $category_id = null) {
        // 检查文件是否存在
        if (!file_exists($image_path)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 附加图片到媒体库失败: 文件不存在 - " . $image_path);
            return false;
        }

        // 获取WordPress上传目录
        $uploads_dir = wp_upload_dir();
        $uploads_basedir = $uploads_dir['basedir'];
        $uploads_baseurl = $uploads_dir['baseurl'];

        // 准备webp文件名
        $filename = pathinfo(basename($image_path), PATHINFO_FILENAME);
        $webp_filename = $filename . '.webp';

        // 【修复】根据类目类型决定保存路径
        if (!empty($category_name)) {
            // 检查是否为自定义匹配类目
            $is_custom_matching = false;
            $category_slug = '';

            if ($category_id) {
                $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
                if ($is_custom_matching) {
                    $category_term = get_term($category_id);
                    $category_slug = $category_term->slug;
                }
            }

            if ($is_custom_matching && !empty($category_slug)) {
                // 自定义匹配类目使用专用路径：/uploads/shoe-svg-generator/custom-matching/products/{category-slug}/
                $target_dir = $uploads_basedir . '/shoe-svg-generator/custom-matching/products/' . $category_slug;
                $relative_path = 'shoe-svg-generator/custom-matching/products/' . $category_slug . '/' . $webp_filename;
                $file_url = $uploads_baseurl . '/shoe-svg-generator/custom-matching/products/' . $category_slug . '/' . $webp_filename;
                error_log('【修复】【前端图片上传】自定义匹配类目使用专用产品路径: ' . $target_dir);
            } else {
                // 普通类目使用标准路径：/uploads/shoe-svg-generator/frontend-gallery/类目名称/
                $safe_category_name = $this->get_safe_category_name($category_name);
                $target_dir = $uploads_basedir . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name;
                $relative_path = 'shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/' . $webp_filename;
                $file_url = $uploads_baseurl . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/' . $webp_filename;
                error_log('【修复】【前端图片上传】普通类目使用标准路径: ' . $target_dir);
            }
        } else {
            // 使用默认的年份目录路径
            $target_year_month = date('Y/m');
            $target_dir = $uploads_basedir . '/' . $target_year_month;
            $relative_path = $target_year_month . '/' . $webp_filename;
            $file_url = $uploads_baseurl . '/' . $target_year_month . '/' . $webp_filename;
            error_log('【修复】【前端图片上传】使用默认年份目录路径: ' . $target_dir);
        }

        // 确保目标目录存在
        if (!$this->ensure_directory($target_dir)) {
            error_log('【前端图片上传】无法创建目标目录: ' . $target_dir);
            return false;
        }

        $target_path = $target_dir . '/' . $webp_filename;

        try {
            // 使用Imagick转换图片为webp格式
            if (class_exists('Imagick')) {
                $imagick = new Imagick($image_path);
                $imagick->setImageFormat('webp');
                $imagick->setImageCompressionQuality(90); // 设置较高的质量
                $imagick->writeImage($target_path);
                $imagick->clear();
                $imagick->destroy();
                // error_log("[" . date('Y-m-d H:i:s') . "] 成功转换图片为WebP格式: " . $target_path);
            } else {
                // 如果没有Imagick，尝试使用GD库
                if (function_exists('imagecreatefromstring') && function_exists('imagewebp')) {
                    $image_data = file_get_contents($image_path);
                    if ($image_data === false) {
                        // error_log("[" . date('Y-m-d H:i:s') . "] 无法读取源图片文件");
                        return false;
                    }

                    $source_image = imagecreatefromstring($image_data);
                    if ($source_image === false) {
                        // error_log("[" . date('Y-m-d H:i:s') . "] 无法创建图片资源");
                        return false;
                    }

                    // 保存为webp格式
                    imagewebp($source_image, $target_path, 90);
                    imagedestroy($source_image);
                    // error_log("[" . date('Y-m-d H:i:s') . "] 使用GD库转换图片为WebP格式: " . $target_path);
                } else {
                    // error_log("[" . date('Y-m-d H:i:s') . "] 服务器不支持WebP转换所需的扩展");
                    return false;
                }
            }

            // 验证webp文件是否成功创建
            if (!file_exists($target_path)) {
                // error_log("[" . date('Y-m-d H:i:s') . "] WebP文件创建失败: " . $target_path);
                return false;
            }

            // 准备附件数据
            $attachment = [
                'post_title' => sanitize_text_field($title),
                'post_mime_type' => 'image/webp',
                'post_status' => 'inherit',
                'guid' => $file_url
            ];

            // 使用相对路径创建附件
            $attachment_id = wp_insert_attachment($attachment, $relative_path);

        if (is_wp_error($attachment_id)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 创建附件失败: " . $attachment_id->get_error_message());
            return false;
        }

        if (!$attachment_id) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 创建附件返回无效ID");
            return false;
        }

        // 包含图像处理相关功能
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        // 生成附件的元数据
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $target_path);

        // 【修复】确保data-large_image指向原始大图而不是缩略图
        if (is_array($attachment_data)) {
            // 获取原始图片的尺寸
            $image_size = getimagesize($target_path);
            if ($image_size) {
                $attachment_data['width'] = $image_size[0];
                $attachment_data['height'] = $image_size[1];

                // 确保原始图片信息正确
                $attachment_data['file'] = $relative_path;

                error_log("[附件元数据修复] 原始图片尺寸: {$image_size[0]}x{$image_size[1]}, 文件: {$relative_path}");
            }

            // 【修复】强制设置原始图片URL为large_image
            if (isset($attachment_data['sizes'])) {
                // 移除可能导致混淆的large尺寸，确保使用原始图片
                if (isset($attachment_data['sizes']['large'])) {
                    unset($attachment_data['sizes']['large']);
                    error_log("[附件元数据修复] 移除large尺寸，强制使用原始图片");
                }

                // 确保原始图片作为最大尺寸
                $attachment_data['sizes']['full'] = [
                    'file' => basename($relative_path),
                    'width' => $image_size[0] ?? 1000,
                    'height' => $image_size[1] ?? 1000,
                    'mime-type' => 'image/webp'
                ];
            }
        }

        // 更新附件元数据
        $update_result = wp_update_attachment_metadata($attachment_id, $attachment_data);

        if (is_wp_error($update_result)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 更新附件元数据失败: " . $update_result->get_error_message());
        } elseif ($update_result === false) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 更新附件元数据失败: 返回false");
        } else {
                // error_log("[" . date('Y-m-d H:i:s') . "] WebP附件创建成功: ID=" . $attachment_id);
        }

        // 【新增】自动清理临时文件（Gallery和Previews）
        if (class_exists('SS_SVG_Generator')) {
            $generator = SS_SVG_Generator::get_instance();
            $reflection = new ReflectionClass($generator);
            $method = $reflection->getMethod('auto_cleanup_gallery_temp_file');
            $method->setAccessible(true);
            $method->invoke($generator, $image_path, $target_path);
        }

        // 【修复】只对普通类目的final-products目录进行清理，自定义匹配类目的文件需要保留
        // 检查是否是自定义匹配类目的产品文件
        $is_custom_matching_file = strpos($image_path, '/shoe-svg-generator/custom-matching/products/') !== false;

        if (!$is_custom_matching_file) {
            // 只清理普通类目的临时文件
            $this->cleanup_frontend_temp_directory($image_path, $category_name);
            error_log("[文件清理] 普通类目文件已清理: " . basename($image_path));
        } else {
            // 自定义匹配类目的文件需要保留，不进行清理
            error_log("[文件清理] 自定义匹配类目文件保留，不清理: " . basename($image_path));
        }

        return $attachment_id;

        } catch (Exception $e) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 转换图片为WebP格式时发生错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 【修复】清理前端产品主图临时目录
     * 在产品主图成功上传到WordPress媒体库后，自动清理临时文件和目录
     * 【重要】只清理final-products目录下的临时文件，不清理custom-matching目录
     *
     * @param string $original_path 原始临时文件路径
     * @param string $category_name 类目名称
     */
    private function cleanup_frontend_temp_directory($original_path, $category_name = null) {
        // 检查是否是前端产品主图的临时文件
        if (empty($original_path) || !file_exists($original_path)) {
            return;
        }

        // 【修复】只清理final-products目录下的临时文件
        // 自定义匹配类目的文件存储在custom-matching/products/目录，这些文件需要保留
        if (strpos($original_path, '/shoe-svg-generator/final-products/') === false) {
            error_log('【前端临时清理】跳过清理，不是final-products目录下的文件: ' . basename($original_path));
            return;
        }

        // 【修复】额外检查：确保这是images子目录下的文件
        if (strpos($original_path, '/images/') === false) {
            error_log('【前端临时清理】跳过清理，不是images目录下的文件: ' . basename($original_path));
            return;
        }

        error_log('【前端临时清理】开始清理产品主图临时文件: ' . basename($original_path));

        // 删除临时文件
        if (unlink($original_path)) {
            error_log('【前端临时清理】已删除临时文件: ' . basename($original_path));

            // 获取临时文件所在的images目录
            $temp_images_dir = dirname($original_path);

            // 检查images目录是否为空，如果为空则删除
            $this->cleanup_empty_directory($temp_images_dir, 'images');

            // 获取类目目录（images的父目录）
            $temp_category_dir = dirname($temp_images_dir);

            // 检查类目目录是否为空，如果为空则删除
            $this->cleanup_empty_directory($temp_category_dir, 'category');

        } else {
            error_log('【前端临时清理】删除临时文件失败: ' . basename($original_path));
        }
    }

    /**
     * 【新增】清理空的目录
     * 检查目录是否为空，如果为空则删除
     *
     * @param string $dir_path 目录路径
     * @param string $dir_type 目录类型（用于日志）
     */
    private function cleanup_empty_directory($dir_path, $dir_type = 'directory') {
        if (!is_dir($dir_path)) {
            return;
        }

        // 检查目录是否为空
        $files = array_diff(scandir($dir_path), array('.', '..'));
        if (empty($files)) {
            if (rmdir($dir_path)) {
                error_log('【前端临时清理】已删除空的' . $dir_type . '目录: ' . basename($dir_path));
            } else {
                error_log('【前端临时清理】删除空' . $dir_type . '目录失败: ' . basename($dir_path));
            }
        } else {
            error_log('【前端临时清理】' . $dir_type . '目录不为空，保留: ' . basename($dir_path) . ' (剩余 ' . count($files) . ' 个文件)');
        }
    }

    /**
     * 获取产品变体
     */
    private function get_product_variations($product_id) {
        global $wpdb;

        // error_log("[" . date('Y-m-d H:i:s') . "] 获取产品变体: product_id=" . $product_id);

        $variations = $wpdb->get_col($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} WHERE post_parent = %d AND post_type = 'product_variation' AND post_status = 'publish'",
            $product_id
        ));

        if (empty($variations)) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 未找到产品变体");
            return array();
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 找到产品变体: " . count($variations) . "个: " . implode(', ', $variations));

        return $variations;
    }

    /**
     * 复制产品变体
     */
    private function duplicate_variation($variation_id, $new_parent_id, $new_image_id) {
        // error_log("[" . date('Y-m-d H:i:s') . "] 开始复制变体: variation_id=" . $variation_id . ", new_parent_id=" . $new_parent_id);

        // 获取原始变体
        $variation = wc_get_product($variation_id);
        if (!$variation || !is_a($variation, 'WC_Product_Variation')) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 无法获取原始变体或类型不正确");
            return false;
        }

        // 【移除】变体层面的颜色过滤已移至产品属性层面处理
        // 现在在产品属性复制时就已经过滤了禁用的颜色，变体会自动继承过滤后的属性

        // 创建新变体
        $new_variation = new WC_Product_Variation();
        $new_variation->set_parent_id($new_parent_id);

        // 复制变体属性
        $new_variation->set_attributes($variation->get_attributes());
        $new_variation->set_status($variation->get_status());
        $new_variation->set_regular_price($variation->get_regular_price());
        $new_variation->set_sale_price($variation->get_sale_price());
        $new_variation->set_price($variation->get_price());
        $new_variation->set_date_on_sale_from($variation->get_date_on_sale_from());
        $new_variation->set_date_on_sale_to($variation->get_date_on_sale_to());
        $new_variation->set_tax_status($variation->get_tax_status());
        $new_variation->set_tax_class($variation->get_tax_class());
        $new_variation->set_manage_stock($variation->get_manage_stock());
        $new_variation->set_stock_quantity($variation->get_stock_quantity());
        $new_variation->set_stock_status($variation->get_stock_status());
        $new_variation->set_backorders($variation->get_backorders());
        $new_variation->set_weight($variation->get_weight());
        $new_variation->set_length($variation->get_length());
        $new_variation->set_width($variation->get_width());
        $new_variation->set_height($variation->get_height());
        $new_variation->set_shipping_class_id($variation->get_shipping_class_id());
        $new_variation->set_image_id($new_image_id); // 使用新产品的主图
        $new_variation->set_downloadable($variation->get_downloadable());

        // 如果是可下载的产品，复制下载设置
        if ($variation->get_downloadable()) {
            $new_variation->set_downloads($variation->get_downloads());
            $new_variation->set_download_limit($variation->get_download_limit());
            $new_variation->set_download_expiry($variation->get_download_expiry());
        }

        // 获取变体SKU并添加后缀以确保唯一性
        $sku = $variation->get_sku();
        if (!empty($sku)) {
            $new_sku = $sku . '-' . uniqid();
            $new_variation->set_sku($new_sku);
            // error_log("[" . date('Y-m-d H:i:s') . "] 设置新变体SKU: " . $new_sku);
        }

        // 保存新变体
        $new_variation_id = $new_variation->save();

        if (!$new_variation_id) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 保存新变体失败");
            return false;
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 变体复制成功: 新变体ID=" . $new_variation_id);

        return $new_variation_id;
    }

    /**
     * 记录SVG产品关系
     * 记录SVG设计、模板和生成的产品之间的关系
     */
    private function record_svg_product_relation($template_id, $svg_file, $category_id, $product_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'svg_product_relations';

        // 先检查表是否存在，不存在则创建
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 记录关系前创建SVG产品关系表");
            $this->create_svg_product_relations_table();

            // 再次检查表是否创建成功
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 无法创建SVG产品关系表，使用元数据记录关系");
                // 如果表创建失败，使用产品元数据记录关系
                update_post_meta($product_id, '_svg_template_id', $template_id);
                update_post_meta($product_id, '_svg_file', $svg_file);
                update_post_meta($product_id, '_svg_category_id', $category_id);
        return true;
            }
        }

        // 检查记录是否已经存在
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE template_id = %d AND svg_file = %s AND category_id = %d AND product_id = %d",
            $template_id, $svg_file, $category_id, $product_id
        ));

        if ($existing) {
            // error_log("[" . date('Y-m-d H:i:s') . "] SVG产品关系记录已存在: ID=" . $existing);
            return true;
        }

        // 插入记录
        $result = $wpdb->insert(
            $table_name,
            [
                'template_id' => $template_id,
                'svg_file' => $svg_file,
                'category_id' => $category_id,
                'product_id' => $product_id,
                'created_at' => current_time('mysql')
            ],
            ['%d', '%s', '%d', '%d', '%s']
        );

        if ($result === false) {
            // error_log("[" . date('Y-m-d H:i:s') . "] 插入SVG产品关系记录失败: " . $wpdb->last_error);

            // 备用方案：使用产品元数据
            update_post_meta($product_id, '_svg_template_id', $template_id);
            update_post_meta($product_id, '_svg_file', $svg_file);
            update_post_meta($product_id, '_svg_category_id', $category_id);

            // error_log("[" . date('Y-m-d H:i:s') . "] 已使用产品元数据记录SVG关系");
            return true;
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 成功记录SVG产品关系: ID=" . $wpdb->insert_id);
        return true;
    }

    /**
     * 获取现有SVG产品
     * 根据模板、SVG文件和类目查询是否已存在对应产品
     */
    private function get_existing_svg_product($template_id, $svg_file, $category_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'svg_product_relations';

        // 检查表是否存在
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            // error_log("[" . date('Y-m-d H:i:s') . "] SVG产品关系表不存在");

            // 尝试从元数据中查找匹配的产品
            $args = [
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 1,
                'meta_query' => [
                    'relation' => 'AND',
                    [
                        'key' => '_svg_template_id',
                        'value' => $template_id,
                        'compare' => '='
                    ],
                    [
                        'key' => '_svg_file',
                        'value' => $svg_file,
                        'compare' => '='
                    ],
                    [
                        'key' => '_svg_category_id',
                        'value' => $category_id,
                        'compare' => '='
                    ]
                ]
            ];

            $query = new WP_Query($args);
            if ($query->have_posts()) {
                $query->the_post();
                $product_id = get_the_ID();
                wp_reset_postdata();

                // error_log("[" . date('Y-m-d H:i:s') . "] 从元数据中找到匹配产品: ID=" . $product_id);
                return [
                    'product_id' => $product_id,
                    'template_id' => $template_id,
                    'svg_file' => $svg_file,
                    'category_id' => $category_id
                ];
            }

            return null;
        }

        // 查询关系表
        $product = $wpdb->get_row($wpdb->prepare(
            "SELECT product_id, template_id, svg_file, category_id FROM $table_name
             WHERE template_id = %d AND svg_file = %s AND category_id = %d LIMIT 1",
            $template_id, $svg_file, $category_id
        ));

        if ($product) {
            // 确认产品仍然存在
            $product_exists = get_post($product->product_id);
            if (!$product_exists) {
                // error_log("[" . date('Y-m-d H:i:s') . "] 关系表中的产品不存在，删除关系记录: ID=" . $product->product_id);
                $wpdb->delete(
                    $table_name,
                    ['product_id' => $product->product_id],
                    ['%d']
                );
                return null;
            }

            // error_log("[" . date('Y-m-d H:i:s') . "] 找到现有产品: ID=" . $product->product_id);
            return [
                'product_id' => $product->product_id,
                'template_id' => $product->template_id,
                'svg_file' => $product->svg_file,
                'category_id' => $product->category_id
            ];
        }

        // error_log("[" . date('Y-m-d H:i:s') . "] 未找到现有产品");
        return null;
    }

    /**
     * 检查SVG产品是否存在
     * AJAX处理方法，检查指定SVG和模板产品的组合是否已存在产品
     */
    public function ss_check_svg_product_exists() {
        // 【修复】增加超时处理和错误日志
        set_time_limit(60); // 设置60秒超时
        
        // 验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            error_log('[AJAX检查产品][修复] nonce验证失败');
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        // 获取参数
        $template_id = isset($_POST['template_id']) ? intval($_POST['template_id']) : 0;
        $svg_file = isset($_POST['svg_file']) ? sanitize_text_field($_POST['svg_file']) : '';
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        error_log("[AJAX检查产品][修复] 开始检查: 模板ID={$template_id}, SVG文件={$svg_file}, 类目ID={$category_id}");

        // 验证参数
        if (!$template_id || empty($svg_file) || !$category_id) {
            error_log('[AJAX检查产品][修复] 参数验证失败: ' . json_encode([
                'template_id' => $template_id,
                'svg_file' => $svg_file,
                'category_id' => $category_id
            ]));
            wp_send_json_error(['message' => '缺少必要参数']);
            return;
        }

        try {
            // 【修复】检查是否为自定义匹配类目
            $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
            error_log("[AJAX检查产品][修复] 是否自定义匹配类目: " . ($is_custom_matching ? '是' : '否'));

            // 【修复】验证SVG文件是否存在 - 使用basename提取文件名
            if (class_exists('SS_SVG_File_Manager')) {
                $svg_file_manager = SS_SVG_File_Manager::get_instance();
                $svg_filename = basename($svg_file); // 【修复】提取文件名，避免路径问题

                error_log("[AJAX检查产品][修复] 原始SVG参数: {$svg_file}, 提取的文件名: {$svg_filename}");

                // 【修复】首先检查JSON记录是否存在，如果不存在则尝试生成
                $json_record = $svg_file_manager->get_category_json_record($category_id);
                if (!$json_record) {
                    error_log("[AJAX检查产品][修复] 类目 {$category_id} 没有JSON记录，尝试生成");
                    $generate_result = $svg_file_manager->generate_category_json_record($category_id);
                    if ($generate_result['success']) {
                        error_log("[AJAX检查产品][修复] 成功生成JSON记录，包含 {$generate_result['files_count']} 个文件");
                    } else {
                        error_log("[AJAX检查产品][修复] 生成JSON记录失败: {$generate_result['message']}");
                    }
                }

                $svg_file_path = $svg_file_manager->get_svg_file_path($svg_filename, $category_id);
                
                if (!$svg_file_path) {
                    error_log("[AJAX检查产品][优化] SVG文件不存在，但继续检查产品: {$svg_filename}");
                    // 【优化】不再依赖原始SVG文件，直接使用预览图进行产品生成
                    // 即使SVG文件不存在，我们仍然可以检查是否已有产品生成
                }

                if ($svg_file_path) {
                    error_log("[AJAX检查产品][修复] SVG文件路径: {$svg_file_path}");
                }
            }

        // error_log("[" . date('Y-m-d H:i:s') . "] 检查SVG产品是否存在: template_id=" . $template_id . ", svg_file=" . $svg_file . ", category_id=" . $category_id);

        // 检查产品表是否存在，不存在则创建
        global $wpdb;
        $table_name = $wpdb->prefix . 'svg_product_relations';
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            // error_log("[" . date('Y-m-d H:i:s') . "] SVG产品关系表不存在");
            $this->create_svg_product_relations_table();
        }

        // 查找产品 - 【修复】使用文件名而不是完整路径
        $existing_product = $this->get_existing_svg_product($template_id, $svg_filename, $category_id);

            if ($existing_product) {
                $product_url = get_permalink($existing_product['product_id']);
                error_log("[AJAX检查产品][修复] 找到现有产品: ID=" . $existing_product['product_id'] . ", URL=" . $product_url);

                wp_send_json_success([
                    'exists' => true,
                    'product_id' => $existing_product['product_id'],
                    'product_url' => $product_url
                ]);
            } else {
                error_log("[AJAX检查产品][修复] 未找到现有产品");

                wp_send_json_success([
                    'exists' => false
                ]);
            }
            
        } catch (Exception $e) {
            error_log("[AJAX检查产品][修复] 异常错误: " . $e->getMessage());
            wp_send_json_error(['message' => 'Exception: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取模板产品的展示信息
     *
     * @param int $product_id 模板产品ID
     * @return array 包含展示标题和图片URL的数组
     */
    public function get_template_display_info($product_id) {
        $display_info = array(
            'title' => '',
            'image_url' => ''
        );

        // 获取自定义展示标题
        $display_title = get_post_meta($product_id, '_ss_template_display_title', true);
        if (!empty($display_title)) {
            $display_info['title'] = $display_title;
        } else {
            // 如果没有自定义标题，使用产品标题
            $display_info['title'] = get_the_title($product_id);
        }

        // 获取自定义展示图片
        $display_image_id = get_post_meta($product_id, '_ss_template_display_image', true);
        if (!empty($display_image_id)) {
            $display_info['image_url'] = wp_get_attachment_image_url($display_image_id, 'medium');
        } else {
            // 如果没有自定义图片，使用产品特色图片
            $display_info['image_url'] = get_the_post_thumbnail_url($product_id, 'medium');
        }

        return $display_info;
    }

    /**
     * 修改从AJAX返回的模板数据，使用自定义展示设置
     *
     * @param array $templates 模板数据数组
     * @return array 修改后的模板数据
     */
    public function apply_template_display_settings($templates) {
        if (empty($templates) || !is_array($templates)) {
            return $templates;
        }

        foreach ($templates as &$template) {
            if (isset($template['id'])) {
                $display_info = $this->get_template_display_info($template['id']);

                // 使用自定义展示标题（如果有）
                if (!empty($display_info['title'])) {
                    $template['title'] = $display_info['title'];
                }

                // 使用自定义展示图片（如果有）
                if (!empty($display_info['image_url'])) {
                    $template['image'] = $display_info['image_url'];
                }
            }
        }

        return $templates;
    }

    /**
     * 将内存字符串转换为字节数
     *
     * @param string $val 内存字符串，如 '128M'
     * @return int 字节数
     */
    private function return_bytes($val) {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;

        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }

    /**
     * 格式化字节数为人类可读格式
     *
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string 格式化后的字符串
     */
    private function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * 裁剪图片的透明边缘
     *
     * @param Imagick $image 要裁剪的图片对象
     * @return Imagick|false 裁剪后的图片对象或false
     */
    private function crop_transparent_borders($image) {
        try {
            // 克隆原图像以避免修改原始对象
            $cropped_image = clone $image;

            // 获取原始尺寸
            $original_width = $cropped_image->getImageWidth();
            $original_height = $cropped_image->getImageHeight();

            error_log(sprintf('[图片裁剪] 原始尺寸: %dx%d', $original_width, $original_height));

            // 使用trim方法自动裁剪透明边缘
            // fuzz参数设置为0表示严格匹配透明像素
            $cropped_image->trimImage(0);

            // 获取裁剪后的尺寸
            $cropped_width = $cropped_image->getImageWidth();
            $cropped_height = $cropped_image->getImageHeight();

            error_log(sprintf('[图片裁剪] 裁剪后尺寸: %dx%d', $cropped_width, $cropped_height));

            // 检查是否实际进行了裁剪
            if ($cropped_width == $original_width && $cropped_height == $original_height) {
                error_log('[图片裁剪] 没有检测到透明边缘，无需裁剪');
                $cropped_image->clear();
                $cropped_image->destroy();
                return false;
            }

            // 检查裁剪后的图片是否太小
            if ($cropped_width < 50 || $cropped_height < 50) {
                error_log('[图片裁剪] 裁剪后图片太小，使用原图');
                $cropped_image->clear();
                $cropped_image->destroy();
                return false;
            }

            error_log(sprintf('[图片裁剪] 成功裁剪透明边缘，尺寸从 %dx%d 变为 %dx%d',
                $original_width, $original_height, $cropped_width, $cropped_height));

            return $cropped_image;

        } catch (Exception $e) {
            error_log('[图片裁剪] 裁剪过程中发生错误: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 【新增】过滤不兼容的产品模板
     * 当类目使用了Color Science预设颜色（黑白灰）时，隐藏只包含对应颜色属性的模板
     *
     * @param array $template_products 模板产品数组
     * @param array $disabled_keywords 禁用的颜色关键词
     * @return array 过滤后的模板产品数组
     */
    private function filter_incompatible_templates($template_products, $disabled_keywords) {
        if (empty($disabled_keywords) || empty($template_products)) {
            return $template_products;
        }

        error_log("[模板过滤] 开始过滤不兼容模板，禁用关键词: " . implode(', ', $disabled_keywords));

        $filtered_templates = [];

        foreach ($template_products as $product) {
            $product_id = $product->ID;
            $product_title = $product->post_title;

            // 获取产品的颜色属性
            $wc_product = wc_get_product($product_id);
            if (!$wc_product) {
                error_log("[模板过滤] 无法获取产品对象: {$product_id}");
                continue;
            }

            $attributes = $wc_product->get_attributes();
            $color_attributes = [];

            // 查找颜色属性
            foreach ($attributes as $attribute_name => $attribute) {
                if (strpos(strtolower($attribute_name), 'color') !== false || 
                    strpos(strtolower($attribute_name), '颜色') !== false) {
                    $color_attributes[$attribute_name] = $attribute;
                }
            }

            if (empty($color_attributes)) {
                // 没有颜色属性的模板保留
                $filtered_templates[] = $product;
                error_log("[模板过滤] 保留模板 '{$product_title}' (ID: {$product_id}) - 无颜色属性");
                continue;
            }

            // 检查是否只包含禁用的颜色
            $has_valid_colors = false;

            foreach ($color_attributes as $attribute_name => $attribute) {
                $color_options = [];

                if ($attribute->is_taxonomy()) {
                    // 分类属性
                    $terms = $attribute->get_terms();
                    if (!is_wp_error($terms) && !empty($terms)) {
                        foreach ($terms as $term) {
                            $color_options[] = [
                                'slug' => $term->slug,
                                'name' => $term->name
                            ];
                        }
                    }
                } else {
                    // 自定义属性
                    $options = $attribute->get_options();
                    foreach ($options as $option) {
                        $color_options[] = [
                            'slug' => sanitize_title($option),
                            'name' => $option
                        ];
                    }
                }

                // 检查是否有非禁用的颜色选项
                foreach ($color_options as $color_option) {
                    $is_disabled = false;

                    foreach ($disabled_keywords as $keyword) {
                        if (stripos($color_option['slug'], $keyword) !== false || 
                            stripos($color_option['name'], $keyword) !== false) {
                            $is_disabled = true;
                            break;
                        }
                    }

                    if (!$is_disabled) {
                        $has_valid_colors = true;
                        error_log("[模板过滤] 模板 '{$product_title}' 包含有效颜色: {$color_option['name']} ({$color_option['slug']})");
                        break 2; // 跳出两层循环
                    }
                }
            }

            if ($has_valid_colors) {
                // 有有效颜色的模板保留
                $filtered_templates[] = $product;
                error_log("[模板过滤] 保留模板 '{$product_title}' (ID: {$product_id}) - 包含有效颜色");
            } else {
                // 只有禁用颜色的模板过滤掉
                error_log("[模板过滤] 过滤掉模板 '{$product_title}' (ID: {$product_id}) - 仅包含禁用颜色");
            }
        }

        error_log("[模板过滤] 过滤完成，原始模板数: " . count($template_products) . "，过滤后模板数: " . count($filtered_templates));

        return $filtered_templates;
    }

    /**
     * 【新增】检查产品是否只包含指定的颜色属性
     * 辅助函数，用于判断产品模板是否与类目预设颜色冲突
     *
     * @param int $product_id 产品ID
     * @param array $color_keywords 要检查的颜色关键词
     * @return bool 是否只包含指定颜色
     */
    private function product_has_only_specified_colors($product_id, $color_keywords) {
        $wc_product = wc_get_product($product_id);
        if (!$wc_product) {
            return false;
        }

        $attributes = $wc_product->get_attributes();
        $color_attributes = [];

        // 查找颜色属性
        foreach ($attributes as $attribute_name => $attribute) {
            if (strpos(strtolower($attribute_name), 'color') !== false || 
                strpos(strtolower($attribute_name), '颜色') !== false) {
                $color_attributes[$attribute_name] = $attribute;
            }
        }

        if (empty($color_attributes)) {
            return false; // 没有颜色属性
        }

        // 检查所有颜色选项是否都在指定关键词中
        foreach ($color_attributes as $attribute_name => $attribute) {
            $color_options = [];

            if ($attribute->is_taxonomy()) {
                $terms = $attribute->get_terms();
                if (!is_wp_error($terms) && !empty($terms)) {
                    foreach ($terms as $term) {
                        $color_options[] = [
                            'slug' => $term->slug,
                            'name' => $term->name
                        ];
                    }
                }
            } else {
                $options = $attribute->get_options();
                foreach ($options as $option) {
                    $color_options[] = [
                        'slug' => sanitize_title($option),
                        'name' => $option
                    ];
                }
            }

            // 检查是否有不在关键词中的颜色
            foreach ($color_options as $color_option) {
                $found_keyword = false;

                foreach ($color_keywords as $keyword) {
                    if (stripos($color_option['slug'], $keyword) !== false || 
                        stripos($color_option['name'], $keyword) !== false) {
                        $found_keyword = true;
                        break;
                    }
                }

                if (!$found_keyword) {
                    return false; // 找到了不在关键词中的颜色
                }
            }
        }

        return true; // 所有颜色都在指定关键词中
    }

    /**
     * 【新增】获取立即框架数据 - 确保页面立即渲染
     * 
     * @param int $term_id 类目ID
     * @return array 框架数据
     */
    private function get_immediate_framework_data($term_id) {
        $svg_files = $this->get_svg_files();
        $framework_data = [
            'term_id' => $term_id,
            'total_designs' => count($svg_files),
            'framework_ready' => true,
            'timestamp' => time()
        ];
        
        error_log("[立即加载] 类目 {$term_id} 框架数据准备完成，设计总数: " . count($svg_files));
        return $framework_data;
    }

    /**
     * 【新增】获取缓存的类目预览图
     * 检查指定类目是否已有生成的预览图，如果有则直接返回
     *
     * @param int $term_id 类目ID
     * @param string $category_name 类目名称
     * @return array 缓存的预览图数据
     */
    private function get_cached_category_previews($term_id, $category_name) {
        // 【优化】使用内存缓存加速重复检查
        $cache_key = "cached_previews_{$term_id}";
        $cached_result = wp_cache_get($cache_key, 'ss_cached_previews');

        if ($cached_result !== false) {
            error_log("[缓存优化] 从内存缓存获取类目 {$term_id} 预览图数据");
            return $cached_result;
        }

        $upload_dir = wp_upload_dir();

        // 【修复】检查是否为自定义匹配类目，使用专用路径
        $is_custom_matching = get_term_meta($term_id, '_is_custom_matching_category', true);
        if (!empty($is_custom_matching)) {
            $term = get_term($term_id);
            $category_slug = $term->slug;
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/';
            $preview_base_url = $upload_dir['baseurl'] . '/shoe-svg-generator/custom-matching/previews/' . $category_slug . '/';
            error_log("[缓存优化] 自定义匹配类目 {$term_id} 使用专用预览路径: {$preview_dir}");
        } else {
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
            $preview_base_url = $upload_dir['baseurl'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
        }
        
        // 检查预览目录是否存在
        if (!is_dir($preview_dir)) {
            error_log("[缓存优化] 类目 {$term_id} 预览目录不存在: {$preview_dir}");
            wp_cache_set($cache_key, [], 'ss_cached_previews', 300); // 缓存5分钟
            return [];
        }

        $svg_files = $this->get_svg_files();
        $cached_previews = [];
        $found_count = 0;

        // 【修复】添加详细的调试日志
        error_log("[缓存优化][修复] 开始检查 " . count($svg_files) . " 个SVG文件的预览图");

        foreach ($svg_files as $index => $svg_file) {
            $file_name = basename($svg_file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
            $preview_path = $preview_dir . $safe_name . '_preview.webp';

            if (file_exists($preview_path)) {
                $preview_url = $preview_base_url . $safe_name . '_preview.webp';
                $cached_previews[] = [
                    'index' => $index,
                    'file' => $file_name,
                    'preview_url' => $preview_url,
                    'status' => 'cached',
                    'file_size' => filesize($preview_path),
                    'modified_time' => filemtime($preview_path)
                ];
                $found_count++;

                // 【修复】记录前几个找到的预览图
                if ($found_count <= 5) {
                    error_log("[缓存优化][修复] 找到预览图 #{$found_count}: 索引={$index}, 文件={$file_name}");
                }
            } else {
                // 【修复】记录前几个未找到的预览图
                if (count($svg_files) - $found_count <= 5) {
                    error_log("[缓存优化][修复] 未找到预览图: 索引={$index}, 文件={$file_name}, 路径={$preview_path}");
                }
            }
        }

        error_log("[缓存优化][修复] 检查完成，找到 {$found_count}/" . count($svg_files) . " 个预览图");

        // 【优化】只有当找到足够多的预览图时才认为是有效缓存
        $total_designs = count($svg_files);

        // 【极速优化】对自定义匹配类目使用更激进的阈值
        $is_custom_matching = get_term_meta($term_id, '_is_custom_matching_category', true);
        if (!empty($is_custom_matching)) {
            // 自定义匹配类目：只要有5个以上预览图就认为是有效缓存，大幅提升首次访问体验
            $cache_threshold = max(5, min(10, $total_designs * 0.3));
            error_log("[缓存优化][极速] 自定义匹配类目 {$term_id} 使用激进阈值: {$cache_threshold}");
        } else {
            // 普通类目：至少6个或25%的预览图
            $cache_threshold = max(6, $total_designs * 0.25);
        }

        if ($found_count >= $cache_threshold) {
            error_log("[缓存优化] 类目 {$term_id} 找到足够的缓存预览图: {$found_count}/{$total_designs} (阈值: {$cache_threshold})");

            // 【优化】按索引排序确保顺序正确
            usort($cached_previews, function($a, $b) {
                return $a['index'] - $b['index'];
            });

            // 缓存结果
            wp_cache_set($cache_key, $cached_previews, 'ss_cached_previews', 600); // 缓存10分钟
            return $cached_previews;
        } else {
            error_log("[缓存优化] 类目 {$term_id} 缓存预览图不足: {$found_count}/{$total_designs} (阈值: {$cache_threshold})，需要重新生成");
            wp_cache_set($cache_key, [], 'ss_cached_previews', 300); // 缓存空结果5分钟
            return [];
        }
    }

    /**
     * 【新增】立即渲染页面框架
     * 确保用户点击类目后立即看到页面结构，而不是等待SVG加载
     * 
     * @param int $term_id 类目ID
     * @return bool 是否成功渲染框架
     */
    public function render_immediate_framework($term_id) {
        $framework_data = $this->get_immediate_framework_data($term_id);
        
        // 设置全局变量供模板使用
        global $ss_immediate_framework_data;
        $ss_immediate_framework_data = $framework_data;
        
        error_log("[立即加载] 类目 {$term_id} 立即框架渲染完成");
        return true;
    }

    /**
     * 【新增】预加载下一批预览图
     * 在用户浏览当前预览图时，后台预加载下一批
     * 
     * @param int $term_id 类目ID
     * @param array $current_visible_indexes 当前可见的索引
     */
    public function preload_next_batch_previews($term_id, $current_visible_indexes = []) {
        if (empty($current_visible_indexes)) {
            return;
        }

        $max_visible_index = max($current_visible_indexes);
        $next_batch_start = $max_visible_index + 1;
        $next_batch_size = 8; // 预加载8个
        
        $svg_files = $this->get_svg_files();
        $term = get_term($term_id);
        $category_name = $this->get_safe_category_name($term->name);
        
        // 获取预设颜色
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (empty($preset_colors)) {
            $preset_colors = $this->extract_colors_from_category_image($term_id);
        }
        
        if (empty($preset_colors)) {
            return;
        }

        // 预加载下一批
        for ($i = $next_batch_start; $i < min($next_batch_start + $next_batch_size, count($svg_files)); $i++) {
            if (isset($svg_files[$i])) {
                // 使用低优先级队列预加载
                $this->queue_preview_generation($category_name, $svg_files[$i], $i, 'low', $term_id);
            }
        }
        
        error_log("[预加载] 类目 {$term_id} 预加载下一批预览图: 索引 {$next_batch_start} 到 " . ($next_batch_start + $next_batch_size - 1));
    }

    /**
     * 启动并发处理 SVG 预览图生成
     */
    public function ss_start_concurrent_processing() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;
        $force_regenerate = isset($_POST['force_regenerate']) ? filter_var($_POST['force_regenerate'], FILTER_VALIDATE_BOOLEAN) : false;

        if ($term_id === 0) {
            wp_send_json_error(['message' => 'Invalid term ID']);
            return;
        }

        // 获取分类数据
        $term = get_term($term_id);
        if (!$term || is_wp_error($term)) {
            wp_send_json_error(['message' => 'Invalid term']);
            return;
        }

        // 检查是否启用并发处理
        if (!get_option('ss_enable_concurrent_processing', true)) {
            wp_send_json_error(['message' => 'Concurrent processing is disabled']);
            return;
        }

        try {
            // 获取 SVG 文件列表
            $svg_files = $this->get_svg_files_for_category($term_id);
            if (empty($svg_files)) {
                wp_send_json_error(['message' => 'No SVG files found']);
                return;
            }

            // 准备类目数据
            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
            if (empty($preset_colors)) {
                $preset_colors = ['#000000']; // 默认黑色
            }

            $category_data = [
                'term_id' => $term_id,
                'name' => $term->name,
                'colors' => $preset_colors,
                'output_dir' => $this->get_category_output_dir($term_id, $term->name)
            ];

            // 获取并发处理器
            $concurrent_processor = SS_Concurrent_Processor::get_instance();

            // 清理之前的进度数据
            $concurrent_processor->clear_progress($term_id);

            // 启动后台处理
            wp_schedule_single_event(time(), 'ss_process_concurrent_batch', [
                $svg_files,
                $category_data,
                $force_regenerate
            ]);

            error_log('[并发处理] 已启动后台处理任务，类目: ' . $term->name . ', 文件数量: ' . count($svg_files));

            wp_send_json_success([
                'message' => 'Concurrent processing started',
                'file_count' => count($svg_files),
                'category_name' => $term->name
            ]);

        } catch (Exception $e) {
            error_log('[并发处理] 启动失败: ' . $e->getMessage());
            wp_send_json_error(['message' => 'Failed to start concurrent processing: ' . $e->getMessage()]);
        }
    }

    /**
     * Server-Sent Events 进度推送
     */
    public function ss_progress_stream() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;

        if ($term_id === 0) {
            wp_die('Invalid category ID');
        }

        // 设置 SSE 头部
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');

        // 防止输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }

        $concurrent_processor = SS_Concurrent_Processor::get_instance();
        $max_duration = 300; // 最大推送时间 5 分钟
        $start_time = time();

        while (time() - $start_time < $max_duration) {
            // 检查连接状态
            if (connection_aborted()) {
                break;
            }

            // 获取进度数据
            $progress = $concurrent_processor->get_progress($term_id);

            if ($progress) {
                // 发送进度数据
                echo "data: " . json_encode($progress) . "\n\n";
                flush();

                // 检查是否完成
                if (isset($progress['percentage']) && $progress['percentage'] >= 100) {
                    break;
                }
            } else {
                // 发送心跳
                echo "data: " . json_encode(['heartbeat' => true, 'timestamp' => time()]) . "\n\n";
                flush();
            }

            sleep(1);
        }

        // 发送结束信号
        echo "data: " . json_encode(['completed' => true, 'timestamp' => time()]) . "\n\n";
        flush();

        exit;
    }

    /**
     * 获取处理进度（AJAX 轮询备选方案）
     */
    public function ss_get_progress() {
        check_ajax_referer('ss_frontend_nonce', 'nonce');

        $term_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        if ($term_id === 0) {
            wp_send_json_error(['message' => 'Invalid category ID']);
            return;
        }

        $concurrent_processor = SS_Concurrent_Processor::get_instance();
        $progress = $concurrent_processor->get_progress($term_id);

        if ($progress) {
            wp_send_json_success($progress);
        } else {
            wp_send_json_success(['percentage' => 0, 'message' => 'No progress data']);
        }
    }

    /**
     * 获取类目的 SVG 文件列表
     */
    private function get_svg_files_for_category($term_id) {
        $svg_file_manager = SS_SVG_File_Manager::get_instance();
        $svg_files = $svg_file_manager->get_svg_files_by_category($term_id);

        // 过滤出存在的文件
        $existing_files = [];
        foreach ($svg_files as $svg_file) {
            if (file_exists($svg_file)) {
                $existing_files[] = $svg_file;
            }
        }

        return $existing_files;
    }

    /**
     * 【修复】获取类目输出目录 - 使用正确的processed_svg路径结构
     */
    private function get_category_output_dir($term_id, $category_name) {
        $upload_dir = wp_upload_dir();
        $safe_category_name = $this->get_safe_category_name($category_name);

        return $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $safe_category_name;
    }

    /**
     * 启动并发预览图生成（专门用于前端页面）
     *
     * @param int $term_id 类目ID
     * @param object $term 类目对象
     * @param bool $generate_all 是否生成所有预览图，默认false
     */
    private function start_concurrent_preview_generation($term_id, $term, $generate_all = false) {
        error_log('[并发预览调试] 函数被调用 - 类目ID: ' . $term_id . ', 类目名: ' . $term->name . ', 全量生成: ' . ($generate_all ? '是' : '否'));

        try {
            // 获取所有 SVG 文件
            $svg_files = $this->get_svg_files();
            if (empty($svg_files)) {
                error_log('[并发预览] 没有找到 SVG 文件');
                return false;
            }

            error_log('[并发预览] 找到 ' . count($svg_files) . ' 个SVG文件，生成模式: ' . ($generate_all ? '全量生成' : '增量生成'));

            // 获取预设颜色
            $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
            if (empty($preset_colors)) {
                $preset_colors = $this->extract_colors_from_category_image($term_id);
            }

            if (empty($preset_colors)) {
                error_log('[并发预览] 类目没有颜色，跳过并发处理');
                return false;
            }

            // 准备类目数据
            $category_data = [
                'term_id' => $term_id,
                'name' => $term->name,
                'colors' => $preset_colors,
                'output_dir' => $this->get_category_output_dir($term_id, $term->name)
            ];

            // 检查是否已经在处理中
            $progress = get_transient('ss_progress_' . $term_id);
            if ($progress && !isset($progress['completed'])) {
                error_log('[并发预览] 已有处理任务在进行中，跳过');
                return false;
            }

            // 【修复】根据生成模式过滤需要处理的文件
            $files_to_process = [];
            $category_name = $this->get_safe_category_name($term->name);
            $upload_dir = wp_upload_dir();

            foreach ($svg_files as $index => $svg_file) {
                $file_name = basename($svg_file);
                $safe_name = pathinfo($file_name, PATHINFO_FILENAME);

                // 检查预览图是否已存在
                $preview_path = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' .
                               $category_name . '/previews/' . $safe_name . '_preview.webp';

                // 【修复】全量生成模式处理所有文件，普通模式只处理没有预览图的文件
                if ($generate_all || !file_exists($preview_path)) {
                    $files_to_process[] = [
                        'file' => $svg_file,
                        'index' => $index
                    ];
                }
            }

            if (empty($files_to_process)) {
                if ($generate_all) {
                    error_log('[并发预览] 全量生成模式：所有预览图已存在，无需处理');
                } else {
                    error_log('[并发预览] 普通模式：所有预览图已存在，无需处理');
                }
                return false;
            }

            error_log('[并发预览] 总SVG文件数: ' . count($svg_files) . ', 需要处理: ' . count($files_to_process) . ' 个文件');
            error_log('[并发预览] 生成模式: ' . ($generate_all ? '全量生成所有文件' : '只生成缺失的文件'));

            // 【优化】根据是否生成全部来调整批次大小
            $batch_size = $generate_all ?
                max(get_option('ss_batch_size', 8) * 2, 16) : // 全量生成时使用更大的批次
                get_option('ss_batch_size', 8); // 普通情况使用默认批次

            error_log('[并发预览] ' . ($generate_all ? '全量生成模式' : '普通生成模式') . '，批次大小: ' . $batch_size);

            // 设置初始进度
            $initial_progress = [
                'current_batch' => 0,
                'total_batches' => ceil(count($files_to_process) / $batch_size),
                'completed_files' => 0,
                'total_files' => count($files_to_process),
                'percentage' => 0,
                'timestamp' => time(),
                'generate_all' => $generate_all // 【新增】标记是否为全量生成
            ];

            set_transient('ss_progress_' . $term_id, $initial_progress, 300);

            // 【修复】使用异步HTTP请求触发处理，避免WordPress任务调度延迟
            $this->trigger_async_processing($files_to_process, $category_data, $batch_size, $generate_all);

            error_log('[并发预览] 已启动异步并发处理任务');
            return true;

        } catch (Exception $e) {
            error_log('[并发预览] 启动失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 【新增】触发异步处理
     *
     * @param array $files_to_process 需要处理的文件列表
     * @param array $category_data 类目数据
     * @param int $batch_size 批次大小
     * @param bool $generate_all 是否全量生成
     */
    private function trigger_async_processing($files_to_process, $category_data, $batch_size = 8, $generate_all = false) {
        // 将数据存储到临时选项中
        $temp_key = 'ss_async_data_' . $category_data['term_id'] . '_' . time();
        set_transient($temp_key, [
            'files_to_process' => $files_to_process,
            'category_data' => $category_data,
            'batch_size' => $batch_size, // 【新增】批次大小
            'generate_all' => $generate_all // 【新增】生成模式
        ], 600); // 【优化】全量生成时延长过期时间到10分钟

        // 使用异步HTTP请求触发处理
        $async_url = admin_url('admin-ajax.php');
        $body = [
            'action' => 'ss_async_process_preview_batch',
            'temp_key' => $temp_key,
            'nonce' => wp_create_nonce('ss_async_process')
        ];

        // 发送异步请求
        wp_remote_post($async_url, [
            'timeout' => 1, // 快速超时，不等待响应
            'blocking' => false, // 非阻塞
            'body' => $body,
            'cookies' => $_COOKIE // 传递当前会话cookies
        ]);

        error_log('[并发预览] 异步请求已发送，临时键: ' . $temp_key);
    }

    /**
     * 【新增】异步处理预览图批次的AJAX处理函数
     */
    public function ss_async_process_preview_batch() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'ss_async_process')) {
            error_log('[异步处理] Nonce验证失败');
            wp_die('Security check failed');
        }

        $temp_key = sanitize_text_field($_POST['temp_key'] ?? '');
        if (empty($temp_key)) {
            error_log('[异步处理] 临时键为空');
            wp_die('Invalid temp key');
        }

        // 获取临时数据
        $temp_data = get_transient($temp_key);
        if (!$temp_data) {
            error_log('[异步处理] 临时数据不存在或已过期: ' . $temp_key);
            wp_die('Temp data not found');
        }

        // 删除临时数据
        delete_transient($temp_key);

        // 执行处理
        error_log('[异步处理] 开始执行并发预览图处理');
        error_log('[异步处理调试] 临时数据包含: files_to_process=' . count($temp_data['files_to_process']) . ', batch_size=' . ($temp_data['batch_size'] ?? '默认') . ', generate_all=' . ($temp_data['generate_all'] ?? '未设置'));

        $this->ss_process_concurrent_preview_batch(
            $temp_data['files_to_process'],
            $temp_data['category_data'],
            $temp_data['batch_size'] ?? null // 【新增】传递批次大小
        );

        error_log('[异步处理] 并发预览图处理完成');
        wp_die('Processing completed');
    }

    /**
     * 后台并发处理批次
     *
     * @param array $svg_files SVG文件列表
     * @param array $category_data 类目数据
     * @param bool $force_regenerate 是否强制重新生成
     * @param int $custom_batch_size 自定义批次大小
     */
    public function ss_process_concurrent_batch($svg_files, $category_data, $force_regenerate = false, $custom_batch_size = null) {
        error_log('[并发处理] 开始后台处理，类目: ' . $category_data['name'] . ', 文件数量: ' . count($svg_files));

        try {
            // 获取并发处理器
            $concurrent_processor = SS_Concurrent_Processor::get_instance();

            // 执行并发处理
            $results = $concurrent_processor->process_svg_batch($svg_files, $category_data, $custom_batch_size);

            // 处理结果
            $success_count = 0;
            $error_count = 0;

            foreach ($results as $result) {
                if (isset($result['success']) && $result['success']) {
                    $success_count++;
                } else {
                    $error_count++;
                    error_log('[并发处理] 处理失败: ' . ($result['file'] ?? 'unknown') . ' - ' . ($result['error'] ?? 'unknown error'));
                }
            }

            error_log('[并发处理] 批次处理完成，成功: ' . $success_count . ', 失败: ' . $error_count);

            // 标记类目已生成预览图
            if ($success_count > 0) {
                $this->mark_category_frontend_preview_generated($category_data['term_id']);
            }

            // 设置最终完成状态
            $final_progress = [
                'current_batch' => 1,
                'total_batches' => 1,
                'completed_files' => $success_count,
                'total_files' => count($svg_files),
                'percentage' => 100,
                'completed' => true,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'timestamp' => time()
            ];

            set_transient('ss_progress_' . $category_data['term_id'], $final_progress, 300);

        } catch (Exception $e) {
            error_log('[并发处理] 后台处理失败: ' . $e->getMessage());

            // 设置错误状态
            $error_progress = [
                'percentage' => 0,
                'completed' => true,
                'error' => true,
                'error_message' => $e->getMessage(),
                'timestamp' => time()
            ];

            set_transient('ss_progress_' . $category_data['term_id'], $error_progress, 300);
        }
    }

    /**
     * 后台并发处理预览图批次（专门用于前端页面）
     *
     * @param array $files_to_process 需要处理的文件列表
     * @param array $category_data 类目数据
     * @param int $custom_batch_size 自定义批次大小
     */
    public function ss_process_concurrent_preview_batch($files_to_process, $category_data, $custom_batch_size = null) {
        error_log('[并发预览] 开始后台处理，类目: ' . $category_data['name'] . ', 文件数量: ' . count($files_to_process));
        error_log('[并发预览调试] 接收到的文件列表: ' . json_encode(array_slice(array_column($files_to_process, 'file'), 0, 5)) . '...(显示前5个)');

        try {
            // 【修复】强制启用并发处理，禁用串行处理以避免路径冲突
            if (!get_option('ss_enable_concurrent_processing', true)) {
                error_log('[并发预览] 并发处理已禁用，但为避免路径冲突，强制启用并发处理');
                // 不再回退到串行处理
            }

            // 获取并发处理器
            $concurrent_processor = SS_Concurrent_Processor::get_instance();

            // 准备 SVG 文件列表
            $svg_files = array_column($files_to_process, 'file');

            error_log('[并发预览] 准备处理的SVG文件数量: ' . count($svg_files));
            error_log('[并发预览] 自定义批次大小: ' . ($custom_batch_size ?? '默认'));

            // 执行并发处理
            $results = $concurrent_processor->process_svg_batch($svg_files, $category_data, $custom_batch_size);

            // 处理结果并并发生成 WebP 预览图
            $success_count = 0;
            $error_count = 0;

            // 收集成功处理的 SVG 文件
            $successful_svgs = [];
            foreach ($results as $result) {
                if (isset($result['success']) && $result['success']) {
                    $successful_svgs[] = $result['file'];
                } else {
                    $error_count++;
                    error_log('[并发预览] SVG 处理失败: ' . ($result['file'] ?? 'unknown') . ' - ' . ($result['error'] ?? 'unknown error'));
                }
            }

            if (!empty($successful_svgs)) {
                // 并发生成 WebP 预览图
                error_log('[并发预览] 开始并发生成 ' . count($successful_svgs) . ' 个 WebP 预览图');
                $webp_results = $this->generate_webp_previews_concurrent($successful_svgs, $category_data);

                foreach ($webp_results as $webp_result) {
                    if ($webp_result['success']) {
                        $success_count++;
                        error_log('[并发预览] WebP 预览图生成成功: ' . basename($webp_result['file']));
                    } else {
                        $error_count++;
                        error_log('[并发预览] WebP 预览图生成失败: ' . basename($webp_result['file']) . ' - ' . ($webp_result['error'] ?? 'unknown'));
                    }
                }
            }

            error_log('[并发预览] 批次处理完成，成功: ' . $success_count . ', 失败: ' . $error_count);

            // 标记类目已生成预览图
            if ($success_count > 0) {
                $this->mark_category_frontend_preview_generated($category_data['term_id']);
            }

            // 设置最终完成状态
            $final_progress = [
                'current_batch' => 1,
                'total_batches' => 1,
                'completed_files' => $success_count,
                'total_files' => count($files_to_process),
                'percentage' => 100,
                'completed' => true,
                'success_count' => $success_count,
                'error_count' => $error_count,
                'timestamp' => time()
            ];

            set_transient('ss_progress_' . $category_data['term_id'], $final_progress, 300);

        } catch (Exception $e) {
            error_log('[并发预览] 后台处理失败: ' . $e->getMessage());
            // 【修复】不再回退到串行处理，避免路径冲突
        }
    }

    /**
     * 生成 WebP 预览图
     */
    private function generate_webp_preview($svg_file, $category_data) {
        try {
            $file_name = basename($svg_file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
            $category_name = $this->get_safe_category_name($category_data['name']);

            // 预览图路径
            $upload_dir = wp_upload_dir();
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
            $preview_path = $preview_dir . $safe_name . '_preview.webp';

            // 确保目录存在
            if (!file_exists($preview_dir)) {
                wp_mkdir_p($preview_dir);
                @chmod($preview_dir, 0755);
            }

            // 检查是否已存在
            if (file_exists($preview_path)) {
                return true; // 已存在，认为成功
            }

            // 使用现有的预览图生成方法
            $result = $this->generate_svg_preview_file(
                $svg_file,
                $category_data['name'],
                $category_data['colors'][0] ?? '#000000',
                true,
                $category_data['term_id']
            );

            return $result !== false;

        } catch (Exception $e) {
            error_log('[并发预览] WebP 生成失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 串行处理预览图（回退方案）
     */
    private function process_preview_sequential($files_to_process, $category_data) {
        error_log('[并发预览] 使用串行处理模式');

        $success_count = 0;
        $total_files = count($files_to_process);

        foreach ($files_to_process as $index => $file_data) {
            try {
                $svg_file = $file_data['file'];
                $result = $this->generate_svg_preview_file(
                    $svg_file,
                    $category_data['name'],
                    $category_data['colors'][0] ?? '#000000',
                    true,
                    $category_data['term_id']
                );

                if ($result) {
                    $success_count++;
                }

                // 更新进度
                $progress = [
                    'current_batch' => 1,
                    'total_batches' => 1,
                    'completed_files' => $index + 1,
                    'total_files' => $total_files,
                    'percentage' => round((($index + 1) / $total_files) * 100, 2),
                    'timestamp' => time()
                ];

                set_transient('ss_progress_' . $category_data['term_id'], $progress, 300);

            } catch (Exception $e) {
                error_log('[并发预览] 串行处理失败: ' . $e->getMessage());
            }
        }

        // 设置最终状态
        $final_progress = [
            'current_batch' => 1,
            'total_batches' => 1,
            'completed_files' => $success_count,
            'total_files' => $total_files,
            'percentage' => 100,
            'completed' => true,
            'success_count' => $success_count,
            'error_count' => $total_files - $success_count,
            'timestamp' => time()
        ];

        set_transient('ss_progress_' . $category_data['term_id'], $final_progress, 300);

        if ($success_count > 0) {
            $this->mark_category_frontend_preview_generated($category_data['term_id']);
        }
    }

    /**
     * 并发生成 WebP 预览图
     */
    private function generate_webp_previews_concurrent($svg_files, $category_data) {
        // 【优化】动态计算最优并发数 - 进一步提升性能
        $cpu_cores = $this->detect_cpu_cores();
        $default_concurrent = min($cpu_cores * 3, 24); // 提升为CPU核心数的3倍，最大24
        $max_concurrent = get_option('ss_max_concurrent_workers', $default_concurrent);

        // 【优化】根据文件数量和CPU核心数动态调整 - 更激进的并发策略
        $file_count = count($svg_files);
        $optimal_concurrent = min($max_concurrent, max(4, ceil($file_count / 2))); // 至少4个，最多按文件数/2计算

        // 【新增】根据系统负载动态调整
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $cpu_usage = ($load[0] / $cpu_cores) * 100;
            if ($cpu_usage < 50) {
                $optimal_concurrent = min($optimal_concurrent * 1.5, $max_concurrent); // 负载低时增加50%并发
            }
        }

        $results = [];
        $active_processes = [];

        error_log('[并发预览] 启动 WebP 并发生成，CPU核心: ' . $cpu_cores . ', 最优并发数: ' . $optimal_concurrent . ' (文件数: ' . $file_count . ')');

        for ($i = 0; $i < count($svg_files); $i++) {
            // 等待进程槽位
            while (count($active_processes) >= $optimal_concurrent) {
                $this->wait_for_webp_process_completion($active_processes, $results);
            }

            // 启动新的 WebP 生成进程
            $process = $this->start_webp_generation_process($svg_files[$i], $category_data, $i);
            if ($process) {
                $active_processes[$i] = $process;
            }
        }

        // 等待所有进程完成
        while (!empty($active_processes)) {
            $this->wait_for_webp_process_completion($active_processes, $results);
        }

        error_log('[并发预览] WebP 并发生成完成，总结果数: ' . count($results));
        return $results;
    }

    /**
     * 启动 WebP 生成进程
     */
    private function start_webp_generation_process($svg_file, $category_data, $index) {
        try {
            $file_name = basename($svg_file);
            $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
            $category_name = $this->get_safe_category_name($category_data['name']);

            // 【修复】使用正确的处理后SVG文件路径，避免重复的processed目录
            $upload_dir = wp_upload_dir();
            $processed_svg_file = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $category_name . '/' . $file_name;

            // 【调试】记录路径信息
            error_log('[并发预览][路径修复] 处理后SVG路径: ' . $processed_svg_file);

            // 预览图路径
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
            $preview_path = $preview_dir . $safe_name . '_preview.webp';

            // 确保目录存在
            if (!file_exists($preview_dir)) {
                wp_mkdir_p($preview_dir);
                @chmod($preview_dir, 0755);
            }

            // 【修复】检查处理后的SVG文件是否存在，并验证文件完整性
            if (!file_exists($processed_svg_file)) {
                error_log('[并发预览] 处理后的 SVG 文件不存在: ' . $processed_svg_file);
                return false;
            }

            // 【新增】检查并修复SVG文件结构问题
            $repair_result = $this->check_and_repair_svg_structure($processed_svg_file);
            if (!$repair_result['success']) {
                error_log('[并发预览] SVG文件修复失败: ' . $repair_result['error']);
                return false;
            }

            if ($repair_result['repaired']) {
                error_log('[并发预览] SVG文件已修复: ' . basename($processed_svg_file) . ' (修复了 ' . $repair_result['unclosed_tags_fixed'] . ' 个未闭合标签)');
            }

            // 【新增】写入debug.log
            $debug_log = WP_PLUGIN_DIR . '/shoe-svg-generator/debug.log';
            $debug_msg = sprintf("[%s] [并发预览] 开始处理文件: %s\n", date('Y-m-d H:i:s'), basename($svg_file));
            file_put_contents($debug_log, $debug_msg, FILE_APPEND | LOCK_EX);

            // 【关键修复】获取SVG尺寸信息并计算正确的目标尺寸，保持比例
            $svg_dimensions = $this->get_svg_dimensions($processed_svg_file);
            $svg_width = $svg_dimensions['width'] > 0 ? $svg_dimensions['width'] : 600;
            $svg_height = $svg_dimensions['height'] > 0 ? $svg_dimensions['height'] : 600;

            // 计算保持比例的目标尺寸
            $preview_size = 600; // 最终画布尺寸
            $scale_by_width = $preview_size / $svg_width;
            $scale_by_height = $preview_size / $svg_height;
            $scale = min($scale_by_width, $scale_by_height); // 选择较小的缩放比例以完整显示

            $target_width = intval($svg_width * $scale);
            $target_height = intval($svg_height * $scale);

            // 【调试】记录尺寸计算信息
            $debug_msg = sprintf("[%s] [并发预览] SVG尺寸计算: 原始(%dx%d) -> 缩放比例(%.2f) -> 目标(%dx%d) -> 最终画布(%dx%d)\n",
                date('Y-m-d H:i:s'), $svg_width, $svg_height, $scale, $target_width, $target_height, $preview_size, $preview_size);
            file_put_contents($debug_log, $debug_msg, FILE_APPEND | LOCK_EX);

            error_log(sprintf('[并发预览][尺寸计算] SVG原始尺寸: %dx%d, 缩放比例: %.2f, 目标尺寸: %dx%d, 最终画布: %dx%d',
                $svg_width, $svg_height, $scale, $target_width, $target_height, $preview_size, $preview_size));

            // 【修复】构建转换命令，使用计算出的目标尺寸而不是固定的600x600
            $temp_png = tempnam(sys_get_temp_dir(), 'ss_webp_') . '.png';

            // 检查 cwebp 是否可用
            $has_cwebp = shell_exec('which cwebp 2>/dev/null');
            $has_inkscape = shell_exec('which inkscape 2>/dev/null');

            if ($has_cwebp) {
                // 【修复】添加inkscape重试机制的cwebp版本，使用计算出的目标尺寸
                $cmd = sprintf(
                    'rsvg-convert -a -w %d -h %d -o %s -b transparent %s 2>&1 && ' .
                    'if [ -f %s ] && [ -s %s ]; then ' .
                        'convert %s -background transparent -gravity center -extent %dx%d %s 2>&1 && ' .
                        'cwebp -q 80 -m 4 -mt %s -o %s 2>&1 && rm %s && rm %s; ' .
                    'else ' .
                        'echo "rsvg-convert failed, trying inkscape..." >&2; ' .
                        ($has_inkscape ?
                            'inkscape --export-type=png --export-filename=%s --export-width=%d --export-height=%d --export-background=transparent --export-background-opacity=0 --export-area-page %s 2>&1 && ' .
                            'if [ -f %s ] && [ -s %s ]; then ' .
                                'convert %s -background transparent -gravity center -extent %dx%d %s 2>&1 && ' .
                                'cwebp -q 80 -m 4 -mt %s -o %s 2>&1 && rm %s && rm %s; ' .
                            'else ' .
                                'echo "inkscape also failed" >&2; exit 1; ' .
                            'fi'
                            : 'echo "inkscape not available" >&2; exit 1'
                        ) . '; ' .
                    'fi',
                    $target_width,
                    $target_height,
                    escapeshellarg($temp_png),
                    escapeshellarg($processed_svg_file),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    $preview_size,
                    $preview_size,
                    escapeshellarg($temp_png . '_canvas.png'),
                    escapeshellarg($temp_png . '_canvas.png'),
                    escapeshellarg($preview_path),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png . '_canvas.png'),
                    escapeshellarg($temp_png),
                    $target_width,
                    $target_height,
                    escapeshellarg($processed_svg_file),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    $preview_size,
                    $preview_size,
                    escapeshellarg($temp_png . '_canvas.png'),
                    escapeshellarg($temp_png . '_canvas.png'),
                    escapeshellarg($preview_path),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png . '_canvas.png')
                );
            } else {
                // 【修复】添加inkscape重试机制的ImageMagick版本，使用计算出的目标尺寸
                $cmd = sprintf(
                    'rsvg-convert -a -w %d -h %d -o %s -b transparent %s 2>&1 && ' .
                    'if [ -f %s ] && [ -s %s ]; then ' .
                        'convert %s -background transparent -gravity center -extent %dx%d -quality 80 -define webp:method=4 %s 2>&1 && rm %s; ' .
                    'else ' .
                        'echo "rsvg-convert failed, trying inkscape..." >&2; ' .
                        ($has_inkscape ?
                            'inkscape --export-type=png --export-filename=%s --export-width=%d --export-height=%d --export-background=transparent --export-background-opacity=0 --export-area-page %s 2>&1 && ' .
                            'if [ -f %s ] && [ -s %s ]; then ' .
                                'convert %s -background transparent -gravity center -extent %dx%d -quality 80 -define webp:method=4 %s 2>&1 && rm %s; ' .
                            'else ' .
                                'echo "inkscape also failed" >&2; exit 1; ' .
                            'fi'
                            : 'echo "inkscape not available" >&2; exit 1'
                        ) . '; ' .
                    'fi',
                    $target_width,
                    $target_height,
                    escapeshellarg($temp_png),
                    escapeshellarg($processed_svg_file),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    $preview_size,
                    $preview_size,
                    escapeshellarg($preview_path),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    $target_width,
                    $target_height,
                    escapeshellarg($processed_svg_file),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    escapeshellarg($temp_png),
                    $preview_size,
                    $preview_size,
                    escapeshellarg($preview_path),
                    escapeshellarg($temp_png)
                );
            }

            // 【调试】记录要执行的命令
            error_log('[并发预览][调试] 执行命令: ' . $cmd);

            // 【新增】记录命令到debug.log
            $debug_msg = sprintf("[%s] [并发预览] 执行转换命令: 目标尺寸(%dx%d) -> 最终画布(%dx%d)\n",
                date('Y-m-d H:i:s'), $target_width, $target_height, $preview_size, $preview_size);
            file_put_contents($debug_log, $debug_msg, FILE_APPEND | LOCK_EX);

            // 启动异步进程
            $descriptors = [
                0 => ['pipe', 'r'],  // stdin
                1 => ['pipe', 'w'],  // stdout
                2 => ['pipe', 'w']   // stderr
            ];

            $process = proc_open($cmd, $descriptors, $pipes);

            if (is_resource($process)) {
                // 关闭 stdin
                fclose($pipes[0]);

                // 设置非阻塞模式
                stream_set_blocking($pipes[1], false);
                stream_set_blocking($pipes[2], false);

                return [
                    'process' => $process,
                    'pipes' => $pipes,
                    'svg_file' => $svg_file,
                    'preview_path' => $preview_path,
                    'index' => $index,
                    'start_time' => microtime(true)
                ];
            }

            return false;

        } catch (Exception $e) {
            error_log('[并发预览] WebP 进程启动失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 等待 WebP 进程完成
     */
    private function wait_for_webp_process_completion(&$active_processes, &$results) {
        foreach ($active_processes as $index => $process_info) {
            $status = proc_get_status($process_info['process']);

            if (!$status['running']) {
                // 进程已完成，读取输出
                $output = stream_get_contents($process_info['pipes'][1]);
                $error = stream_get_contents($process_info['pipes'][2]);

                // 关闭管道和进程
                fclose($process_info['pipes'][1]);
                fclose($process_info['pipes'][2]);
                $exit_code = proc_close($process_info['process']);

                $duration = microtime(true) - $process_info['start_time'];

                // 【修复】改进成功判断逻辑：主要检查文件是否存在且有内容
                $file_exists = file_exists($process_info['preview_path']);
                $file_size = $file_exists ? filesize($process_info['preview_path']) : 0;
                $success = $file_exists && $file_size > 0;

                // 【调试】记录详细信息以便排查问题
                error_log(sprintf('[并发预览][调试] 文件: %s, 退出码: %d, 文件存在: %s, 文件大小: %d字节',
                    basename($process_info['svg_file']),
                    $exit_code,
                    $file_exists ? '是' : '否',
                    $file_size
                ));

                $result = [
                    'file' => $process_info['svg_file'],
                    'preview_path' => $process_info['preview_path'],
                    'success' => $success,
                    'duration' => $duration,
                    'exit_code' => $exit_code,
                    'file_size' => $file_size
                ];

                if (!$success) {
                    // 【修复】更精确的错误信息
                    if (!$file_exists) {
                        $result['error'] = '预览文件未生成';
                    } elseif ($file_size === 0) {
                        $result['error'] = '预览文件为空';
                    } else {
                        $result['error'] = $error ?: 'WebP generation failed';
                    }

                    error_log('[并发预览] WebP 生成失败: ' . basename($process_info['svg_file']) . ' - ' . $result['error']);

                    // 【调试】记录命令输出以便排查
                    if (!empty($output)) {
                        error_log('[并发预览][调试] 命令输出: ' . trim($output));
                    }
                    if (!empty($error)) {
                        error_log('[并发预览][调试] 错误输出: ' . trim($error));
                    }
                } else {
                    // 【调试】成功时也记录一些信息
                    error_log('[并发预览] WebP 生成成功: ' . basename($process_info['svg_file']) . ' (大小: ' . $file_size . '字节)');

                    // 【新增】为WebP预览图添加水印
                    try {
                        $preview_image = new Imagick($process_info['preview_path']);
                        $this->add_watermark_to_svg_image($preview_image);
                        $preview_image->writeImage($process_info['preview_path']);
                        $preview_image->clear();
                        $preview_image->destroy();
                        error_log('[并发预览] 成功为WebP预览图添加水印: ' . basename($process_info['svg_file']));
                    } catch (Exception $e) {
                        error_log('[并发预览] 为WebP预览图添加水印失败: ' . basename($process_info['svg_file']) . ' - ' . $e->getMessage());
                    }
                }

                $results[] = $result;

                // 移除已完成的进程
                unset($active_processes[$index]);
                break; // 只处理一个完成的进程，然后返回
            }
        }

        // 如果没有进程完成，短暂等待
        if (!empty($active_processes)) {
            usleep(50000); // 等待 0.05 秒
        }
    }

    /**
     * 【新增】AJAX处理：获取缓存的预览图
     * 检查指定类目是否有缓存的预览图，如果有则返回
     */
    public function ss_get_cached_previews() {
        // 验证nonce
        if (!check_ajax_referer('ss_frontend_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => '安全验证失败'));
        }

        // 获取类目ID
        $term_id = isset($_POST['term_id']) ? intval($_POST['term_id']) : 0;

        if (!$term_id) {
            wp_send_json_error(array('message' => '无效的类目ID'));
            return;
        }

        $term = get_term($term_id);
        if (!$term || is_wp_error($term)) {
            wp_send_json_error(array('message' => '找不到指定的类目'));
            return;
        }

        $category_name = $this->get_safe_category_name($term->name);

        // 【修复】添加详细的调试日志
        error_log("[缓存AJAX][修复] 开始检查类目 {$term_id} ({$term->name}) 的缓存预览图");

        // 获取缓存的预览图
        $cached_previews = $this->get_cached_category_previews($term_id, $category_name);

        // 【修复】添加更详细的调试信息
        $upload_dir = wp_upload_dir();
        $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';
        error_log("[缓存AJAX][修复] 预览图目录: {$preview_dir}");
        error_log("[缓存AJAX][修复] 目录是否存在: " . (file_exists($preview_dir) ? '是' : '否'));

        if (file_exists($preview_dir)) {
            $files = glob($preview_dir . '*.webp');
            error_log("[缓存AJAX][修复] 目录中的webp文件数量: " . count($files));
            if (count($files) > 0) {
                error_log("[缓存AJAX][修复] 前3个文件: " . implode(', ', array_slice($files, 0, 3)));
            }
        }

        if (!empty($cached_previews)) {
            error_log("[缓存AJAX][修复] 类目 {$term_id} 返回缓存预览图 " . count($cached_previews) . " 个");

            wp_send_json_success(array(
                'cached_previews' => $cached_previews,
                'total_cached' => count($cached_previews),
                'cache_hit' => true,
                'message' => '成功加载缓存预览图'
            ));
        } else {
            error_log("[缓存AJAX][修复] 类目 {$term_id} 未找到足够的缓存预览图");

            wp_send_json_success(array(
                'cached_previews' => [],
                'total_cached' => 0,
                'cache_hit' => false,
                'message' => '未找到缓存预览图'
            ));
        }
    }

    /**
     * 【极速优化】生成合成图片缓存键
     */
    private function get_composite_cache_key($template_id, $svg_file, $category_id) {
        return 'composite_' . md5($template_id . '_' . $svg_file . '_' . $category_id);
    }

    /**
     * 【极速优化】获取缓存的合成图片
     */
    private function get_cached_composite_image($cache_key) {
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/shoe-svg-generator/composite-cache/';
        $cache_file = $cache_dir . $cache_key . '.webp';

        if (file_exists($cache_file) && filesize($cache_file) > 0) {
            // 检查缓存文件的修改时间（24小时内有效）
            $cache_age = time() - filemtime($cache_file);
            if ($cache_age < 86400) { // 24小时
                return $cache_file;
            } else {
                // 缓存过期，删除文件
                @unlink($cache_file);
            }
        }

        return false;
    }

    /**
     * 【极速优化】缓存合成图片
     */
    private function cache_composite_image($cache_key, $image_path) {
        $upload_dir = wp_upload_dir();
        $cache_dir = $upload_dir['basedir'] . '/shoe-svg-generator/composite-cache/';

        if (!file_exists($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }

        $cache_file = $cache_dir . $cache_key . '.webp';

        if (file_exists($image_path)) {
            return copy($image_path, $cache_file);
        }

        return false;
    }

    /**
     * 【极速优化】预检查产品生成所需的所有资源
     */
    private function precheck_product_generation_resources($template_id, $svg_file, $category_id) {
        $start_time = microtime(true);

        // 【修复】提取文件名，确保后续所有使用都基于文件名而不是完整路径
        $svg_filename = basename($svg_file);
        error_log("[产品生成][预检查][修复] 原始SVG参数: {$svg_file}, 提取的文件名: {$svg_filename}");

        // 检查模板产品
        $template_cache_key = "template_product_{$template_id}";
        $template_product = wp_cache_get($template_cache_key, 'ss_templates');

        if ($template_product === false) {
            $template_product = wc_get_product($template_id);
            if ($template_product) {
                wp_cache_set($template_cache_key, $template_product, 'ss_templates', 600);
            }
        }

        if (!$template_product) {
            return ['success' => false, 'message' => '无法找到模板产品'];
        }

        // 检查类目信息
        $category_term = get_term($category_id);
        if (!$category_term || is_wp_error($category_term)) {
            return ['success' => false, 'message' => '无效的类目ID'];
        }

        $category_name = $category_term->name;
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);

        // 批量获取元数据
        $template_meta = get_post_meta($template_id);
        $category_meta = get_term_meta($category_id);

        // 验证模板配置
        $template_image_id = isset($template_meta['_ss_template_image_id'][0]) ? $template_meta['_ss_template_image_id'][0] : '';
        $svg_area = isset($template_meta['_ss_svg_area'][0]) ? maybe_unserialize($template_meta['_ss_svg_area'][0]) : null;
        $shoe_area = isset($template_meta['_ss_shoe_area'][0]) ? maybe_unserialize($template_meta['_ss_shoe_area'][0]) : null;

        if (empty($template_image_id) || !is_array($svg_area) || !is_array($shoe_area)) {
            return ['success' => false, 'message' => '模板产品配置不完整'];
        }

        // 验证类目配置（自定义匹配类目不强制要求类目图片）
        $category_image_id = isset($category_meta['thumbnail_id'][0]) ? $category_meta['thumbnail_id'][0] : '';

        if (empty($category_image_id) && !$is_custom_matching) {
            return ['success' => false, 'message' => '类目配置不完整'];
        }

        // 验证文件存在性
        $upload_dir = wp_upload_dir();

        // 【修复】预览图路径 - 确保与实际目录结构一致
        if ($is_custom_matching) {
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $category_term->slug . '/' .
                           basename($svg_filename, '.svg') . '_preview.webp';
            error_log("[产品生成][预检查][修复] 自定义匹配类目预览文件路径: {$preview_file}");
        } else {
            // 【修复】普通类目使用与processed_svg相同的目录结构
            $safe_category_name = $this->get_safe_category_name($category_name);
            $preview_file = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/previews/' .
                           basename($svg_filename, '.svg') . '_preview.webp';
            error_log("[产品生成][预检查][修复] 普通类目预览文件路径: {$preview_file}");

            // 【修复】同时检查processed_svg目录中的SVG文件是否存在
            $processed_svg_file = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $safe_category_name . '/' . $svg_filename;
            error_log("[产品生成][预检查][修复] 检查processed_svg文件: {$processed_svg_file} - " . (file_exists($processed_svg_file) ? '存在' : '不存在'));
        }

        $template_image_path = get_attached_file($template_image_id);
        $category_image_path = $is_custom_matching ? '' : get_attached_file($category_image_id);

        // 【修复】不再检查原始SVG文件，直接使用预览图进行产品生成
        // 由于我们现在直接使用SVG预览图合成产品，不再需要依赖原始SVG文件
        error_log("[产品生成][预检查][优化] 跳过SVG文件检查，直接使用预览图: {$svg_filename}");

        // 检查必要文件
        $required_files = [
            'preview' => $preview_file,
            'template' => $template_image_path
        ];

        if (!$is_custom_matching) {
            $required_files['category'] = $category_image_path;
        }

        foreach ($required_files as $type => $file_path) {
            if (empty($file_path) || !file_exists($file_path)) {
                return ['success' => false, 'message' => "无法找到{$type}文件"];
            }
        }

        $precheck_time = round((microtime(true) - $start_time) * 1000, 2);
        error_log("[产品生成][预检查] 资源检查完成，耗时: {$precheck_time}ms");

        return [
            'success' => true,
            'template_product' => $template_product,
            'category_name' => $category_name,
            'category_term' => $category_term,
            'template_meta' => $template_meta,
            'category_meta' => $category_meta,
            'preview_file' => $preview_file,
            'template_image_path' => $template_image_path,
            'category_image_path' => $category_image_path,
            'is_custom_matching' => $is_custom_matching,
            'precheck_time_ms' => $precheck_time
        ];
    }

    /**
     * 【极速优化】使用缓存的合成图片创建产品
     */
    private function create_product_from_cached_composite($template_id, $cached_composite, $svg_file, $category_id, $start_time) {
        // 获取必要的数据 - 【修复】确保使用文件名
        $svg_filename = basename($svg_file);
        $category_term = get_term($category_id);
        $category_name = $category_term->name;
        $svg_basename = pathinfo($svg_filename, PATHINFO_FILENAME);
        $safe_product_name = sanitize_title($svg_basename);
        $safe_category_name = $this->get_safe_category_name($category_name);

        // 直接创建产品，跳过图片合成步骤 - 【修复】使用文件名
        $product_result = $this->create_product_from_template(
            $template_id,
            $cached_composite,
            $svg_filename,
            $safe_category_name,
            $safe_product_name,
            $category_id,
            $category_name
        );

        if ($product_result) {
            // 记录SVG产品关系 - 【修复】使用文件名
            $this->record_svg_product_relation($template_id, $svg_filename, $category_id, $product_result['product_id']);
        }

        return $product_result;
    }

    /**
     * 【新增功能】AJAX处理：手动为类目分配所有模板产品
     */
    public function ss_auto_assign_templates() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ss_auto_assign_templates')) {
            wp_send_json_error(['message' => '安全验证失败']);
            return;
        }

        // 获取类目ID
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        if (!$category_id) {
            wp_send_json_error(['message' => '缺少类目ID']);
            return;
        }

        // 验证类目是否存在
        $term = get_term($category_id);
        if (!$term || is_wp_error($term)) {
            wp_send_json_error(['message' => '无效的类目ID']);
            return;
        }

        // 执行自动分配
        $result = $this->auto_assign_all_templates_to_category($category_id);

        if ($result['success']) {
            wp_send_json_success([
                'message' => "成功为类目 '{$term->name}' 分配了 {$result['count']} 个模板产品",
                'data' => $result
            ]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * 【修复】确保产品图片gallery显示正确的大图
     * 修复data-large_image指向缩略图而不是原始大图的问题
     */
    public function fix_product_gallery_large_image($html, $attachment_id) {
        // 检查是否是我们生成的产品图片
        $attachment_meta = wp_get_attachment_metadata($attachment_id);
        $attachment_url = wp_get_attachment_url($attachment_id);

        // 检查是否是shoe-svg-generator生成的图片
        if (strpos($attachment_url, '/shoe-svg-generator/') === false) {
            return $html; // 不是我们的图片，不处理
        }

        error_log("[Gallery大图修复] 处理附件ID: {$attachment_id}, URL: {$attachment_url}");

        // 获取原始图片URL（full size）
        $full_image_url = wp_get_attachment_image_url($attachment_id, 'full');
        if (!$full_image_url) {
            $full_image_url = $attachment_url; // 回退到附件URL
        }

        // 获取原始图片尺寸
        $full_image_size = wp_get_attachment_image_src($attachment_id, 'full');
        $full_width = $full_image_size[1] ?? 1000;
        $full_height = $full_image_size[2] ?? 1000;

        error_log("[Gallery大图修复] 原始图片URL: {$full_image_url}, 尺寸: {$full_width}x{$full_height}");

        // 使用正则表达式替换data-large_image属性
        $html = preg_replace(
            '/data-large_image="[^"]*"/',
            'data-large_image="' . esc_attr($full_image_url) . '"',
            $html
        );

        // 同时修复data-large_image_width和data-large_image_height
        $html = preg_replace(
            '/data-large_image_width="[^"]*"/',
            'data-large_image_width="' . esc_attr($full_width) . '"',
            $html
        );

        $html = preg_replace(
            '/data-large_image_height="[^"]*"/',
            'data-large_image_height="' . esc_attr($full_height) . '"',
            $html
        );

        error_log("[Gallery大图修复] 已修复附件 {$attachment_id} 的data-large_image属性");

        return $html;
    }

    /**
     * 【修复】修复附件图片属性，确保data-large_image指向正确的大图
     */
    public function fix_attachment_image_attributes($attr, $attachment, $size) {
        // 只处理我们生成的图片
        $attachment_url = wp_get_attachment_url($attachment->ID);
        if (strpos($attachment_url, '/shoe-svg-generator/') === false) {
            return $attr;
        }

        // 获取原始图片URL和尺寸
        $full_image_url = wp_get_attachment_image_url($attachment->ID, 'full');
        $full_image_size = wp_get_attachment_image_src($attachment->ID, 'full');

        if ($full_image_url && $full_image_size) {
            $attr['data-large_image'] = $full_image_url;
            $attr['data-large_image_width'] = $full_image_size[1];
            $attr['data-large_image_height'] = $full_image_size[2];

            error_log("[附件属性修复] 修复附件 {$attachment->ID} 的data-large_image: {$full_image_url}");
        }

        return $attr;
    }

    /**
     * 优化PHP设置以处理大型SVG文件
     * 解决"Premature end of data in tag g"错误
     */
    private function optimize_php_settings_for_svg() {
        // 记录原始设置
        $original_memory = ini_get('memory_limit');
        $original_time = ini_get('max_execution_time');
        $original_input_time = ini_get('max_input_time');

        if (function_exists('ini_set')) {
            // 增加内存限制到2GB（处理大型SVG文件）
            @ini_set('memory_limit', '2G');

            // 增加执行时间到600秒
            @ini_set('max_execution_time', 600);

            // 增加输入时间到600秒
            @ini_set('max_input_time', 600);

            error_log('[PHP优化] SVG处理设置已优化:');
            error_log('[PHP优化] 内存限制: ' . $original_memory . ' → ' . ini_get('memory_limit'));
            error_log('[PHP优化] 执行时间: ' . $original_time . '秒 → ' . ini_get('max_execution_time') . '秒');
            error_log('[PHP优化] 输入时间: ' . $original_input_time . '秒 → ' . ini_get('max_input_time') . '秒');
        } else {
            error_log('[PHP优化] 警告：ini_set函数不可用，无法动态优化PHP设置');
        }

        // 设置libxml选项以处理大型XML文档
        if (extension_loaded('libxml')) {
            libxml_use_internal_errors(true);
            error_log('[PHP优化] libxml错误处理已启用，支持大型XML文档解析');
        }
    }

    /**
     * 使用LIBXML_PARSEHUGE选项解析SVG文件
     * 解决"Excessive depth in document: 256"错误
     */
    private function parse_svg_with_huge_option($svg_content) {
        if (!extension_loaded('libxml')) {
            error_log('[SVG解析] libxml扩展未加载');
            return false;
        }

        $old_setting = libxml_use_internal_errors(true);
        libxml_clear_errors();

        // 使用LIBXML_PARSEHUGE选项解析大型SVG文档
        $doc = simplexml_load_string($svg_content, 'SimpleXMLElement', LIBXML_PARSEHUGE);
        $errors = libxml_get_errors();

        libxml_use_internal_errors($old_setting);

        if ($doc !== false && empty($errors)) {
            error_log('[SVG解析] 使用LIBXML_PARSEHUGE选项成功解析SVG');
            return true;
        } else {
            if (!empty($errors)) {
                error_log('[SVG解析] XML解析错误: ' . $errors[0]->message);
            }
            return false;
        }
    }

    /**
     * 使用XML解析器验证SVG文件的有效性
     * 这是最严格的验证方法，能检测到rsvg-convert会遇到的问题
     */
    private function validate_svg_with_xml_parser($svg_content) {
        if (!extension_loaded('libxml')) {
            error_log('[SVG验证] libxml扩展未加载，跳过XML验证');
            return true; // 如果没有libxml，假设文件有效
        }

        $old_setting = libxml_use_internal_errors(true);
        libxml_clear_errors();

        // 尝试多种解析方法
        $methods = [
            'simplexml_huge' => function($content) {
                return simplexml_load_string($content, 'SimpleXMLElement', LIBXML_PARSEHUGE);
            },
            'simplexml_normal' => function($content) {
                return simplexml_load_string($content, 'SimpleXMLElement');
            },
            'domdocument' => function($content) {
                $dom = new DOMDocument();
                $dom->loadXML($content, LIBXML_PARSEHUGE);
                return $dom;
            }
        ];

        $is_valid = false;
        $last_error = '';

        foreach ($methods as $method_name => $method) {
            libxml_clear_errors();

            try {
                $result = $method($svg_content);
                $errors = libxml_get_errors();

                if ($result !== false && empty($errors)) {
                    error_log('[SVG验证] 使用 ' . $method_name . ' 方法验证成功');
                    $is_valid = true;
                    break;
                } else {
                    if (!empty($errors)) {
                        $last_error = $errors[0]->message;
                        error_log('[SVG验证] ' . $method_name . ' 方法失败: ' . trim($last_error));
                    }
                }
            } catch (Exception $e) {
                error_log('[SVG验证] ' . $method_name . ' 方法异常: ' . $e->getMessage());
                $last_error = $e->getMessage();
            }
        }

        libxml_use_internal_errors($old_setting);

        if (!$is_valid) {
            error_log('[SVG验证] 所有XML解析方法都失败，最后错误: ' . trim($last_error));
        }

        return $is_valid;
    }

    /**
     * 检查并修复SVG文件的XML结构问题
     * 专门处理被截断的SVG文件
     */
    private function check_and_repair_svg_structure($svg_path) {
        if (!file_exists($svg_path)) {
            return ['success' => false, 'error' => '文件不存在'];
        }

        $content = file_get_contents($svg_path);
        if (!$content) {
            return ['success' => false, 'error' => '无法读取文件内容'];
        }

        $file_size = strlen($content);
        $file_size_mb = round($file_size / 1024 / 1024, 2);

        error_log('[SVG检查] 检查文件: ' . basename($svg_path) . ' (大小: ' . $file_size_mb . 'MB)');

        // 【关键修复】首先使用XML解析器验证文件
        $xml_valid = $this->validate_svg_with_xml_parser($content);
        if ($xml_valid) {
            error_log('[SVG检查] XML解析器验证通过，文件结构正常');
            return ['success' => true, 'message' => '文件结构正常', 'repaired' => false];
        }

        // XML解析失败，开始检查具体问题
        error_log('[SVG检查] XML解析器验证失败，开始检查具体问题');
        $is_truncated = false;
        $issues = [];

        // 1. 检查文件大小是否接近10MB限制
        if ($file_size > 9500000 && $file_size < 10500000) {
            $is_truncated = true;
            $issues[] = 'file_size_near_10mb_limit';
            error_log('[SVG检查] 检测到文件大小接近10MB限制，可能被截断');
        }

        // 2. 检查是否有正确的SVG结束标签
        if (!preg_match('/<\/svg\s*>\s*$/', $content)) {
            $is_truncated = true;
            $issues[] = 'missing_svg_end_tag';
            error_log('[SVG检查] 检测到缺失SVG结束标签');
        }

        // 3. 检查未闭合的<g>标签
        $g_open_count = preg_match_all('/<g[^>]*>/', $content);
        $g_close_count = preg_match_all('/<\/g>/', $content);
        $unclosed_g_count = $g_open_count - $g_close_count;

        if ($unclosed_g_count > 0) {
            $is_truncated = true;
            $issues[] = 'unclosed_g_tags';
            error_log('[SVG检查] 检测到 ' . $unclosed_g_count . ' 个未闭合的<g>标签');
        }

        // 4. 检查文件末尾是否有不完整的标签
        $last_100_chars = substr($content, -100);
        if (preg_match('/<[^>]*$/', $last_100_chars)) {
            $is_truncated = true;
            $issues[] = 'incomplete_tag_at_end';
            error_log('[SVG检查] 检测到文件末尾有不完整的标签');
        }

        // 5. 【新增】如果XML解析失败但没有检测到明显问题，强制标记为需要修复
        if (!$is_truncated) {
            $is_truncated = true;
            $issues[] = 'xml_parse_error';
            error_log('[SVG检查] XML解析失败，但未检测到明显问题，强制修复');
            // 重新计算未闭合标签数，可能有隐藏的问题
            $unclosed_g_count = max($unclosed_g_count, 1);
        }

        if ($is_truncated) {
            error_log('[SVG检查] 文件需要修复，问题: ' . implode(', ', $issues));
            return $this->repair_truncated_svg($svg_path, $content, $unclosed_g_count);
        } else {
            error_log('[SVG检查] 文件结构正常');
            return ['success' => true, 'message' => '文件结构正常', 'repaired' => false];
        }
    }

    /**
     * 修复被截断的SVG文件
     */
    private function repair_truncated_svg($svg_path, $content, $unclosed_g_count) {
        $original_size = strlen($content);

        error_log('[SVG修复] 开始修复被截断的SVG文件: ' . basename($svg_path));

        // 步骤1: 移除文件末尾的不完整内容
        $last_complete_tag_pos = strrpos($content, '>');
        if ($last_complete_tag_pos !== false) {
            $after_last_tag = substr($content, $last_complete_tag_pos + 1);
            if (strpos($after_last_tag, '<') !== false && strpos($after_last_tag, '>') === false) {
                $content = substr($content, 0, $last_complete_tag_pos + 1);
                error_log('[SVG修复] 移除了不完整的标签内容');
            }
        }

        // 步骤2: 闭合未闭合的<g>标签
        if ($unclosed_g_count > 0) {
            $close_tags = str_repeat('</g>', $unclosed_g_count);

            if (preg_match('/<\/svg\s*>\s*$/', $content)) {
                // 如果已经有</svg>，在它之前插入
                $content = preg_replace('/(<\/svg\s*>\s*)$/', $close_tags . '$1', $content);
            } else {
                // 如果没有</svg>，添加闭合标签和SVG结束标签
                $content = rtrim($content) . $close_tags;
            }

            error_log('[SVG修复] 闭合了 ' . $unclosed_g_count . ' 个未闭合的<g>标签');
        }

        // 步骤3: 确保有正确的SVG结束标签
        if (!preg_match('/<\/svg\s*>\s*$/', $content)) {
            $content = rtrim($content) . "\n</svg>";
            error_log('[SVG修复] 添加了SVG结束标签');
        }

        // 步骤4: 验证修复后的XML
        if (!$this->validate_svg_with_xml_parser($content)) {
            error_log('[SVG修复] 修复后的XML仍然无效，尝试更激进的修复');

            // 更激进的修复：移除更多可能有问题的内容
            $content = $this->aggressive_svg_repair($content);

            // 再次验证
            if (!$this->validate_svg_with_xml_parser($content)) {
                error_log('[SVG修复] 激进修复后的XML仍然无效');
                return ['success' => false, 'error' => '修复后的XML仍然无效'];
            } else {
                error_log('[SVG修复] 激进修复成功');
            }
        }

        // 步骤5: 保存修复后的文件（覆盖原文件）
        $success = file_put_contents($svg_path, $content);
        if (!$success) {
            error_log('[SVG修复] 无法保存修复后的文件');
            return ['success' => false, 'error' => '无法保存修复后的文件'];
        }

        $repaired_size = strlen($content);
        $size_reduction = $original_size - $repaired_size;

        error_log('[SVG修复] 修复完成: ' . basename($svg_path));
        error_log('[SVG修复] 原始大小: ' . number_format($original_size) . ' 字节');
        error_log('[SVG修复] 修复后大小: ' . number_format($repaired_size) . ' 字节');
        error_log('[SVG修复] 减少大小: ' . number_format($size_reduction) . ' 字节');

        return [
            'success' => true,
            'message' => 'SVG文件已修复',
            'repaired' => true,
            'original_size' => $original_size,
            'repaired_size' => $repaired_size,
            'size_reduction' => $size_reduction,
            'unclosed_tags_fixed' => $unclosed_g_count
        ];
    }

    /**
     * 激进的SVG修复方法
     * 当常规修复失败时使用
     */
    private function aggressive_svg_repair($content) {
        error_log('[SVG激进修复] 开始激进修复');

        // 1. 找到最后一个完整的标签
        $last_complete_pos = 0;
        $bracket_count = 0;
        $in_tag = false;

        for ($i = 0; $i < strlen($content); $i++) {
            $char = $content[$i];

            if ($char === '<') {
                $in_tag = true;
                $bracket_count++;
            } elseif ($char === '>') {
                if ($in_tag) {
                    $in_tag = false;
                    $last_complete_pos = $i;
                }
            }
        }

        // 2. 截断到最后一个完整标签
        if ($last_complete_pos > 0 && $last_complete_pos < strlen($content) - 1) {
            $content = substr($content, 0, $last_complete_pos + 1);
            error_log('[SVG激进修复] 截断到最后一个完整标签位置: ' . $last_complete_pos);
        }

        // 3. 重新计算并闭合所有未闭合的标签
        $open_tags = [];
        $pos = 0;

        // 解析所有标签
        while (($start = strpos($content, '<', $pos)) !== false) {
            $end = strpos($content, '>', $start);
            if ($end === false) break;

            $tag = substr($content, $start, $end - $start + 1);
            $pos = $end + 1;

            // 检查是否是闭合标签
            if (preg_match('/<\/(\w+)>/', $tag, $matches)) {
                // 闭合标签，从栈中移除
                $tag_name = $matches[1];
                for ($i = count($open_tags) - 1; $i >= 0; $i--) {
                    if ($open_tags[$i] === $tag_name) {
                        array_splice($open_tags, $i, 1);
                        break;
                    }
                }
            } elseif (preg_match('/<(\w+)(?:\s[^>]*)?\/>/', $tag)) {
                // 自闭合标签，不需要处理
                continue;
            } elseif (preg_match('/<(\w+)(?:\s[^>]*)?[^\/]>/', $tag, $matches)) {
                // 开放标签
                $tag_name = $matches[1];
                $open_tags[] = $tag_name;
            }
        }

        // 4. 闭合所有未闭合的标签（按相反顺序）
        $close_tags = '';
        for ($i = count($open_tags) - 1; $i >= 0; $i--) {
            $close_tags .= '</' . $open_tags[$i] . '>';
        }

        if (!empty($close_tags)) {
            error_log('[SVG激进修复] 闭合 ' . count($open_tags) . ' 个未闭合标签: ' . implode(', ', $open_tags));

            // 如果已经有</svg>，在它之前插入
            if (preg_match('/<\/svg\s*>\s*$/', $content)) {
                $content = preg_replace('/(<\/svg\s*>\s*)$/', $close_tags . '$1', $content);
            } else {
                $content = rtrim($content) . $close_tags . "\n</svg>";
            }
        } elseif (!preg_match('/<\/svg\s*>\s*$/', $content)) {
            // 确保有SVG结束标签
            $content = rtrim($content) . "\n</svg>";
            error_log('[SVG激进修复] 添加SVG结束标签');
        }

        error_log('[SVG激进修复] 激进修复完成');
        return $content;
    }

    /**
     * 【新增】为SVG图片添加水印
     *
     * @param Imagick $image 要添加水印的图片对象
     * @return bool 是否成功添加水印
     */
    private function add_watermark_to_svg_image($image) {
        try {
            // 水印文件路径
            $watermark_path = WP_CONTENT_DIR . '/uploads/shoe-svg-generator/watermark/watermark.png';

            // 检查水印文件是否存在
            if (!file_exists($watermark_path)) {
                error_log('[SVG水印] 水印文件不存在: ' . $watermark_path);
                return false;
            }

            // 加载水印图片
            $watermark = new Imagick($watermark_path);
            $watermark->setImageBackgroundColor(new ImagickPixel('transparent'));
            $watermark->setImageAlphaChannel(Imagick::ALPHACHANNEL_ACTIVATE);

            // 获取原图和水印尺寸
            $image_width = $image->getImageWidth();
            $image_height = $image->getImageHeight();
            $watermark_width = $watermark->getImageWidth();
            $watermark_height = $watermark->getImageHeight();

            error_log('[SVG水印] 原图尺寸: ' . $image_width . 'x' . $image_height);
            error_log('[SVG水印] 水印尺寸: ' . $watermark_width . 'x' . $watermark_height);

            // 如果原图是600x600，直接按原尺寸贴水印
            if ($image_width == 600 && $image_height == 600 && $watermark_width == 600 && $watermark_height == 600) {
                // 直接覆盖整个图片
                $pos_x = 0;
                $pos_y = 0;
                error_log('[SVG水印] 600x600图片，直接按原尺寸贴水印');
            } else {
                // 其他尺寸的图片，将水印缩放到合适大小并居中放置
                $scale_factor = min($image_width / $watermark_width, $image_height / $watermark_height);
                $new_watermark_width = intval($watermark_width * $scale_factor);
                $new_watermark_height = intval($watermark_height * $scale_factor);

                // 缩放水印
                $watermark->resizeImage($new_watermark_width, $new_watermark_height, Imagick::FILTER_LANCZOS, 1);

                // 计算居中位置
                $pos_x = intval(($image_width - $new_watermark_width) / 2);
                $pos_y = intval(($image_height - $new_watermark_height) / 2);

                error_log('[SVG水印] 缩放水印到: ' . $new_watermark_width . 'x' . $new_watermark_height . ', 位置: ' . $pos_x . ',' . $pos_y);
            }

            // 合成水印到原图
            $image->compositeImage(
                $watermark,
                Imagick::COMPOSITE_OVER,
                $pos_x,
                $pos_y
            );

            // 清理水印对象
            $watermark->clear();
            $watermark->destroy();

            error_log('[SVG水印] 成功添加水印到SVG图片');
            return true;

        } catch (Exception $e) {
            error_log('[SVG水印] 添加水印失败: ' . $e->getMessage());
            return false;
        }
    }
}

// 初始化前端管理器
$ss_frontend_manager = SS_Frontend_Manager::get_instance();