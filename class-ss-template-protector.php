<?php
/**
 * 模板产品保护功能类
 * 
 * 提供模板产品的保护机制，防止误删或修改
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * 模板产品保护器
 */
class SS_Template_Protector {

    /**
     * 单例实例
     */
    private static $instance = null;

    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 产品编辑页面钩子
        add_action('add_meta_boxes', array($this, 'add_protection_meta_box'));
        add_action('save_post', array($this, 'save_protection_settings'));

        // 保护钩子
        add_action('wp_trash_post', array($this, 'prevent_template_deletion'));
        add_action('before_delete_post', array($this, 'prevent_template_deletion'));
        add_action('save_post', array($this, 'prevent_template_modification'), 5);
        add_action('delete_attachment', array($this, 'prevent_template_image_deletion'));
        
        // WooCommerce 钩子
        add_action('woocommerce_before_product_object_save', array($this, 'prevent_wc_product_modification'));

        // AJAX 钩子
        add_action('wp_ajax_ss_toggle_template_protection', array($this, 'ajax_toggle_protection'));

        // 管理界面钩子
        add_filter('post_row_actions', array($this, 'modify_row_actions'), 10, 2);
        add_action('admin_notices', array($this, 'show_protection_notices'));

        // 样式和脚本
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * 添加保护设置元框
     */
    public function add_protection_meta_box() {
        $screen = get_current_screen();
        
        // 只在产品编辑页面显示
        if ($screen && $screen->post_type === 'product') {
            global $post;
            
            // 检查是否是模板产品
            if ($post && get_post_meta($post->ID, '_ss_is_template', true) === '1') {
                add_meta_box(
                    'ss_template_protection',
                    '🛡️ 模板保护',
                    array($this, 'render_protection_meta_box'),
                    'product',
                    'side',
                    'high'
                );
            }
        }
    }

    /**
     * 渲染保护设置元框
     */
    public function render_protection_meta_box($post) {
        $is_protected = $this->is_template_protected($post->ID);
        $protection_level = get_post_meta($post->ID, '_ss_template_protection_level', true);
        if (empty($protection_level)) {
            $protection_level = 'full';
        }

        // 添加nonce字段
        wp_nonce_field('ss_template_protection_nonce', 'ss_template_protection_nonce');

        ?>
        <div class="ss-protection-settings">
            <p>
                <label>
                    <input type="checkbox" 
                           name="ss_template_protected" 
                           value="1" 
                           <?php checked($is_protected); ?>
                           id="ss_template_protected_checkbox">
                    <strong>启用模板保护</strong>
                </label>
            </p>

            <div id="protection_level_settings" style="<?php echo $is_protected ? '' : 'display:none;'; ?>">
                <p><strong>保护级别：</strong></p>
                <p>
                    <label>
                        <input type="radio" 
                               name="ss_template_protection_level" 
                               value="full" 
                               <?php checked($protection_level, 'full'); ?>>
                        完全保护 <small>(不允许任何修改和删除)</small>
                    </label>
                </p>
                <p>
                    <label>
                        <input type="radio" 
                               name="ss_template_protection_level" 
                               value="partial" 
                               <?php checked($protection_level, 'partial'); ?>>
                        部分保护 <small>(允许库存等非关键字段修改)</small>
                    </label>
                </p>
            </div>

            <?php if ($is_protected): ?>
                <div class="ss-protection-status protected">
                    <p><span class="dashicons dashicons-shield-alt"></span> <strong>此模板已受保护</strong></p>
                    <p><small>受保护的内容包括：</small></p>
                    <ul style="font-size: 11px; margin-left: 15px;">
                        <li>产品基本信息（标题、描述、价格）</li>
                        <li>产品图片（主图、画廊图片）</li>
                        <li>产品变体和属性</li>
                        <li>模板相关设置</li>
                        <li>关联的图片文件</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="ss-protection-status unprotected">
                    <p><span class="dashicons dashicons-warning"></span> <strong>此模板未受保护</strong></p>
                    <p><small>建议启用保护以防止意外修改或删除</small></p>
                </div>
            <?php endif; ?>

            <p style="margin-top: 15px;">
                <button type="button" 
                        class="button button-secondary" 
                        id="ss_toggle_protection_btn"
                        data-product-id="<?php echo $post->ID; ?>"
                        data-protected="<?php echo $is_protected ? '1' : '0'; ?>">
                    <?php echo $is_protected ? '解除保护' : '立即保护'; ?>
                </button>
            </p>
        </div>

        <style>
        .ss-protection-settings .ss-protection-status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .ss-protection-status.protected {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .ss-protection-status.unprotected {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .ss-protection-settings .dashicons {
            vertical-align: middle;
        }
        </style>
        <?php
    }

    /**
     * 保存保护设置
     */
    public function save_protection_settings($post_id) {
        // 检查nonce
        if (!isset($_POST['ss_template_protection_nonce']) || 
            !wp_verify_nonce($_POST['ss_template_protection_nonce'], 'ss_template_protection_nonce')) {
            return;
        }

        // 检查权限
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // 检查是否是模板产品
        if (get_post_meta($post_id, '_ss_is_template', true) !== '1') {
            return;
        }

        // 保存保护状态
        $protected = isset($_POST['ss_template_protected']) ? '1' : '0';
        update_post_meta($post_id, '_ss_template_protected', $protected);

        // 保存保护级别
        $protection_level = isset($_POST['ss_template_protection_level']) ? 
                           sanitize_text_field($_POST['ss_template_protection_level']) : 'full';
        update_post_meta($post_id, '_ss_template_protection_level', $protection_level);

        // 记录日志
        $product_title = get_the_title($post_id);
        if ($protected === '1') {
            error_log("[模板保护] 模板产品 '{$product_title}' (ID: {$post_id}) 已启用保护，级别: {$protection_level}");
        } else {
            error_log("[模板保护] 模板产品 '{$product_title}' (ID: {$post_id}) 已解除保护");
        }
    }

    /**
     * 检查模板是否受保护
     */
    public function is_template_protected($product_id) {
        // 检查是否是模板产品
        if (get_post_meta($product_id, '_ss_is_template', true) !== '1') {
            return false;
        }

        // 检查保护状态
        return get_post_meta($product_id, '_ss_template_protected', true) === '1';
    }

    /**
     * 获取保护级别
     */
    public function get_protection_level($product_id) {
        if (!$this->is_template_protected($product_id)) {
            return false;
        }

        $level = get_post_meta($product_id, '_ss_template_protection_level', true);
        return empty($level) ? 'full' : $level;
    }

    /**
     * 防止删除受保护的模板产品
     */
    public function prevent_template_deletion($post_id) {
        if ($this->is_template_protected($post_id)) {
            $product_title = get_the_title($post_id);
            
            // 记录尝试删除的日志
            error_log("[模板保护] 阻止删除受保护的模板产品: '{$product_title}' (ID: {$post_id})");
            
            // 显示错误信息并阻止删除
            wp_die(
                '无法删除受保护的模板产品 "' . $product_title . '"。请先解除保护后再删除。',
                '模板保护',
                array('back_link' => true)
            );
        }
    }

    /**
     * 防止修改受保护的模板产品
     */
    public function prevent_template_modification($post_id) {
        // 跳过自动保存和修订
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }

        // 检查是否是受保护的模板
        if (!$this->is_template_protected($post_id)) {
            return;
        }

        $protection_level = $this->get_protection_level($post_id);
        
        // 完全保护模式下阻止所有修改
        if ($protection_level === 'full') {
            // 检查是否是保护设置的更新
            if (isset($_POST['ss_template_protection_nonce'])) {
                return; // 允许保护设置的更新
            }

            // 检查关键字段是否被修改
            if ($this->has_critical_changes($post_id)) {
                $product_title = get_the_title($post_id);
                error_log("[模板保护] 阻止修改受保护的模板产品: '{$product_title}' (ID: {$post_id})");
                
                wp_die(
                    '无法修改受保护的模板产品 "' . $product_title . '"。请先解除保护后再修改。',
                    '模板保护',
                    array('back_link' => true)
                );
            }
        }
    }

    /**
     * 检查是否有关键字段的修改
     */
    private function has_critical_changes($post_id) {
        // 获取当前产品数据
        $current_post = get_post($post_id);
        if (!$current_post) {
            return false;
        }

        // 检查标题、内容、摘要是否被修改
        if (isset($_POST['post_title']) && $_POST['post_title'] !== $current_post->post_title) {
            return true;
        }

        if (isset($_POST['content']) && $_POST['content'] !== $current_post->post_content) {
            return true;
        }

        if (isset($_POST['excerpt']) && $_POST['excerpt'] !== $current_post->post_excerpt) {
            return true;
        }

        // 检查关键的产品元数据
        $critical_meta_keys = array(
            '_regular_price',
            '_sale_price',
            '_price',
            '_sku',
            '_weight',
            '_length',
            '_width',
            '_height',
            '_ss_template_image_id',
            '_ss_svg_area',
            '_ss_shoe_area',
            '_ss_gallery_templates'
        );

        foreach ($critical_meta_keys as $meta_key) {
            if (isset($_POST[$meta_key])) {
                $current_value = get_post_meta($post_id, $meta_key, true);
                if ($_POST[$meta_key] !== $current_value) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 防止删除受保护模板的图片
     */
    public function prevent_template_image_deletion($attachment_id) {
        // 查找使用此图片的受保护模板产品
        $protected_products = $this->find_products_using_image($attachment_id);
        
        if (!empty($protected_products)) {
            $product_names = array();
            foreach ($protected_products as $product_id) {
                $product_names[] = get_the_title($product_id);
            }
            
            error_log("[模板保护] 阻止删除受保护模板产品使用的图片 (ID: {$attachment_id})，关联产品: " . implode(', ', $product_names));
            
            wp_die(
                '无法删除此图片，因为它被以下受保护的模板产品使用：' . implode(', ', $product_names) . '。请先解除相关模板的保护。',
                '模板保护',
                array('back_link' => true)
            );
        }
    }

    /**
     * 查找使用指定图片的受保护模板产品
     */
    private function find_products_using_image($attachment_id) {
        global $wpdb;
        
        $protected_products = array();
        
        // 查找使用此图片作为主图或画廊图片的产品
        $products = $wpdb->get_results($wpdb->prepare("
            SELECT p.ID 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_ss_is_template' AND pm1.meta_value = '1'
            INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_ss_template_protected' AND pm2.meta_value = '1'
            WHERE p.post_type = 'product'
            AND p.post_status = 'publish'
            AND (
                EXISTS (SELECT 1 FROM {$wpdb->postmeta} WHERE post_id = p.ID AND meta_key = '_thumbnail_id' AND meta_value = %s)
                OR EXISTS (SELECT 1 FROM {$wpdb->postmeta} WHERE post_id = p.ID AND meta_key = '_product_image_gallery' AND meta_value LIKE %s)
                OR EXISTS (SELECT 1 FROM {$wpdb->postmeta} WHERE post_id = p.ID AND meta_key = '_ss_template_image_id' AND meta_value = %s)
            )
        ", $attachment_id, '%' . $attachment_id . '%', $attachment_id));
        
        foreach ($products as $product) {
            $protected_products[] = $product->ID;
        }
        
        return $protected_products;
    }

    /**
     * 防止通过WooCommerce API修改受保护的产品
     */
    public function prevent_wc_product_modification($product) {
        // 检查是否是有效的产品对象
        if (!$product || !is_object($product) || !method_exists($product, 'get_id')) {
            return;
        }

        // 检查是否在管理界面的产品编辑页面
        if (!is_admin() || !current_user_can('edit_posts')) {
            return;
        }

        // 检查是否是实际的保存操作
        if (!isset($_POST) || empty($_POST)) {
            return;
        }

        try {
            $product_id = $product->get_id();
            if (!$product_id || !$this->is_template_protected($product_id)) {
                return;
            }

            $protection_level = $this->get_protection_level($product_id);

            if ($protection_level === 'full') {
                // 检查是否是保护设置的更新
                if (isset($_POST['ss_template_protection_nonce'])) {
                    return; // 允许保护设置的更新
                }

                $product_title = $product->get_name();
                error_log("[模板保护] 阻止通过WooCommerce API修改受保护的模板产品: '{$product_title}' (ID: {$product_id})");

                throw new Exception('无法修改受保护的模板产品 "' . $product_title . '"。请先解除保护后再修改。');
            }
        } catch (Exception $e) {
            // 如果是我们抛出的保护异常，重新抛出
            if (strpos($e->getMessage(), '无法修改受保护的模板产品') !== false) {
                throw $e;
            }
            // 其他异常记录日志但不阻止操作
            error_log('[模板保护] WooCommerce产品修改检查时出错: ' . $e->getMessage());
        }
    }

    /**
     * AJAX处理保护状态切换
     */
    public function ajax_toggle_protection() {
        // 检查nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ss_template_protection_ajax')) {
            wp_die('安全检查失败');
        }

        // 检查权限
        if (!current_user_can('edit_posts')) {
            wp_die('权限不足');
        }

        $product_id = intval($_POST['product_id']);
        $current_protected = $_POST['protected'] === '1';

        // 切换保护状态
        $new_protected = $current_protected ? '0' : '1';
        update_post_meta($product_id, '_ss_template_protected', $new_protected);

        // 如果启用保护，设置默认保护级别
        if ($new_protected === '1') {
            update_post_meta($product_id, '_ss_template_protection_level', 'full');
        }

        $product_title = get_the_title($product_id);
        $action = $new_protected === '1' ? '启用' : '解除';
        
        error_log("[模板保护] 通过AJAX {$action}模板产品保护: '{$product_title}' (ID: {$product_id})");

        wp_send_json_success(array(
            'protected' => $new_protected === '1',
            'message' => "已{$action}模板保护"
        ));
    }

    /**
     * 修改产品列表的行操作
     */
    public function modify_row_actions($actions, $post) {
        if ($post->post_type === 'product' && get_post_meta($post->ID, '_ss_is_template', true) === '1') {
            if ($this->is_template_protected($post->ID)) {
                // 移除删除链接
                unset($actions['trash']);
                
                // 添加保护状态指示
                $actions['protection_status'] = '<span style="color: #0073aa;">🛡️ 已保护</span>';
            }
        }
        
        return $actions;
    }

    /**
     * 显示保护相关的管理通知
     */
    public function show_protection_notices() {
        $screen = get_current_screen();
        
        if ($screen && $screen->post_type === 'product') {
            global $post;
            
            if ($post && $this->is_template_protected($post->ID)) {
                echo '<div class="notice notice-info"><p>';
                echo '<span class="dashicons dashicons-shield-alt"></span> ';
                echo '此模板产品已受保护，无法被修改或删除。';
                echo '</p></div>';
            }
        }
    }

    /**
     * 加载管理界面脚本和样式
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook === 'post.php' || $hook === 'post-new.php') {
            $screen = get_current_screen();
            
            if ($screen && $screen->post_type === 'product') {
                // 检查JavaScript文件是否存在
                $js_file = plugin_dir_path(__FILE__) . 'assets/js/template-protector.js';
                if (file_exists($js_file)) {
                    wp_enqueue_script(
                        'ss-template-protector',
                        plugin_dir_url(__FILE__) . 'assets/js/template-protector.js',
                        array('jquery'),
                        '1.0.0',
                        true
                    );

                    wp_localize_script('ss-template-protector', 'ssProtector', array(
                        'ajax_url' => admin_url('admin-ajax.php'),
                        'nonce' => wp_create_nonce('ss_template_protection_ajax')
                    ));
                } else {
                    // 如果文件不存在，记录错误但不中断执行
                    error_log('[模板保护] JavaScript文件不存在: ' . $js_file);
                }
            }
        }
    }
}
