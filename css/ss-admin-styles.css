.ss-template-settings img {
    max-width: 100%;
    height: auto;
}

/* 主图模板预览样式优化 */
.main-template-preview {
    position: relative;
    margin-bottom: 20px;
    padding: 15px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background: #fafafa;
    text-align: center;
    overflow: hidden;
}

.main-template-image-container {
    position: relative;
    display: inline-block;
    user-select: none;
}

.main-template-image {
    display: block;
    max-width: 500px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.main-template-controls {
    margin: 15px 0;
    padding: 10px;
    background: #f0f0f1;
    border-radius: 4px;
}

.main-template-controls p {
    margin: 5px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.main-template-controls .button {
    margin: 0;
    font-size: 12px;
    padding: 4px 8px;
}

/* 兼容旧的ID选择器 */
#ss_template_image_preview {
    position: relative;
    margin-bottom: 20px;
    max-width: 100%;
    overflow: hidden;
}

#ss_template_image_preview img {
    display: block;
    max-width: 100%;
}

/* Cropper.js相关样式 */
.cropper-container {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    touch-action: none;
    user-select: none;
}

.cropper-wrap-box,
.cropper-canvas,
.cropper-drag-box,
.cropper-crop-box,
.cropper-modal {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
}

.cropper-wrap-box {
    overflow: hidden;
}

.cropper-drag-box {
    background-color: #fff;
    opacity: 0;
}

.cropper-modal {
    background-color: #000;
    opacity: 0.5;
}

.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid #39f;
    outline-color: rgba(51, 153, 255, 0.75);
    overflow: hidden;
    width: 100%;
}

/* 主图模板选区样式 */
.ss-svg-area,
.ss-shoe-area,
.ss-selection {
    position: absolute;
    pointer-events: none;
    z-index: 10;
    box-sizing: border-box;
}

.ss-svg-area {
    border: 2px dashed red;
    background-color: rgba(255, 0, 0, 0.1);
}

.ss-shoe-area {
    border: 2px dashed blue;
    background-color: rgba(0, 0, 255, 0.1);
}

/* 确保选框容器正确定位 */
.main-template-image-container {
    position: relative;
    display: inline-block;
    user-select: none;
}

.main-template-image-container .ss-svg-area,
.main-template-image-container .ss-shoe-area {
    position: absolute;
    top: 0;
    left: 0;
}

/* 按钮样式 */
.ss-area-buttons {
    margin: 10px 0;
}

.ss-area-buttons button {
    margin-right: 10px;
}

/* 输入框样式 */
.ss-area-inputs {
    margin: 15px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.ss-area-inputs label {
    display: block;
    margin-bottom: 5px;
}

.ss-area-inputs input[type="number"] {
    width: 100%;
    padding: 5px;
}

/* 提取的颜色显示样式 */
.ss-extracted-colors {
    display: flex;
    flex-wrap: wrap;
    max-width: 500px;
}

.ss-extracted-colors .ss-extracted-color-item {
    width: 20%;
    margin-bottom: 10px;
    text-align: center;
}

.ss-extracted-colors .ss-extracted-color-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 auto;
    border: 1px solid #ccc;
}

.ss-extracted-colors .ss-extracted-color-code {
    font-size: 10px;
    margin-top: 5px;
    word-break: break-all;
}

.ss-extracted-colors::after {
    content: '';
    flex-basis: 100%;
    height: 0;
}
/* 颜色下拉菜单样式 */
.ss-color-option {
    display: inline-flex;
    align-items: center;
}

.ss-color-swatch {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border: 1px solid #ccc;
}

/* 分页样式 */
.tablenav .tablenav-pages {
    text-align: right;
}
.tablenav .tablenav-pages a,
.tablenav .tablenav-pages span {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 2px;
    border: 1px solid #ccc;
    background: #f7f7f7;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    color: #2271b1;
    text-decoration: none;
}
.tablenav .tablenav-pages span.current {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}
.tablenav .tablenav-pages a:hover {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}

.ss-default-color-variant {
    margin-left: 10px;
    min-width: 200px;
}

.ss-template-product-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.ss-template-product-item label {
    margin-right: 10px;
    min-width: 150px;
}

/* 模板展示管理页面样式 */
.ss-template-display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.ss-template-display-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.ss-template-info {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.ss-template-info h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #23282d;
}

.ss-template-info .description {
    color: #666;
    font-style: italic;
    margin: 5px 0;
}

.ss-template-display-settings {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.ss-template-display-image {
    position: relative;
}

.ss-template-display-image label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.image-preview-wrapper {
    margin: 10px 0;
    min-height: 150px;
    background: #f7f7f7;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview {
    max-width: 100%;
    max-height: 200px;
    display: block;
}

.ss-template-display-title label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
}

.ss-template-display-title input[type="text"] {
    width: 100%;
}

.ss-template-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.ss-template-actions .spinner {
    float: none;
    margin: 0;
}

.ss-template-actions .save-status {
    flex-grow: 1;
}

.ss-template-actions .notice {
    margin: 0;
    padding: 5px 10px;
}

/* 响应式调整 */
@media screen and (max-width: 782px) {
    .ss-template-display-grid {
        grid-template-columns: 1fr;
    }

    .ss-template-display-item {
        padding: 15px;
    }

    .image-preview-wrapper {
        min-height: 100px;
    }
}

/* 按钮组样式 */
.ss-template-display-image .button {
    margin-right: 10px;
}

/* 图片预览容器样式优化 */
.image-preview-wrapper:empty {
    padding: 40px;
    text-align: center;
}

.image-preview-wrapper:empty::after {
    content: '点击"选择图片"按钮上传展示图片';
    color: #666;
    font-style: italic;
}

/* 保存状态消息样式 */
.save-status .notice {
    margin: 5px 0;
}

.save-status .notice p {
    margin: 0.5em 0;
}

/* 加载中动画样式 */
.spinner.is-active {
    visibility: visible;
}

/* Gallery模板设置样式 */
#ss_gallery_templates_container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    background: #f9f9f9;
}

/* Gallery模板选框样式 */
.gallery-template-image-container {
    position: relative;
    display: inline-block;
    user-select: none;
}

.gallery-template-image {
    display: block;
    max-width: 300px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.gallery-svg-area-overlay {
    position: absolute;
    border: 2px dashed #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    pointer-events: none;
    z-index: 10;
    box-sizing: border-box;
}

.gallery-template-image.selecting {
    cursor: crosshair !important;
}

.select-gallery-svg-area {
    background: #ff6b6b;
    border-color: #ff6b6b;
    color: #fff;
    margin-bottom: 10px;
}

.select-gallery-svg-area:hover {
    background: #ff5252;
    border-color: #ff5252;
}

/* 防止选择时的文本选择和拖拽 */
.gallery-selecting {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.gallery-template-image {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

.gallery-template-image-container {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 选择模式下的样式 */
.gallery-template-image.selecting {
    cursor: crosshair !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.ss-gallery-template-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.ss-gallery-template-item:last-child {
    margin-bottom: 0;
}

.ss-gallery-template-item h5 {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.ss-gallery-template-item h6 {
    margin: 15px 0 10px 0;
    font-size: 13px;
    font-weight: 600;
    color: #555;
}

.gallery-template-image-preview {
    margin: 10px 0;
    padding: 10px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background: #fafafa;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-template-image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.gallery-template-image-preview:empty::after {
    content: '选择模板图片后将在此显示预览';
    color: #999;
    font-style: italic;
    font-size: 12px;
}

.ss-gallery-template-item .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.ss-gallery-template-item .button-secondary {
    color: #d63638;
    border-color: #d63638;
}

.ss-gallery-template-item .button-secondary:hover {
    background: #d63638;
    color: #fff;
}

.ss-gallery-template-item p {
    margin: 10px 0;
}

.ss-gallery-template-item label {
    display: inline-block;
    margin-right: 10px;
    min-width: 60px;
    font-weight: 500;
}

.ss-gallery-template-item input[type="number"] {
    width: 80px;
    margin-right: 15px;
}

#add_gallery_template {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    margin-top: 10px;
}

#add_gallery_template:hover {
    background: #135e96;
    border-color: #135e96;
}

/* SVG区域设置样式 */
.ss-gallery-template-item .svg-area-settings {
    background: #f0f0f1;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}

.ss-gallery-template-item .svg-area-settings p {
    margin: 5px 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

/* Gallery模板控制按钮样式 */
.gallery-template-controls {
    margin: 10px 0;
    padding: 8px;
    background: #f0f0f1;
    border-radius: 4px;
}

.gallery-template-controls p {
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
}

.gallery-template-controls .button {
    margin: 0;
    font-size: 11px;
    padding: 3px 6px;
    min-height: auto;
}

/* Gallery可调整选框样式 */
.gallery-svg-area-overlay {
    position: absolute;
    border: 2px dashed #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    display: none;
    pointer-events: none;
    z-index: 10;
    box-sizing: border-box;
}

.gallery-svg-area-overlay.active {
    pointer-events: auto;
    border: 2px solid #0073aa;
    background: rgba(0, 115, 170, 0.1);
}

/* 调整手柄样式 - 始终可点击 */
.gallery-resize-handle {
    position: absolute;
    background: #0073aa;
    border: 1px solid #fff;
    width: 8px;
    height: 8px;
    z-index: 11;
    box-sizing: border-box;
    pointer-events: auto !important; /* 强制启用点击事件 */
    cursor: pointer;
}

.gallery-resize-handle:hover {
    background: #005a87;
    transform: scale(1.2); /* 悬停时稍微放大 */
}

/* 移动手柄样式 - 始终可点击 */
.gallery-move-handle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    background: #0073aa;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: move;
    z-index: 12;
    pointer-events: auto !important; /* 强制启用点击事件 */
}

.gallery-move-handle:hover {
    background: #005a87;
    transform: scale(1.1); /* 悬停时稍微放大 */
}

/* 各个方向的调整手柄 */
.gallery-resize-handle.nw {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
}

.gallery-resize-handle.n {
    top: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: n-resize;
}

.gallery-resize-handle.ne {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
}

.gallery-resize-handle.e {
    top: 50%;
    right: -4px;
    margin-top: -4px;
    cursor: e-resize;
}

.gallery-resize-handle.se {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
}

.gallery-resize-handle.s {
    bottom: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: s-resize;
}

.gallery-resize-handle.sw {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
}

.gallery-resize-handle.w {
    top: 50%;
    left: -4px;
    margin-top: -4px;
    cursor: w-resize;
}



/* 缩放相关样式 */
.main-template-image.zoomed,
.gallery-template-image.zoomed {
    cursor: move;
}

.main-template-image-container.zoom-mode,
.gallery-template-image-container.zoom-mode {
    overflow: auto;
    max-width: 100%;
    max-height: 400px;
    border: 2px solid #0073aa;
    border-radius: 4px;
}

/* 响应式调整 */
@media screen and (max-width: 782px) {
    .ss-gallery-template-item {
        padding: 10px;
    }

    .ss-gallery-template-item label {
        min-width: auto;
        display: block;
        margin-bottom: 5px;
    }

    .ss-gallery-template-item input[type="number"] {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .ss-gallery-template-item .svg-area-settings p {
        flex-direction: column;
        align-items: flex-start;
    }

    /* 主图模板响应式 */
    .main-template-image {
        max-width: 100% !important;
    }

    .main-template-controls p {
        justify-content: flex-start;
    }

    .main-template-controls .button {
        font-size: 11px;
        padding: 3px 6px;
    }

    /* Gallery模板控制按钮响应式 */
    .gallery-template-controls p {
        justify-content: flex-start;
    }

    .gallery-template-controls .button {
        font-size: 10px;
        padding: 2px 4px;
    }

    /* 缩放模式下的容器调整 */
    .main-template-image-container.zoom-mode,
    .gallery-template-image-container.zoom-mode {
        max-height: 300px;
    }

    .gallery-template-image-preview img {
        max-width: 100%;
    }
}

/* 主图模板选框样式 - 与Gallery模板一致 */
.main-template-area-overlay {
    position: absolute;
    display: none;
    pointer-events: none;
    z-index: 10;
    box-sizing: border-box;
}

.main-template-area-overlay.svg-area {
    border: 2px dashed red;
    background: rgba(255, 0, 0, 0.1);
}

.main-template-area-overlay.shoe-area {
    border: 2px dashed blue;
    background: rgba(0, 0, 255, 0.1);
}

.main-template-area-overlay.active {
    pointer-events: auto;
    border-style: solid;
}

.main-template-area-overlay.svg-area.active {
    border-color: #d63638;
    background: rgba(214, 54, 56, 0.1);
}

.main-template-area-overlay.shoe-area.active {
    border-color: #0073aa;
    background: rgba(0, 115, 170, 0.1);
}

/* 主图模板调整手柄样式 */
.main-template-resize-handle {
    position: absolute;
    background: #0073aa;
    border: 1px solid #fff;
    width: 8px;
    height: 8px;
    z-index: 11;
    box-sizing: border-box;
    pointer-events: auto !important;
    cursor: pointer;
}

.main-template-area-overlay.svg-area .main-template-resize-handle {
    background: #d63638;
}

.main-template-resize-handle:hover {
    transform: scale(1.2);
}

.main-template-area-overlay.svg-area .main-template-resize-handle:hover {
    background: #b32d2e;
}

.main-template-area-overlay.shoe-area .main-template-resize-handle:hover {
    background: #005a87;
}

/* 主图模板移动手柄样式 */
.main-template-move-handle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    background: #0073aa;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: move;
    z-index: 12;
    pointer-events: auto !important;
}

.main-template-area-overlay.svg-area .main-template-move-handle {
    background: #d63638;
}

.main-template-move-handle:hover {
    transform: scale(1.1);
}

.main-template-area-overlay.svg-area .main-template-move-handle:hover {
    background: #b32d2e;
}

.main-template-area-overlay.shoe-area .main-template-move-handle:hover {
    background: #005a87;
}

/* 主图模板各个方向的调整手柄 */
.main-template-resize-handle.nw {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
}

.main-template-resize-handle.n {
    top: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: n-resize;
}

.main-template-resize-handle.ne {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
}

.main-template-resize-handle.e {
    top: 50%;
    right: -4px;
    margin-top: -4px;
    cursor: e-resize;
}

.main-template-resize-handle.se {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
}

.main-template-resize-handle.s {
    bottom: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: s-resize;
}

.main-template-resize-handle.sw {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
}

.main-template-resize-handle.w {
    top: 50%;
    left: -4px;
    margin-top: -4px;
    cursor: w-resize;
}

/* 主图模板选择模式样式 */
.main-template-image.selecting {
    cursor: crosshair !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.main-template-selecting {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.main-template-image {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* 主图模板按钮样式优化 */
#ss_select_svg_area {
    background: #d63638;
    border-color: #d63638;
    color: #fff;
}

#ss_select_svg_area:hover {
    background: #b32d2e;
    border-color: #b32d2e;
}

#ss_select_shoe_area {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

#ss_select_shoe_area:hover {
    background: #005a87;
    border-color: #005a87;
}

#ss_save_area {
    background: #00a32a;
    border-color: #00a32a;
    color: #fff;
}

#ss_save_area:hover {
    background: #008a20;
    border-color: #008a20;
}
