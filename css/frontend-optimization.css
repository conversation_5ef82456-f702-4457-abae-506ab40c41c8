/* 前端SVG预览优化样式 */

/* 进度条优化 */
.ss-loading-progress {
    position: relative;
    margin: 20px 0;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    height: 20px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.ss-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ss-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.ss-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: #333;
    font-size: 12px;
    text-shadow: 0 1px 2px rgba(255,255,255,0.8);
    z-index: 10;
}

/* 高优先级项目的特殊样式 */
.ss-svg-preview-item[data-priority="high"] {
    border: 2px solid #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.ss-svg-preview-item[data-priority="high"] .skeleton-placeholder {
    background: linear-gradient(90deg, #e8f5e8, #f0f8f0, #e8f5e8);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 骨架屏优化 */
.skeleton-loader {
    position: relative;
    overflow: hidden;
}

.skeleton-placeholder {
    background: linear-gradient(90deg, #f0f0f0, #f8f8f8, #f0f0f0);
    background-size: 200% 100%;
    animation: skeleton-loading 2s ease-in-out infinite;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

@keyframes skeleton-loading {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 加载完成的淡入效果 */
.ss-svg-preview-image.loaded img {
    animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 处理中状态的脉冲效果 */
.ss-svg-preview-image.loading-active .skeleton-placeholder {
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 进度条完成状态 */
.ss-loading-progress.completed {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.5s ease;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .ss-progress-text {
        font-size: 10px;
    }
    
    .ss-loading-progress {
        height: 16px;
    }
}

/* 加载更多按钮优化 */
.ss-load-more-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.ss-load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 错误状态优化 */
.ss-svg-preview-image.load-error {
    background: #ffebee;
    border: 2px dashed #f44336;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.error-message {
    color: #f44336;
    font-size: 12px;
    margin-bottom: 8px;
}

.retry-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
}

.retry-btn:hover {
    background: #d32f2f;
}