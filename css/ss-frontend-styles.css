/* 前端SVG设计选择页面样式 */
.ss-frontend-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.ss-frontend-header {
    text-align: center;
    margin-bottom: 30px;
}

.ss-frontend-header h1 {
    font-size: 28px;
    margin-bottom: 10px;
    color: #333;
}

.ss-frontend-header p {
    font-size: 16px;
    color: #666;
}

/* SVG设计网格 */
.ss-svg-designs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ss-svg-design-item {
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    background-color: #fff;
}

.ss-svg-design-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.ss-svg-design-thumbnail {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    padding: 10px;
}

.ss-svg-design-thumbnail img {
    max-width: 100%;
    max-height: 160px;
    object-fit: contain;
}

.ss-svg-design-name {
    padding: 10px;
    text-align: center;
    font-size: 14px;
    color: #333;
    border-top: 1px solid #eee;
}

/* 加载更多指示器 */
.ss-loading-more {
    text-align: center;
    padding: 20px 0;
}

.ss-loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.ss-loading-more p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 无设计提示 */
.ss-no-designs-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 0;
    color: #666;
    font-size: 16px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .ss-svg-designs-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .ss-svg-design-thumbnail {
        height: 150px;
    }
    
    .ss-frontend-header h1 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .ss-svg-designs-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .ss-svg-design-thumbnail {
        height: 120px;
    }
    
    .ss-frontend-header h1 {
        font-size: 20px;
    }
    
    .ss-frontend-header p {
        font-size: 14px;
    }
}

/* 加载更多按钮容器 */
.ss-load-more-container {
    text-align: center;
    margin: 30px 0;
}

.ss-load-more-btn {
    display: inline-block;
    padding: 12px 30px;
    background-color: #3498db;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.ss-load-more-btn:hover {
    background-color: #2980b9;
}

.ss-load-more-btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

/* 加载动画 */
.ss-loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
    margin: 0 auto;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 选中状态 */
.ss-svg-design-item.selected {
    border-color: #3498db;
    box-shadow: 0 0 0 2px #3498db;
}

/* SVG设计选择页面样式 */
.ss-design-selection-container {
    width: 100%;
    max-width: 100%;
    padding: 0 15px;
    margin: 0 auto;
    box-sizing: border-box;
}

/* 使用Flatsome的container类限制宽度 */
.ss-design-selection-container .ss-inner-container {
    width: 100%;
    max-width: 1080px; /* 与Flatsome shop container宽度匹配 */
    margin: 0 auto;
}

.ss-page-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
}

.ss-page-description {
    text-align: center;
    margin-bottom: 30px;
    font-size: 16px;
    color: #666;
}

/* 设计网格 - 修改为每行4列 */
.ss-design-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 每行4列 */
    gap: 20px;
    margin-bottom: 30px;
}

/* 响应式布局 - 平板设备 */
@media (max-width: 991px) {
    .ss-design-grid {
        grid-template-columns: repeat(3, 1fr); /* 平板设备每行3列 */
    }
}

/* 响应式布局 - 手机设备 */
@media (max-width: 767px) {
    .ss-design-grid {
        grid-template-columns: repeat(2, 1fr); /* 手机设备每行2列 */
        gap: 15px;
    }
    
    .ss-design-selection-container {
        padding: 15px;
        margin: 15px auto;
    }
    
    .ss-page-title {
        font-size: 20px;
    }
}

/* 设计项样式优化 */
.ss-design-item {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ss-design-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.ss-design-thumbnail {
    position: relative;
    padding-top: 100%; /* 保持1:1比例 */
    background-color: #f0f0f0;
}

.ss-design-thumbnail img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 10px;
}

.ss-design-name {
    padding: 10px;
    text-align: center;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: auto; /* 将文字推到底部 */
}

/* 加载更多按钮样式优化 */
.ss-load-more-container {
    text-align: center;
    margin-top: 20px;
}

.ss-load-more-btn {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

/* 确保整个容器在不同屏幕尺寸下居中 */
@media (min-width: 1200px) {
    .ss-design-selection-container {
        max-width: 1170px;
    }
}

@media (min-width: 1400px) {
    .ss-design-selection-container {
        max-width: 1320px;
    }
}

/* 【优化】SVG预览容器 - 立即显示框架，与类目图片容器宽度一致 */
.ss-svg-preview-container {
    margin: 30px auto; /* 居中显示 */
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
    /* 立即显示，不等待JavaScript */
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    transition: all 0.3s ease;
    /* 【关键】与类目图片容器保持相同的最大宽度 */
    max-width: 800px;
    width: 100%;
}

.ss-svg-preview-container h2 {
    margin-bottom: 20px;
    text-align: center;
}

/* SVG预览网格 */
.ss-svg-preview-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 桌面端4列 */
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
}

/* SVG预览项 */
.ss-svg-preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 6px;
    padding: 10px;
    transition: all 0.3s ease;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.ss-svg-preview-item:hover {
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transform: translateY(-3px);
}

/* 预览图片容器 */
.ss-svg-preview-image {
    width: 100%;
    aspect-ratio: 1 / 1;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f8f8f8;
    position: relative;
}

.ss-svg-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

/* 预览名称 */
.ss-svg-preview-name {
    margin-top: 10px;
    font-size: 14px;
    text-align: center;
    color: #333;
    font-weight: 500;
    padding: 0 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

/* 加载更多按钮 */
.ss-svg-load-more {
    text-align: center;
    margin-top: 20px;
}

.ss-svg-load-more-btn {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.ss-svg-load-more-btn:hover {
    background-color: #135e96;
}

/* 占位符加载动画 */
.loading-preview {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-animation 1.5s infinite;
}

@keyframes loading-animation {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 骨架屏加载器 */
.skeleton-loader {
    position: relative;
    background-color: #f0f0f0;
    overflow: hidden;
}

.skeleton-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite ease-in-out;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 活跃加载样式 */
.loading-active .skeleton-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #d9edf7 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: active-loading 1.2s infinite ease-in-out;
}

@keyframes active-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 淡入效果 */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 【优化】进度条容器 - 悬浮显示且立即可见 */
.ss-loading-progress {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 8px;
    /* 立即显示，不等待JavaScript */
    opacity: 1 !important;
    visibility: visible !important;
    background-color: rgba(240, 240, 240, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: flex;
    align-items: center;
    transition: opacity 0.5s ease;
}

/* 进度条填充部分 */
.ss-progress-bar {
    height: 100%;
    width: 0%;
    background-color: #2271b1;
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
    position: relative;
    min-width: 5px;
}

/* 进度文本 */
.ss-progress-text {
    position: absolute;
    right: -55px; /* 【修复】增加距离，避免被进度条遮挡 */
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    font-weight: bold;
    color: #2271b1;
    white-space: nowrap; /* 【新增】防止文字换行 */
    min-width: 45px; /* 【新增】确保有足够空间显示数字 */
    text-align: center; /* 【新增】文字居中对齐 */
}

/* 【修复】完成状态 - 保持显示，不隐藏 */
.ss-loading-progress.completed {
    opacity: 1; /* 【修复】保持完全可见 */
    pointer-events: auto; /* 【修复】保持可交互 */
}

/* 【新增】倒计时消息样式 */
.ss-countdown-message {
    position: fixed;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(33, 113, 177, 0.95);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    max-width: 90vw;
    text-align: center;
}

.ss-countdown-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.ss-countdown-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 【新增】现代化SVG时钟图标样式 */
.ss-timer-icon {
    width: 16px;
    height: 16px;
    color: currentColor;
    animation: clockTick 2s infinite;
    transform-origin: center;
}

/* 时钟滴答动画 - 更现代化的效果 */
@keyframes clockTick {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
    25% {
        transform: rotate(5deg) scale(1.05);
        opacity: 0.9;
    }
    50% {
        transform: rotate(0deg) scale(1.1);
        opacity: 0.8;
    }
    75% {
        transform: rotate(-5deg) scale(1.05);
        opacity: 0.9;
    }
}

.ss-countdown-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .ss-countdown-message {
        bottom: 80px;
        padding: 10px 16px;
        font-size: 13px;
        border-radius: 20px;
        max-width: 85vw;
    }

    .ss-countdown-content {
        gap: 6px;
    }

    .ss-timer-icon {
        width: 14px;
        height: 14px;
    }

    .ss-countdown-text {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .ss-countdown-message {
        bottom: 70px;
        padding: 8px 14px;
        font-size: 12px;
        border-radius: 18px;
        max-width: 80vw;
    }

    .ss-countdown-text {
        font-size: 12px;
    }

    .ss-timer-icon {
        width: 12px;
        height: 12px;
    }
}

/* 移动端适配 */
@media screen and (max-width: 767px) {
    .ss-loading-progress {
        max-width: 90%;
        height: 6px;
        margin-bottom: 20px;
    }
    
    .ss-progress-text {
        font-size: 12px;
        right: -45px; /* 【修复】移动端也增加距离 */
        min-width: 40px; /* 【新增】移动端适配 */
    }
}

/* 错误状态 */
.load-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff0f0;
    border: 1px dashed #ffcccc;
}

.error-message {
    color: #e53935;
    font-size: 12px;
    margin-bottom: 8px;
}

.retry-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
}

.retry-btn:hover {
    background-color: #d32f2f;
}

/* 响应式布局调整 - 确保SVG预览容器与类目图片容器宽度一致 */

/* 桌面端优化 (1200px+) - 与类目图片容器保持一致 */
@media (min-width: 1200px) {
    .ss-svg-preview-container {
        max-width: 1080px; /* 与类目图片容器一致 */
    }
}

/* 大桌面端优化 (1024px - 1199px) - 与类目图片容器保持一致 */
@media (min-width: 1024px) and (max-width: 1199px) {
    .ss-svg-preview-container {
        max-width: 800px; /* 与类目图片容器一致 */
    }
}

/* 平板端优化 (768px - 1023px) - 与类目图片容器保持一致 */
@media (min-width: 768px) and (max-width: 1023px) {
    .ss-svg-preview-container {
        max-width: 700px; /* 与类目图片容器一致 */
    }
}

/* 手机端优化 (480px - 767px) - 与类目图片容器保持一致 */
@media (min-width: 480px) and (max-width: 767px) {
    .ss-svg-preview-container {
        max-width: 100%; /* 与类目图片容器一致 */
        margin: 30px 15px; /* 与类目图片容器边距一致 */
    }
}

/* 小手机端优化 (最大479px) - 与类目图片容器保持一致 */
@media (max-width: 479px) {
    .ss-svg-preview-container {
        max-width: 100%; /* 与类目图片容器一致 */
        margin: 30px 10px; /* 与类目图片容器边距一致 */
    }
}

/* SVG预览网格响应式调整 */
@media screen and (max-width: 992px) {
    .ss-svg-preview-grid {
        grid-template-columns: repeat(3, 1fr); /* 平板端3列 */
    }
}

@media screen and (max-width: 767px) {
    .ss-svg-preview-grid {
        grid-template-columns: repeat(2, 1fr); /* 手机端2列 */
        gap: 15px;
    }

    .ss-page-title {
        font-size: 20px;
    }

    .ss-page-description {
        font-size: 14px;
    }

    .ss-svg-preview-name {
        font-size: 13px;
    }

    .ss-loading-progress {
        max-width: 90%;
        height: 6px;
        margin-bottom: 20px;
    }
}

/* 确保图片预览整齐对齐 */
.ss-svg-preview-item {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ss-svg-preview-image {
    flex: 1;
    margin-bottom: 10px;
}

/* 模板选择弹窗样式 */
.ss-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;

    /* 【优化】移除固定居中，改为动态定位 */
    display: none; /* 初始状态保持隐藏 */
    justify-content: center;
    /* 移除 align-items: center，改为动态计算位置 */
}

.ss-modal-content {
    position: absolute;
    background-color: #fff;
    /* 【修复】与SVG预览容器宽度保持一致 */
    width: 90%;
    max-width: 800px; /* 与SVG预览容器默认最大宽度一致 */
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);

    /* 【优化】动态定位，初始位置将通过JavaScript计算 */
    left: 50%;
    transform: translateX(-50%);
    top: 20px; /* 默认距离顶部20px，将被JavaScript动态调整 */

    /* 添加最大高度限制，避免内容过长时超出屏幕 */
    max-height: calc(100vh - 40px); /* 确保上下各留20px边距 */
    overflow: hidden; /* 改为hidden，让内部元素控制滚动 */

    /* 【修复】确保不会超出屏幕边界 */
    box-sizing: border-box;
}

.ss-modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ss-modal-header h3 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.ss-modal-close {
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
    background-color: transparent;
}

.ss-modal-close:hover {
    color: #333;
    background-color: #f5f5f5;
}

/* 【新增】移动端关闭按钮优化 */
@media screen and (max-width: 768px) {
    .ss-modal-close {
        font-size: 26px;
        min-width: 44px;
        min-height: 44px;
        padding: 10px;
        -webkit-tap-highlight-color: rgba(0,0,0,0.1);
    }

    .ss-modal-close:active {
        background-color: #e9ecef;
        transform: scale(0.95);
    }
}

.ss-modal-body {
    padding: 20px;
    min-height: 200px;
    position: relative;
}

/* 【新增】横向滑动容器样式 */
.ss-template-slider-container {
    position: relative;
    width: 100%;
}

.ss-template-slider-wrapper {
    overflow-x: auto;
    overflow-y: hidden;
    /* 隐藏默认滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    padding-bottom: 15px; /* 为横向滑块留出空间 */
}

.ss-template-slider-wrapper::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 【新增】横向滑块样式 */
.ss-horizontal-scrollbar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.ss-horizontal-scrollbar.visible {
    opacity: 1;
    pointer-events: auto;
}

.ss-horizontal-scrollbar-track {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    position: relative;
}

.ss-horizontal-scrollbar-thumb {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: rgba(34, 113, 177, 0.6);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    min-width: 30px;
}

.ss-horizontal-scrollbar-thumb:hover {
    background-color: rgba(34, 113, 177, 0.8);
}

.ss-horizontal-scrollbar-thumb:active {
    background-color: rgba(34, 113, 177, 1);
}

/* 模态窗口底部样式 */
.ss-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
}

/* 【废弃】原生成状态样式 - 现在使用模板项内的状态显示 */
.ss-generation-status {
    display: none !important; /* 【优化】隐藏原来的弹窗底部状态显示 */
}

.ss-status-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border: 2.5px solid rgba(0,0,0,0.1);
    border-top: 2.5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.ss-status-message {
    font-size: 16px;
    line-height: 1.4;
    font-weight: 500;
    color: #2c3e50;
    margin: 0;
    text-align: left;
}

/* 响应式样式调整 */
@media screen and (max-width: 768px) {
    .ss-modal-footer {
        padding: 12px 15px;
        min-height: 5px; /* 【修改】减小高度为5px */
        margin-top: 10px;
    }

    .ss-generation-status {
        padding: 12px;
    }

    .ss-status-spinner {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-width: 2px;
    }

    .ss-status-message {
        font-size: 14px;
    }
}

/* 添加平滑过渡效果 */
.ss-generation-status,
.ss-status-message {
    transition: all 0.3s ease;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模板网格样式 */
.ss-template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* 【新增】移动端横向滑动模板网格 */
@media screen and (max-width: 768px) {
    .ss-template-grid {
        display: flex;
        flex-wrap: nowrap;
        gap: 15px;
        margin-top: 15px;
        width: max-content; /* 确保内容宽度超出容器 */
        min-width: 100%;
        padding: 0 5px; /* 左右留一点边距 */
    }

    .ss-template-item {
        flex: 0 0 140px; /* 固定宽度，不收缩 */
        width: 140px;
        /* 【新增】触摸优化 */
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* 【新增】确保滑动容器可以被触摸 */
    .ss-template-slider-wrapper {
        touch-action: pan-x; /* 只允许水平滑动 */
        -webkit-overflow-scrolling: touch;
        cursor: grab;
    }

    .ss-template-slider-wrapper:active {
        cursor: grabbing;
    }

    /* 【新增】移动端滑动指示 */
    .ss-template-slider-wrapper::after {
        content: '← Swipe to see more →';
        position: absolute;
        bottom: -35px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 13px;
        color: #666;
        white-space: nowrap;
        opacity: 0.8;
        pointer-events: none;
        z-index: 10;
    }
}

/* 【新增】小屏手机横向滑动优化 */
@media screen and (max-width: 480px) {
    .ss-template-grid {
        gap: 12px;
        padding: 0 3px; /* 小屏手机更小的边距 */
    }

    .ss-template-item {
        flex: 0 0 120px;
        width: 120px;
    }

    /* 【新增】小屏手机滑动指示优化 */
    .ss-template-slider-wrapper::after {
        font-size: 12px; /* 小屏手机稍小的字体 */
        color: #999; /* 稍淡的颜色 */
        opacity: 0.7;
    }
}

.ss-template-item {
    border: 2px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #fff;
    position: relative; /* 【新增】为状态覆盖层提供定位基准 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.ss-template-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #bbb;
}

.ss-template-item.selected {
    border-color: #2271b1;
    box-shadow: 0 0 0 2px rgba(34, 113, 177, 0.3);
    transform: translateY(-2px);
}

.ss-template-image {
    position: relative;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    overflow: hidden;
    background-color: #f8f9fa;
    border-radius: 6px 6px 0 0;
}

.ss-template-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ss-template-item:hover .ss-template-image img {
    transform: scale(1.03);
}

.ss-template-title {
    padding: 12px 10px;
    text-align: center;
    font-weight: 500;
    color: #2c3e50;
    background-color: #fff;
    border-top: 1px solid #e9ecef;
    font-size: 14px;
    line-height: 1.3;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 加载状态样式 */
.ss-modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 20px;
}

.ss-modal-loading p {
    margin-top: 15px;
    color: #666;
    font-size: 16px;
    font-weight: 500;
}

/* 【新增】移动端加载状态优化 */
@media screen and (max-width: 768px) {
    .ss-modal-loading {
        min-height: 150px;
        padding: 15px;
    }

    .ss-modal-loading p {
        font-size: 14px;
        margin-top: 12px;
    }
}

@media screen and (max-width: 480px) {
    .ss-modal-loading {
        min-height: 120px;
        padding: 12px;
    }

    .ss-modal-loading p {
        font-size: 13px;
        margin-top: 10px;
    }
}

.ss-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* 错误状态样式 */
.ss-modal-error {
    text-align: center;
    padding: 20px;
}

.ss-error-message {
    color: #dc3232;
    margin-bottom: 15px;
}

.ss-retry-btn {
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.ss-retry-btn:hover {
    background-color: #135e96;
}

/* 调试信息样式 */
.ss-debug-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
    font-family: monospace;
    font-size: 12px;
    max-height: 150px;
    overflow-y: auto;
}

.ss-debug-output {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 响应式调整 - 移动端优化 */
@media screen and (max-width: 768px) {
    .ss-modal-content {
        /* 【修复】与SVG预览容器宽度保持一致，确保不超出屏幕边界 */
        width: calc(100% - 30px); /* 左右各留15px边距 */
        max-width: 700px; /* 与平板端SVG预览容器一致 */
        max-height: calc(100vh - 20px); /* 【优化】增加显示高度 */
        top: 10px; /* 【优化】减少顶部距离 */
        border-radius: 12px;
        margin: 0 auto;
        /* 【修复】确保水平居中 */
        left: 50%;
        transform: translateX(-50%);
    }

    .ss-modal-header {
        padding: 15px 20px 10px;
        border-bottom: 1px solid #eee;
    }

    .ss-modal-header h3 {
        font-size: 20px;
        font-weight: 600;
        margin: 0;
        color: #2c3e50;
    }

    .ss-modal-close {
        font-size: 28px;
        font-weight: 300;
        color: #666;
        padding: 5px;
        min-width: 40px;
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ss-modal-body {
        padding: 15px 20px 20px;
        max-height: calc(100vh - 100px); /* 【优化】增加显示高度，减少顶部预留空间 */
        overflow: hidden; /* 【移除】移动端不需要上下滚动条 */
    }

    /* 【新增】移动端横向滑块优化 */
    .ss-horizontal-scrollbar {
        height: 12px; /* 移动端更高的滑块 */
        opacity: 0.8; /* 移动端默认显示 */
        pointer-events: auto;
        bottom: 5px;
    }

    .ss-horizontal-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }

    .ss-horizontal-scrollbar-thumb {
        background-color: rgba(34, 113, 177, 0.7);
        border-radius: 6px;
        min-width: 40px; /* 移动端更大的最小宽度 */
    }

    .ss-template-slider-wrapper {
        padding-bottom: 50px; /* 【优化】确保提示文字完整显示 */
        -webkit-overflow-scrolling: touch;
    }

    .ss-template-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
        margin-top: 15px;
    }

    .ss-template-item {
        border-radius: 10px;
        box-shadow: 0 3px 12px rgba(0,0,0,0.1);
        border-width: 1.5px;
    }

    .ss-template-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 18px rgba(0,0,0,0.15);
    }

    .ss-template-item:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    .ss-template-image {
        border-radius: 8px 8px 0 0;
    }

    .ss-template-title {
        padding: 10px 8px;
        font-size: 13px;
        min-height: 45px;
        line-height: 1.2;
    }
}

/* 【新增】小屏幕手机优化 (iPhone SE等) */
@media screen and (max-width: 480px) {
    .ss-modal-content {
        /* 【修复】与SVG预览容器宽度保持一致，确保不超出屏幕边界 */
        width: calc(100% - 20px); /* 左右各留10px边距 */
        max-width: 100%; /* 与小手机端SVG预览容器一致 */
        max-height: calc(100vh - 15px); /* 【优化】增加显示高度 */
        top: 5px; /* 【优化】减少顶部距离 */
        border-radius: 8px;
        /* 【修复】确保水平居中 */
        left: 50%;
        transform: translateX(-50%);
    }

    .ss-modal-header {
        padding: 12px 15px 8px;
    }

    .ss-modal-header h3 {
        font-size: 18px;
    }

    .ss-modal-close {
        font-size: 24px;
        min-width: 36px;
        min-height: 36px;
    }

    .ss-modal-body {
        padding: 12px 15px 15px;
        max-height: calc(100vh - 80px); /* 【优化】增加显示高度，减少顶部预留空间 */
        overflow: hidden; /* 【移除】小屏手机不需要上下滚动条 */
    }

    /* 【新增】小屏手机横向滑块优化 */
    .ss-horizontal-scrollbar {
        height: 10px; /* 小屏手机适中的滑块高度 */
        bottom: 3px;
    }

    .ss-template-slider-wrapper {
        padding-bottom: 55px; /* 【优化】确保提示文字完整显示 */
    }

    .ss-template-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        margin-top: 12px;
    }

    .ss-template-item {
        border-radius: 8px;
        border-width: 1px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .ss-template-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    }

    .ss-template-image {
        border-radius: 6px 6px 0 0;
    }

    .ss-template-title {
        padding: 8px 6px;
        font-size: 12px;
        min-height: 40px;
        line-height: 1.1;
    }
}

/* 【新增】模板项生成状态样式 */
.ss-template-generation-status {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    pointer-events: none;
}

.ss-template-status-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.65); /* 【优化】调整透明度为65% */
    backdrop-filter: blur(8px); /* 【修复】增强模糊效果 */
    -webkit-backdrop-filter: blur(8px); /* 【修复】Safari兼容性 */
    display: flex !important; /* 【Safari修复】强制显示 */
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    animation: fadeInOverlay 0.3s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2); /* 【修复】添加边框增强毛玻璃效果 */
    opacity: 1; /* 【Safari修复】确保可见 */
    z-index: 10; /* 【Safari修复】确保层级 */
}

.ss-template-status-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    max-width: 80%;
}

.ss-template-status-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(52, 152, 219, 0.2);
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.ss-template-status-message {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 生成中的模板项样式 */
.ss-template-item.generating {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    transform: scale(1.02);
}

.ss-template-item.generating .ss-template-image {
    filter: brightness(0.7);
}

/* 动画效果 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInOverlay {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .ss-template-status-content {
        padding: 15px;
    }

    .ss-template-status-spinner {
        width: 28px;
        height: 28px;
        border-width: 2.5px;
        margin-bottom: 10px;
    }

    .ss-template-status-message {
        font-size: 13px;
    }
}

/* 模态框显示动画 */
.ss-modal.show {
    display: block; /* 【优化】改为block布局，因为内容使用absolute定位 */
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 【新增】移动端触摸优化 */
@media screen and (max-width: 768px) {
    .ss-modal {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
        /* 【修复】确保模态框背景不会导致水平滚动 */
        overflow-x: hidden;
    }

    .ss-modal-content {
        -webkit-transform: translateZ(0); /* 启用硬件加速 */
        transform: translateZ(0);
        will-change: transform;
        /* 【修复】防止内容超出容器 */
        box-sizing: border-box;
        overflow-x: hidden;
    }

    .ss-template-item {
        -webkit-tap-highlight-color: rgba(34, 113, 177, 0.2);
        tap-highlight-color: rgba(34, 113, 177, 0.2);
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    .ss-template-item:active {
        background-color: #f8f9fa;
    }

    /* 【移除】原生滚动条样式，使用自定义滑块 */
}

/* 选中状态样式 */
.ss-template-item.selected {
    border-color: #2271b1;
    box-shadow: 0 0 0 2px #2271b1;
}

/* 类目图片容器样式 - 现代简约风格 */
.ss-category-image-container {
    width: 100%;
    max-width: 800px; /* 限制最大宽度，避免过宽 */
    margin: 0 auto 40px; /* 居中显示 */
    position: relative;
    overflow: hidden;
    border-radius: 16px; /* 更现代的圆角 */
    background: #ffffff;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08); /* 更柔和的阴影 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 更流畅的过渡 */
}

.ss-category-image-container:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); /* 悬停时增强阴影 */
    transform: translateY(-2px); /* 轻微上浮效果 */
}

.ss-category-image {
    width: 100%;
    height: 240px; /* 固定高度，确保一致性 */
    object-fit: contain; /* 完整显示图片，不裁剪 */
    object-position: center; /* 居中对齐 */
    display: block;
    background: #fafafa; /* 浅灰背景，更好的视觉效果 */
    padding: 20px; /* 内边距，给图片留白 */
    box-sizing: border-box;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ss-category-image:hover {
    transform: scale(1.03); /* 轻微缩放效果 */
}

/* 桌面端优化 (1200px+) */
@media (min-width: 1200px) {
    .ss-category-image-container {
        max-width: 1080px;
        margin-bottom: 50px;
        border-radius: 20px;
    }

    .ss-category-image {
        height: 280px;
        padding: 30px;
    }
}

/* 大桌面端优化 (1024px - 1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
    .ss-category-image-container {
        max-width: 800px;
        margin-bottom: 40px;
    }

    .ss-category-image {
        height: 260px;
        padding: 25px;
    }
}

/* 平板端优化 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .ss-category-image-container {
        max-width: 700px;
        margin-bottom: 35px;
        border-radius: 14px;
    }

    .ss-category-image {
        height: 220px;
        padding: 20px;
    }
}

/* 手机端优化 (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
    .ss-category-image-container {
        max-width: 100%;
        margin: 0 15px 30px;
        border-radius: 12px;
    }

    .ss-category-image {
        height: 180px;
        padding: 15px;
    }
}

/* 小手机端优化 (最大479px) */
@media (max-width: 479px) {
    .ss-category-image-container {
        max-width: 100%;
        margin: 0 10px 25px;
        border-radius: 10px;
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
    }

    .ss-category-image {
        height: 160px;
        padding: 12px;
    }

    .ss-category-image-container:hover {
        transform: none; /* 移动端禁用上浮效果 */
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
    }

    .ss-category-image:hover {
        transform: none; /* 移动端禁用缩放效果 */
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .ss-category-image-container {
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .ss-category-image-container {
        background: #1a1a1a;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
    }

    .ss-category-image {
        background: #2a2a2a;
    }

    .ss-category-image-container:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    }
}

/* 减少动画的用户偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .ss-category-image-container,
    .ss-category-image {
        transition: none;
    }

    .ss-category-image-container:hover {
        transform: none;
    }

    .ss-category-image:hover {
        transform: none;
    }
}

/* 【新增】模板悬浮窗与SVG预览容器完全一致的响应式断点 */

/* 桌面端优化 (1200px+) - 与SVG预览容器保持一致 */
@media (min-width: 1200px) {
    .ss-modal-content {
        max-width: 1080px; /* 与SVG预览容器一致 */
    }
}

/* 大桌面端优化 (1024px - 1199px) - 与SVG预览容器保持一致 */
@media (min-width: 1024px) and (max-width: 1199px) {
    .ss-modal-content {
        max-width: 800px; /* 与SVG预览容器一致 */
    }
}

/* 平板端优化 (768px - 1023px) - 与SVG预览容器保持一致 */
@media (min-width: 768px) and (max-width: 1023px) {
    .ss-modal-content {
        max-width: 700px; /* 与SVG预览容器一致 */
        width: calc(100% - 40px); /* 左右各留20px边距 */
    }
}

/* 手机端优化 (480px - 767px) - 与SVG预览容器保持一致 */
@media (min-width: 480px) and (max-width: 767px) {
    .ss-modal-content {
        max-width: 100%; /* 与SVG预览容器一致 */
        width: calc(100% - 30px); /* 与SVG预览容器边距一致 */
        margin: 0 auto;
    }
}

/* 小手机端优化 (最大479px) - 与SVG预览容器保持一致 */
@media (max-width: 479px) {
    .ss-modal-content {
        max-width: 100%; /* 与SVG预览容器一致 */
        width: calc(100% - 20px); /* 与SVG预览容器边距一致 */
        margin: 0 auto;
    }
}

