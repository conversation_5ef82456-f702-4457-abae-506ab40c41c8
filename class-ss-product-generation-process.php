<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// 引入 WP_Background_Process 类
if ( ! class_exists( 'WP_Background_Process' ) ) {
    require_once plugin_dir_path( __FILE__ ) . 'includes/wp-background-processing/wp-background-processing.php';
}

class SS_Product_Generation_Process extends WP_Background_Process {
    protected $action = 'ss_product_generation_process';

    // 将一次处理的最大批次数量改大（默认是 10 或更小）
    // 可以根据服务器CPU/内存情况调到 20、30、50 等
    protected $batch_size = 50;

    // 新增：用于跟踪进度的属性
    protected $total_items = 0;
    protected $completed_items = 0;

    public function __construct() {
        parent::__construct();

        // ---------------------------
        // ① 提高PHP脚本可用资源
        // ---------------------------
        // 在此强制调大内存和脚本执行时间
        if ( function_exists( 'ini_set' ) ) {
            @ini_set( 'memory_limit', '8192M' );  // 可视您服务器上限做调整
            @ini_set( 'max_execution_time', '1200' ); // PHP执行时间(秒)
            @set_time_limit( 1200 );                // 同上
        }

        // 获取队列中的总任务数
        $queue = $this->get_batch();
        $generation_log = get_option( 'ss_generation_log', array() );
        if ( isset( $queue->data ) && is_array( $queue->data ) ) {
            $this->total_items = count( $queue->data );
            $generation_log['total_items'] = $this->total_items;
            $generation_log['is_running'] = true;
            update_option( 'ss_generation_log', $generation_log );
        }
    }

    protected function task( $category_id ) {
        $task_start_time = microtime(true);
        // error_log('[PARALLEL DEBUG] 开始处理类目任务，ID: ' . $category_id);

        // 如果已暂停或已停止，则让该任务再次排队，不继续执行
        $generation_log = get_option( 'ss_generation_log', array() );
        if ( empty( $generation_log['is_running'] ) || ! $generation_log['is_running'] ) {
            // error_log('[PARALLEL DEBUG] 生成过程已暂停或停止，将任务放回队列');
            return $category_id; // 将当前任务重新放回队列，保持暂停
        }

        // 关键修改：使用process_category方法来处理类目，如果返回true表示跳过
        if ($this->process_category($category_id)) {
            // 如果process_category返回true，表示已处理或应该跳过

            // 更新已完成的任务数量
            $this->completed_items++;

            // 更新日志，标记为已跳过（从前端预览）
            $term = get_term($category_id, 'product_cat');
            $term_name = is_wp_error($term) ? "未知类目(ID: $category_id)" : $term->name;

            update_option('ss_generation_log', array(
                'total_items'     => $this->total_items,
                'completed_items' => $this->completed_items,
                'is_running'      => true,
                'current_item_name' => $term_name,
                'skipped'         => true, // 标记为已跳过
                'skip_reason'     => '已在前端生成预览图',
            ));

            // error_log('[PARALLEL DEBUG] 已跳过类目 "' . $term_name . '" (ID: ' . $category_id . ') 的产品生成，因为已在前端生成过预览图');

            $task_end_time = microtime(true);
            $task_duration = round($task_end_time - $task_start_time, 2);
            // error_log('[PARALLEL DEBUG] 类目任务处理完成（跳过），耗时: ' . $task_duration . '秒');

            return false; // 表示此项目已处理完成
        }

        // 以下是原有的产品生成逻辑
        // 获取插件实例
        $generator = SS_SVG_Generator::get_instance();

        // 获取分类对象
        $term = get_term( $category_id, 'product_cat' );
        if ( $term && ! is_wp_error( $term ) ) {
            // error_log('[PARALLEL DEBUG] 开始为类目 "' . $term->name . '" 生成产品');

            // 更新日志：正在处理的分类名称
            update_option( 'ss_generation_log', array(
                'total_items'      => $this->total_items,
                'completed_items'  => $this->completed_items,
                'is_running'       => true,
                'current_item_name'=> $term->name,
            ) );

            // 记录开始时间
            $generation_start_time = microtime(true);

            // 生成该分类的产品（核心逻辑）
            $result = $generator->ss_generate_products_for_category( $term );

            // 记录结束时间和耗时
            $generation_end_time = microtime(true);
            $generation_duration = round($generation_end_time - $generation_start_time, 2);

            // 记录生成结果
            if (is_array($result)) {
                // error_log('[PARALLEL DEBUG] 类目 "' . $term->name . '" 产品生成完成，共生成 ' . count($result) . ' 个产品，耗时: ' . $generation_duration . '秒');
            } else {
                // error_log('[PARALLEL DEBUG] 类目 "' . $term->name . '" 产品生成失败或无结果，耗时: ' . $generation_duration . '秒');
            }

            // 标记该类目已通过后台生成产品
            update_term_meta($category_id, 'ss_backend_products_generated', time());
            // error_log('[PARALLEL DEBUG] 已标记类目 "' . $term->name . '" (ID:' . $category_id . ') 为后台生成产品');

            // 【新增】单个类目产品生成完成后，清理该类目的Gallery临时文件
            if (class_exists('SS_SVG_Generator')) {
                $generator = SS_SVG_Generator::get_instance();
                $cleanup_result = $generator->clean_gallery_temp_files($term->name);
                if ($cleanup_result['directories_cleaned'] > 0 || $cleanup_result['files_cleaned'] > 0) {
                    error_log("[类目生成完成] 类目 '{$term->name}' Gallery清理: 清理了 {$cleanup_result['directories_cleaned']} 个文件夹，{$cleanup_result['files_cleaned']} 个文件");
                }
            }

            // 更新已完成的任务数量
            $this->completed_items++;

            // 更新日志
            update_option( 'ss_generation_log', array(
                'total_items'      => $this->total_items,
                'completed_items'  => $this->completed_items,
                'is_running'       => true,
                'current_item_name'=> $term->name,
            ) );
        } else {
            // error_log('[PARALLEL DEBUG] 无法获取类目信息，ID: ' . $category_id);
        }

        $task_end_time = microtime(true);
        $task_duration = round($task_end_time - $task_start_time, 2);
        // error_log('[PARALLEL DEBUG] 类目任务处理完成，总耗时: ' . $task_duration . '秒');

        // 返回 false，表示当前项目已完成处理
        return false;
    }

    protected function complete() {
        parent::complete();

        // 更新日志，标记为已完成
        $generation_log = get_option( 'ss_generation_log', array() );
        $generation_log['is_running'] = false;
        update_option( 'ss_generation_log', $generation_log );

        // 【新增】产品生成完成后，清理所有Gallery临时文件
        if (class_exists('SS_SVG_Generator')) {
            $generator = SS_SVG_Generator::get_instance();
            $cleanup_result = $generator->clean_gallery_temp_files();
            error_log("[产品生成完成] Gallery清理结果: 清理了 {$cleanup_result['directories_cleaned']} 个文件夹，{$cleanup_result['files_cleaned']} 个文件");
        }

        // 处理完成后，清除日志
        delete_option( 'ss_generation_log' );
    }

    /**
     * 暂停后台进程
     */
    public function pause_process() {
        // 将 is_running 设置为 false
        $generation_log = get_option( 'ss_generation_log', array() );
        $generation_log['is_running'] = false;
        update_option( 'ss_generation_log', $generation_log );

        // 清除下次调度事件，防止继续自动触发
        $this->clear_scheduled_event();

        // 解锁进程，保证当前批次执行完后不会再继续
        $this->unlock_process();
    }

    /**
     * 继续后台进程
     */
    public function resume_process() {
        // 将 is_running 设置为 true
        $generation_log = get_option( 'ss_generation_log', array() );
        $generation_log['is_running'] = true;
        update_option( 'ss_generation_log', $generation_log );

        // 重新调度后台进程
        $this->dispatch();
    }

    /**
     * 取消后台进程
     */
    public function cancel_process() {
        // 清除下次调度事件
        $this->clear_scheduled_event();

        // 删除队列中的所有任务
        $this->delete_queue();

        // 解锁进程
        $this->unlock_process();

        // 将 is_running 设置为 false
        $generation_log = get_option( 'ss_generation_log', array() );
        $generation_log['is_running'] = false;
        update_option( 'ss_generation_log', $generation_log );
    }

    /**
     * 处理单个分类的产品生成，返回true表示跳过，返回false表示需要继续处理
     */
    protected function process_category($category_id) {
        // 添加详细日志
        $category = get_term($category_id, 'product_cat');
        if (!$category || is_wp_error($category)) {
            // error_log('[PARALLEL DEBUG] 无法获取类目信息: ' . $category_id);
            return false;
        }

        // error_log('[PARALLEL DEBUG] 正在检查类目 "' . $category->name . '" (ID: ' . $category_id . ') 的处理条件');

        // 1. 检查是否为底层类目
        $child_terms = get_terms(array(
            'taxonomy' => 'product_cat',
            'parent' => $category_id,
            'hide_empty' => false,
            'fields' => 'ids',
            'number' => 1
        ));

        if (!empty($child_terms) && !is_wp_error($child_terms)) {
            // error_log('[PARALLEL DEBUG] 类目 "' . $category->name . '" 不是底层类目，跳过处理');
            return true; // 跳过非底层类目
        }

        // 2. 检查是否包含产品模板
        $template_products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => 1,
            'post_status' => 'publish',
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id,
                ),
            ),
            'meta_query' => array(
                array(
                    'key' => '_ss_is_template',
                    'value' => '1',
                ),
            ),
            'fields' => 'ids',
        ));

        if (empty($template_products)) {
            // error_log('[PARALLEL DEBUG] 类目 "' . $category->name . '" 没有产品模板，跳过处理');
            return true; // 跳过没有产品模板的类目
        } else {
            // error_log('[PARALLEL DEBUG] 类目 "' . $category->name . '" 找到产品模板: ' . implode(', ', $template_products));
        }

        // 3. 检查是否已在前端生成过预览图
        $preview_generated = get_term_meta($category_id, 'ss_frontend_preview_generated', true);
        $processed_designs = get_term_meta($category_id, 'ss_processed_designs', true);

        if (!empty($preview_generated) && is_array($processed_designs) && !empty($processed_designs)) {
            // error_log('[PARALLEL DEBUG] 类目 "' . $category->name . '" 已在前端生成过预览图，跳过处理');
            // error_log('[PARALLEL DEBUG] 已处理的设计数量: ' . count($processed_designs));
            return true; // 跳过已在前端生成过预览图的类目
        }

        // 所有条件都满足，需要执行后台产品生成
       // error_log('[PARALLEL DEBUG] 类目 "' . $category->name . '" 满足所有条件，将执行后台产品生成');
        return false; // 返回false表示需要继续处理此类目
    }
}
