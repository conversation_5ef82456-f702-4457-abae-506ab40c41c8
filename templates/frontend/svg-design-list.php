<?php
/**
 * SVG Design Selection Template
 * 
 * Displays SVG design list for user selection
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current category
$term = get_queried_object();

// Get category thumbnail
$thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);
$category_image_url = $thumbnail_id ? wp_get_attachment_image_url($thumbnail_id, 'full') : '';

// Ensure we have design data
global $svg_designs;
if (empty($svg_designs)) {
    $svg_designs = array();
}

get_header();
?>

<!-- 【极速优化】立即执行的页面加载优化脚本 -->
<script type="text/javascript">
// 立即执行，不等待DOM加载，提升感知性能
(function() {
    window.ss_page_load_start = performance.now();

    // 【极速优化】立即添加CSS确保关键元素可见，并优化视觉效果
    var style = document.createElement('style');
    style.textContent = `
        .ss-svg-preview-container, .ss-loading-progress {
            opacity: 1 !important;
            visibility: visible !important;
        }
        .skeleton-loader {
            animation: skeleton-loading 1.2s ease-in-out infinite;
        }
        .ss-svg-preview-grid {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
        .ss-design-selection-container {
            opacity: 1 !important;
        }
        /* 【新增】立即显示页面结构，减少布局抖动 */
        .ss-svg-preview-item {
            min-height: 200px;
            opacity: 1 !important;
        }
    `;
    document.head.appendChild(style);

    // 【极速优化】立即显示页面标题和描述
    document.addEventListener('DOMContentLoaded', function() {
        var container = document.querySelector('.ss-design-selection-container');
        if (container) {
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }
    });

    console.log('SVG预览页面极速优化完成，耗时:', performance.now() - window.ss_page_load_start, 'ms');
})();
</script>

<!-- 加载实时进度推送模块 -->
<script src="<?php echo plugin_dir_url(__FILE__) . '../../js/ss-realtime-progress.js'; ?>"></script>

<div class="ss-design-selection-container">
    <div class="ss-inner-container">
        <?php if ($category_image_url): ?>
        <div class="ss-category-image-container">
            <img src="<?php echo esc_url($category_image_url); ?>" alt="<?php echo esc_attr($term->name); ?>" class="ss-category-image">
        </div>
        <?php endif; ?>
        
        
        <p class="ss-page-description">
            Browse the designs below. Click on a design you like to continue.
        </p>
        
        <!-- 进度条将在页面底部悬浮显示 -->
        
        <?php if (empty($svg_designs)): ?>
            <div class="ss-no-designs-message">
                No designs available. Please try again later.
            </div>
        <?php else: ?>
            <div class="ss-svg-preview-grid">
                <?php 
                $batch_size = 8; // 一次显示8个项目
                $total_designs = count($svg_designs);

                // 【修复】顺序显示优化：已生成预览图优先显示，移除随机逻辑提高缓存利用率
                $frontend_manager = SS_Frontend_Manager::get_instance();
                $category_name = $frontend_manager->get_safe_category_name($term->name);

                // 检查哪些SVG已经有预览图
                $upload_dir = wp_upload_dir();
                $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_name . '/previews/';

                $generated_indexes = array(); // 已生成预览图的索引
                $pending_indexes = array();   // 未生成预览图的索引

                foreach ($svg_designs as $index => $design) {
                    $safe_name = pathinfo($design['file'], PATHINFO_FILENAME);
                    $preview_path = $preview_dir . $safe_name . '_preview.webp';

                    if (file_exists($preview_path)) {
                        $generated_indexes[] = $index;
                    } else {
                        $pending_indexes[] = $index;
                    }
                }

                // 【修复】移除随机排序，保持原始顺序以提高缓存利用率
                // 已生成的预览图按原始顺序排列
                sort($generated_indexes);
                // 未生成的预览图也按原始顺序排列
                sort($pending_indexes);

                // 【关键】创建优化的显示顺序：已生成的在前，未生成的在后
                $display_order = array();
                $display_index = 0;

                // 先添加已生成的预览图
                foreach ($generated_indexes as $original_index) {
                    $display_order[] = array(
                        'index' => $original_index,
                        'is_first_batch' => $display_index < $batch_size,
                        'display_index' => $display_index,
                        'has_preview' => true
                    );
                    $display_index++;
                }

                // 再添加未生成的预览图
                foreach ($pending_indexes as $original_index) {
                    $display_order[] = array(
                        'index' => $original_index,
                        'is_first_batch' => $display_index < $batch_size,
                        'display_index' => $display_index,
                        'has_preview' => false
                    );
                    $display_index++;
                }

                // 记录排序结果用于调试
                $generated_count = count($generated_indexes);
                $pending_count = count($pending_indexes);
                $first_batch_cached = min($generated_count, $batch_size);
                error_log("SVG顺序显示优化: 已生成{$generated_count}个, 待生成{$pending_count}个, 前{$batch_size}张中已缓存{$first_batch_cached}个 (缓存利用率: " . round(($first_batch_cached / $batch_size) * 100, 1) . "%)");
                
                foreach ($display_order as $item):
                    $index = $item['index'];
                    $display_index = $item['display_index'];
                    $design = $svg_designs[$index];
                    $display_style = $display_index >= $batch_size ? 'style="display:none;"' : '';
                    $priority = $item['is_first_batch'] ? 'high' : 'normal';
                    $has_preview = $item['has_preview'] ? 'true' : 'false';
                ?>
                    <div class="ss-svg-preview-item"
                         data-file="<?php echo esc_attr($design['file']); ?>"
                         data-index="<?php echo esc_attr($index); ?>"
                         data-display-index="<?php echo esc_attr($display_index); ?>"
                         data-priority="<?php echo esc_attr($priority); ?>"
                         data-has-preview="<?php echo esc_attr($has_preview); ?>"
                         <?php echo $display_style; ?>>
                        <div class="ss-svg-preview-image skeleton-loader">
                            <div class="skeleton-placeholder"></div>
                        </div>
                        <div class="ss-svg-preview-name"><?php echo esc_html($design['name']); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="ss-load-more-container">
                <button class="ss-load-more-btn" <?php echo count($svg_designs) <= $batch_size ? 'style="display:none;"' : ''; ?>>Load More</button>
            </div>

            <!-- 添加模板选择弹窗 -->
            <div id="ss-template-modal" class="ss-modal">
                <div class="ss-modal-content">
                    <div class="ss-modal-header">
                        <h3>Select Product Template</h3>
                        <span class="ss-modal-close">&times;</span>
                    </div>
                    <div class="ss-modal-body">
                        <!-- 调试信息区域 -->
                        <div class="ss-debug-info" style="display: none;">
                            <pre class="ss-debug-output"></pre>
                        </div>
                        <!-- 加载指示器 -->
                        <div class="ss-modal-loading">
                            <div class="ss-loading-spinner"></div>
                            <p>Loading templates...</p>
                        </div>
                        <!-- 【新增】横向滑动容器 -->
                        <div class="ss-template-slider-container">
                            <div class="ss-template-slider-wrapper">
                                <div class="ss-template-grid"></div>
                            </div>
                            <!-- 【新增】横向滑块指示器 -->
                            <div class="ss-horizontal-scrollbar">
                                <div class="ss-horizontal-scrollbar-track">
                                    <div class="ss-horizontal-scrollbar-thumb"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 错误信息显示 -->
                        <div class="ss-modal-error" style="display: none;">
                            <p class="ss-error-message"></p>
                            <button class="ss-retry-btn">Retry</button>
                        </div>
                    </div>
                    <div class="ss-modal-footer">
                        <div class="ss-generation-status" style="display: none;">
                            <div class="ss-status-spinner"></div>
                            <p class="ss-status-message"></p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 添加悬浮进度条 -->
<div class="ss-loading-progress">
    <div class="ss-progress-bar"></div>
    <div class="ss-progress-text">0%</div>
</div>

<script type="text/javascript">
// 【优化】立即显示页面框架，不等待jQuery ready
(function() {
    // 立即记录页面开始时间
    window.ss_page_start_time = performance.now();

    // 立即显示骨架屏，提升感知性能
    var style = document.createElement('style');
    style.textContent = `
        .ss-svg-preview-container { opacity: 1 !important; }
        .ss-loading-progress { opacity: 1 !important; }
        .skeleton-loader { animation: skeleton-loading 1.5s ease-in-out infinite; }
    `;
    document.head.appendChild(style);

    console.log('SVG预览页面框架立即显示完成，耗时:', performance.now() - window.ss_page_start_time, 'ms');
})();

jQuery(document).ready(function($) {
    console.log('SVG design list initialized, 总耗时:', performance.now() - window.ss_page_start_time, 'ms');

    // 准备分类数据用于状态检查
    window.ss_category_data = {
        term_id: <?php echo esc_js($term->term_id); ?>,
        term_name: '<?php echo esc_js($term->name); ?>'
    };
    
    // 【优化】分批加载状态管理
    var progressCompleted = false;
    var lastCompletedCount = 0;
    var lastTotalCount = 0;
    var batchSize = 8; // 一次显示8个项目
    var isFirstVisit = true; // 默认为首次访问
    var cachedPreviewsLoaded = false; // 【新增】缓存预览图加载状态

    // 【性能优化】全局缓存存储，避免Load more时重复AJAX请求
    window.ss_all_cached_previews = null; // 存储所有缓存预览图数据
    window.ss_cache_load_time = 0; // 记录缓存加载时间
    window.ss_performance_stats = { // 性能统计
        localCacheHits: 0,
        ajaxFallbacks: 0,
        avgLocalCacheTime: 0,
        avgAjaxTime: 0
    };

    // 【新增】检查缓存的预览图 - 返回Promise版本，支持特定项目检查
    function checkCachedPreviews(specificItems = null) {
        console.log('检查缓存的预览图...', specificItems ? '(特定项目: ' + specificItems.length + '个)' : '(全部项目)');

        return new Promise(function(resolve, reject) {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'ss_get_cached_previews',
                    nonce: '<?php echo wp_create_nonce('ss_frontend_nonce'); ?>',
                    term_id: window.ss_category_data.term_id
                },
                timeout: 3000, // 【极速优化】减少超时时间，提升响应速度
                success: function(response) {
                    if (response.success && response.data && response.data.cached_previews && response.data.cached_previews.length > 0) {
                        console.log('发现缓存预览图:', response.data.cached_previews.length, '个');

                        // 【性能优化】在全量检查时保存所有缓存数据到全局变量，避免Load more时重复AJAX
                        if (!specificItems) {
                            window.ss_all_cached_previews = response.data.cached_previews;
                            window.ss_cache_load_time = Date.now();
                            console.log('【性能优化】已将', response.data.cached_previews.length, '个缓存预览图保存到全局变量');
                        }

                        // 【修复】如果指定了特定项目，只处理这些项目的缓存
                        var previewsToLoad = response.data.cached_previews;
                        if (specificItems && specificItems.length > 0) {
                            var specificIndexes = specificItems.map(function(item) {
                                return parseInt($(item).data('index'));
                            });
                            previewsToLoad = response.data.cached_previews.filter(function(preview) {
                                return specificIndexes.includes(preview.index);
                            });
                            console.log('过滤后的特定项目缓存预览图:', previewsToLoad.length, '个，索引:', specificIndexes);
                        }

                        // 立即显示缓存的预览图
                        if (previewsToLoad.length > 0) {
                            loadCachedPreviews(previewsToLoad);
                            if (!specificItems) {
                                cachedPreviewsLoaded = true;
                            }
                        }

                        // 更新进度条（仅在全量检查时）
                        if (!specificItems) {
                            var totalItems = $('.ss-svg-preview-item').length;
                            var cachedCount = response.data.cached_previews.length;
                            var progressPercent = Math.round((cachedCount / totalItems) * 100);

                            $('.ss-progress-bar').css('width', progressPercent + '%');
                            $('.ss-progress-text').text(cachedCount + '/' + totalItems); // 【修复】始终显示数量格式

                            // 【优化】基于顺序显示的缓存利用率判断
                            var firstBatchCached = 0;
                            $('.ss-svg-preview-item').slice(0, 8).each(function() {
                                if ($(this).find('.ss-svg-preview-image').hasClass('cached-preview')) {
                                    firstBatchCached++;
                                }
                            });

                            // 【修复】如果前8张都有缓存，或者总缓存覆盖率足够高，标记为基本完成
                            if (firstBatchCached >= 8 || cachedCount >= totalItems * 0.8) {
                                progressCompleted = true;
                                console.log('缓存预览图覆盖率足够，标记为完成 (前8张缓存:', firstBatchCached, '/8, 总覆盖率:', Math.round((cachedCount/totalItems)*100), '%)');
                            }
                        }

                        resolve({
                            cached: true,
                            count: previewsToLoad.length,
                            total: specificItems ? specificItems.length : $('.ss-svg-preview-item').length,
                            ratio: specificItems ? (previewsToLoad.length / specificItems.length) : (response.data.cached_previews.length / $('.ss-svg-preview-item').length)
                        });
                    } else {
                        console.log('未发现足够的缓存预览图，继续正常加载流程');
                        resolve({
                            cached: false,
                            count: 0,
                            total: specificItems ? specificItems.length : $('.ss-svg-preview-item').length,
                            ratio: 0
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.log('缓存检查失败，继续正常加载流程:', error);
                    reject(error);
                }
            });
        });
    }

    // 【性能优化】快速从本地缓存加载特定项目的预览图，避免AJAX请求
    function loadCachedPreviewsFromLocalCache(specificItems) {
        console.log('【性能优化】尝试从本地缓存加载', specificItems.length, '个项目的预览图');

        // 检查全局缓存是否可用
        if (!window.ss_all_cached_previews || !Array.isArray(window.ss_all_cached_previews)) {
            console.log('【性能优化】本地缓存不可用，fallback到AJAX方式');
            return null;
        }

        // 检查缓存是否过期（5分钟）
        var cacheAge = Date.now() - window.ss_cache_load_time;
        if (cacheAge > 300000) { // 5分钟
            console.log('【性能优化】本地缓存已过期，fallback到AJAX方式');
            return null;
        }

        var startTime = performance.now();

        // 获取特定项目的索引
        var specificIndexes = specificItems.map(function(item) {
            return parseInt($(item).data('index'));
        });

        // 从全局缓存中过滤出特定项目的预览图
        var previewsToLoad = window.ss_all_cached_previews.filter(function(preview) {
            return specificIndexes.includes(preview.index);
        });

        var filterTime = performance.now() - startTime;
        console.log('【性能优化】本地缓存过滤完成，耗时:', filterTime.toFixed(2), 'ms，找到', previewsToLoad.length, '个预览图');

        if (previewsToLoad.length > 0) {
            // 立即加载预览图
            loadCachedPreviews(previewsToLoad);

            return {
                cached: true,
                count: previewsToLoad.length,
                total: specificItems.length,
                ratio: previewsToLoad.length / specificItems.length,
                loadTime: filterTime
            };
        }

        return {
            cached: false,
            count: 0,
            total: specificItems.length,
            ratio: 0,
            loadTime: filterTime
        };
    }

    // 【优化】加载缓存的预览图 - 按display-index顺序优先加载前8张
    function loadCachedPreviews(cachedPreviews) {
        // 【优化】按display-index排序，优先处理前8张显示的预览图
        var sortedPreviews = cachedPreviews.slice().sort(function(a, b) {
            var aDisplayIndex = parseInt($('.ss-svg-preview-item[data-index="' + a.index + '"]').data('display-index')) || 999;
            var bDisplayIndex = parseInt($('.ss-svg-preview-item[data-index="' + b.index + '"]').data('display-index')) || 999;
            return aDisplayIndex - bDisplayIndex;
        });

        console.log('[缓存加载] 按display-index顺序加载', sortedPreviews.length, '个缓存预览图');

        sortedPreviews.forEach(function(preview) {
            var $item = $('.ss-svg-preview-item[data-index="' + preview.index + '"]');
            var $image = $item.find('.ss-svg-preview-image');
            var displayIndex = parseInt($item.data('display-index')) || 999;
            
            if (!$image.hasClass('loaded')) {
                // 创建图片元素
                var img = new Image();
                
                img.onload = function() {
                    // 移除骨架加载器
                    $image.removeClass('skeleton-loader')
                        .addClass('loaded cached-preview')
                        .find('.skeleton-placeholder').remove();

                    // 插入图片并添加淡入效果
                    $image.append('<img src="' + preview.preview_url + '" alt="' + preview.file + '" class="fade-in">');

                    console.log('缓存预览图加载完成: display-index=' + displayIndex + ', original-index=' + preview.index + ', file=' + preview.file + ', 优先级=' + (displayIndex < 8 ? '高' : '低'));

                    // 【新增】立即更新进度条
                    setTimeout(function() {
                        updateProgress(); // 触发进度条重新计算
                        countdownManager.checkAndStop();
                    }, 100);
                };
                
                img.onerror = function() {
                    console.log('缓存预览图加载失败: index=' + preview.index + ', file=' + preview.file);
                    // 加载失败时移除缓存标记，让正常流程处理
                    $image.removeClass('cached-preview');
                };
                
                img.src = preview.preview_url;
            }
        });
    }

    // 分批加载状态
    var loadingStage = 1; // 当前加载阶段（1=前8张，2=第二批8张，等等）
    var currentBatchCompleted = false; // 当前批次是否完成

    // 【新增】累计进度和倒计时管理
    var totalSvgCount = <?php echo count($svg_designs); ?>; // SVG总数量
    var cumulativeCompleted = 0; // 累计完成数量
    var batchStartTime = null; // 当前批次开始时间
    var estimatedTimeRemaining = 0; // 预估剩余时间（秒）
    var countdownInterval = null; // 倒计时定时器
    
    // 【新增】智能倒计时管理器
    var countdownManager = {
        isActive: false,
        shouldShow: false,
        hasExpiredNaturally: false, // 【新增】标记倒计时是否自然结束

        // 【修复】检查是否应该显示倒计时 - 更精确的判断逻辑
        checkShouldShow: function() {
            // 如果进度已完成，不显示
            if (progressCompleted) {
                console.log('[倒计时管理器] 进度已完成，不显示倒计时');
                return false;
            }

            // 【修复】检查当前批次是否有需要生成的SVG
            var hasUnloadedItems = this.hasUnloadedItemsInCurrentBatch();
            if (!hasUnloadedItems) {
                console.log('[倒计时管理器] 当前批次没有需要生成的SVG，不显示倒计时');
                return false;
            }

            // 【修复】首次访问时总是显示倒计时
            if (isFirstVisit && loadingStage === 1) {
                console.log('[倒计时管理器] 首次访问第一批，显示倒计时');
                return true;
            }

            // 【修复】如果倒计时已自然结束，但当前批次有新增的未生成SVG，仍然显示倒计时
            if (this.hasExpiredNaturally) {
                if (loadingStage > 1 && hasUnloadedItems) {
                    console.log('[倒计时管理器] 虽然倒计时已过期，但检测到新批次有未生成SVG，重新显示倒计时');
                    this.resetExpiredStatus();
                } else {
                    console.log('[倒计时管理器] 倒计时已自然结束，不再显示新倒计时');
                    return false;
                }
            }

            // 【修复】检查当前批次的缓存覆盖率，而不是整体覆盖率
            if (cachedPreviewsLoaded) {
                var currentBatchItems = this.getCurrentBatchItems();
                var batchLoadedItems = 0;

                currentBatchItems.forEach(function($item) {
                    var $image = $item.find('.ss-svg-preview-image');
                    if ($image.hasClass('loaded') || $image.hasClass('cached-preview')) {
                        batchLoadedItems++;
                    }
                });

                var batchCacheRatio = currentBatchItems.length > 0 ? batchLoadedItems / currentBatchItems.length : 0;

                if (batchCacheRatio >= 0.8) {
                    console.log('[倒计时管理器] 当前批次缓存覆盖率足够高(' + Math.round(batchCacheRatio * 100) + '%)，不显示倒计时');
                    return false;
                }
            }

            return true;
        },

        // 启动倒计时
        start: function() {
            // 【修复】在启动新批次时，检查是否需要重置过期状态
            if (this.hasExpiredNaturally && loadingStage > 1) {
                console.log('[倒计时管理器] 检测到新批次开始，重置过期状态');
                this.resetExpiredStatus();
            }

            this.shouldShow = this.checkShouldShow();
            if (!this.shouldShow) {
                return;
            }

            if (this.isActive) {
                console.log('[倒计时管理器] 倒计时已激活，跳过重复启动');
                return;
            }

            this.isActive = true;
            startBatchCountdown();
            console.log('[倒计时管理器] 倒计时已启动');
        },

        // 停止倒计时
        stop: function() {
            if (this.isActive) {
                hideCountdownMessage();
                this.isActive = false;
                console.log('[倒计时管理器] 倒计时已停止');
            }
        },

        // 【新增】标记倒计时自然结束
        markExpired: function() {
            this.hasExpiredNaturally = true;
            this.isActive = false;
            console.log('[倒计时管理器] 倒计时自然结束，标记为已过期');
        },

        // 【新增】重置过期状态（用于Load More时）
        resetExpiredStatus: function() {
            this.hasExpiredNaturally = false;
            console.log('[倒计时管理器] 重置过期状态');
        },

        // 检查并自动停止倒计时
        checkAndStop: function() {
            if (!this.isActive) return;

            // 检查当前批次是否完成
            var currentBatchComplete = this.isCurrentBatchComplete();
            if (currentBatchComplete) {
                this.stop();
                console.log('[倒计时管理器] 检测到批次完成，自动停止倒计时');
            }
        },

        // 检查当前批次是否完成
        isCurrentBatchComplete: function() {
            var currentBatchItems = this.getCurrentBatchItems();
            if (currentBatchItems.length === 0) return false;

            var completedCount = 0;
            currentBatchItems.forEach(function($item) {
                var $image = $item.find('.ss-svg-preview-image');
                if ($image.hasClass('loaded') || $image.hasClass('cached-preview')) {
                    completedCount++;
                }
            });

            return completedCount >= currentBatchItems.length;
        },

        // 获取当前批次的项目
        getCurrentBatchItems: function() {
            var items = [];

            if (loadingStage === 1) {
                // 第一批：前8张显示的图片
                $('.ss-svg-preview-item:visible').each(function() {
                    var displayIndex = parseInt($(this).data('display-index'));
                    if (displayIndex < 8) {
                        items.push($(this));
                    }
                });
            } else {
                // 后续批次：当前阶段的可见项目
                var batchStart = (loadingStage - 1) * 8;
                var batchEnd = loadingStage * 8;

                $('.ss-svg-preview-item:visible').each(function() {
                    var displayIndex = parseInt($(this).data('display-index'));
                    if (displayIndex >= batchStart && displayIndex < batchEnd) {
                        items.push($(this));
                    }
                });
            }

            return items;
        },

        // 【新增】检查当前批次是否有未加载的项目
        hasUnloadedItemsInCurrentBatch: function() {
            var currentBatchItems = this.getCurrentBatchItems();

            for (var i = 0; i < currentBatchItems.length; i++) {
                var $item = currentBatchItems[i];
                var $image = $item.find('.ss-svg-preview-image');

                // 如果有未加载且非缓存的项目，说明需要显示倒计时
                if (!$image.hasClass('loaded') && !$image.hasClass('cached-preview')) {
                    return true;
                }
            }

            return false;
        }
    };

    // 【优化】初始化实时进度监听器
    var progressMonitor = null;
    var useRealtimeProgress = true; // 是否使用实时进度推送

    function initRealtimeProgress() {
        if (typeof window.SSProgressManager !== 'undefined' && useRealtimeProgress) {
            console.log('初始化实时进度监听器');

            progressMonitor = window.SSProgressManager.create(window.ss_category_data.term_id, {
                updateInterval: 1000,
                maxRetries: 3
            });

            // 绑定进度回调
            progressMonitor
                .on('progress', function(data) {
                    console.log('收到实时进度更新:', data);
                    updateProgressFromRealtime(data);
                })
                .on('complete', function(data) {
                    console.log('实时进度完成:', data);
                    handleRealtimeComplete(data);
                })
                .on('error', function(data) {
                    console.log('实时进度错误，回退到轮询:', data);
                    useRealtimeProgress = false;
                    startTraditionalFlow();
                })
                .on('connect', function() {
                    console.log('实时进度连接已建立');
                })
                .on('disconnect', function() {
                    console.log('实时进度连接已断开');
                });

            return true;
        }

        return false;
    }

    function updateProgressFromRealtime(data) {
        if (data.percentage !== undefined) {
            $('.ss-progress-bar').css('width', data.percentage + '%');
        }

        if (data.completed_files !== undefined && data.total_files !== undefined) {
            $('.ss-progress-text').text(data.completed_files + '/' + data.total_files);
        }

        // 如果有具体的文件完成信息，更新对应的预览图
        if (data.completed_files && data.completed_files > 0) {
            checkPreviewStatus(); // 检查并更新预览图
        }
    }

    function handleRealtimeComplete(data) {
        progressCompleted = true;
        countdownManager.stop();

        if (progressMonitor) {
            progressMonitor.stop();
        }

        // 最后检查一次确保所有预览图都已加载
        setTimeout(function() {
            checkPreviewStatus();
        }, 1000);

        console.log('实时进度处理完成，成功: ' + (data.success_count || 0) + ', 失败: ' + (data.error_count || 0));
    }

    function startTraditionalFlow() {
        console.log('启动传统轮询流程');

        setTimeout(function() {
            if (!cachedPreviewsLoaded) {
                console.log('未发现缓存，开始正常预览状态检查');
                countdownManager.start();
                checkPreviewStatus();
            } else {
                console.log('已加载缓存预览图，直接检查状态');
                checkPreviewStatus();
            }
        }, 500);
    }

    // 【新增】立即检查缓存的预览图，使用Promise确保时序
    var initialCacheStartTime = performance.now();
    checkCachedPreviews().then(function() {
        var initialCacheTime = performance.now() - initialCacheStartTime;
        console.log('【性能监控】初始缓存检查完成，耗时:', initialCacheTime.toFixed(2), 'ms，开始初始化流程');

        // 尝试初始化实时进度
        if (!initRealtimeProgress()) {
            console.log('实时进度不可用，使用传统流程');
            startTraditionalFlow();
        } else {
            console.log('实时进度已启用');

            // 启动实时进度监听
            progressMonitor.start();

            // 同时启动并发处理（如果需要）
            if (!cachedPreviewsLoaded) {
                // 延迟启动并发处理，确保页面已准备好
                setTimeout(function() {
                    startConcurrentProcessing();
                }, 1000);
            }
        }

        // 额外的快速检查，专门针对前8张图
        setTimeout(function() {
            if (!progressCompleted) {
                console.log('执行前8张图的快速检查');
                checkPreviewStatus();
            }
        }, 1500);
    }).catch(function(error) {
        console.log('缓存检查失败，继续正常流程:', error);
        startTraditionalFlow();
    });

    // 【新增】启动并发处理（专门用于前端预览图生成）
    function startConcurrentProcessing() {
        console.log('[并发处理] 检查是否需要启动并发预览图生成');

        // 检查是否已经有进度数据（表示已经在处理中）
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'ss_get_progress',
                category_id: window.ss_category_data.term_id,
                nonce: '<?php echo wp_create_nonce('ss_frontend_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data && response.data.percentage > 0 && !response.data.completed) {
                    console.log('[并发处理] 检测到正在进行的处理任务，继续监听进度');
                    // 已经在处理中，不需要重新启动
                } else {
                    console.log('[并发处理] 没有检测到进行中的任务，并发处理将通过 ss_check_previews_status 自动启动');
                    // 并发处理会在 ss_check_previews_status 中自动启动
                    // 这里不需要额外的操作，让正常的检查流程处理
                }
            },
            error: function(xhr, status, error) {
                console.log('[并发处理] 进度检查失败，继续正常流程:', error);
            }
        });
    }
    
    function scheduleNextCheck() {
        if (!progressCompleted) {
            // 【修复】只检查当前批次的剩余项目，而不是所有可见项目
            var currentBatchItems = [];
            var batchStart, batchEnd;

            if (loadingStage === 1) {
                batchStart = 0;
                batchEnd = 8;
            } else {
                batchStart = (loadingStage - 1) * 8;
                batchEnd = loadingStage * 8;
            }

            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex >= batchStart && displayIndex < batchEnd) {
                    currentBatchItems.push($(this));
                }
            });

            // 统计当前批次中未加载的项目
            var remainingInBatch = 0;
            currentBatchItems.forEach(function($item) {
                var $image = $item.find('.ss-svg-preview-image');
                if (!$image.hasClass('loaded') && !$image.hasClass('cached-preview')) {
                    remainingInBatch++;
                }
            });

            // 根据当前批次剩余项目数量调整检查间隔
            var checkInterval;
            if (remainingInBatch <= 1) {
                checkInterval = 500; // 最后1个项目时每0.5秒检查
            } else if (remainingInBatch <= 2) {
                checkInterval = 800; // 最后2个项目时每0.8秒检查
            } else if (loadingStage === 1) {
                checkInterval = 500; // 第一批使用500ms间隔，最快响应
            } else {
                checkInterval = 1000; // 其他批次使用1秒间隔
            }

            console.log('安排下次检查: 当前批次剩余' + remainingInBatch + '个项目，间隔' + checkInterval + 'ms，阶段' + loadingStage);
            setTimeout(checkPreviewStatus, checkInterval);
        }
    }

    // 【新增】倒计时相关函数
    function startBatchCountdown() {
        // 【修复】检查是否真的需要显示倒计时
        if (progressCompleted) {
            console.log('进度已完成，跳过倒计时');
            return;
        }

        // 【修复】检查当前批次的缓存覆盖率，而不是整体覆盖率
        if (cachedPreviewsLoaded) {
            // 获取当前批次的项目
            var currentBatchItems = [];
            var batchStart, batchEnd;

            if (loadingStage === 1) {
                batchStart = 0;
                batchEnd = 8;
            } else {
                batchStart = (loadingStage - 1) * 8;
                batchEnd = loadingStage * 8;
            }

            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex >= batchStart && displayIndex < batchEnd) {
                    currentBatchItems.push($(this));
                }
            });

            // 统计当前批次中已加载的项目
            var batchLoadedItems = 0;
            currentBatchItems.forEach(function($item) {
                var $image = $item.find('.ss-svg-preview-image');
                if ($image.hasClass('loaded') || $image.hasClass('cached-preview')) {
                    batchLoadedItems++;
                }
            });

            var batchCacheRatio = currentBatchItems.length > 0 ? batchLoadedItems / currentBatchItems.length : 0;

            console.log('当前批次缓存检查:', {
                stage: loadingStage,
                batchRange: batchStart + '-' + (batchEnd - 1),
                batchTotal: currentBatchItems.length,
                batchLoaded: batchLoadedItems,
                batchCacheRatio: Math.round(batchCacheRatio * 100) + '%'
            });

            // 【关键修复】只有当前批次缓存覆盖率足够高时才跳过倒计时
            if (batchCacheRatio >= 0.8) {
                console.log('当前批次缓存覆盖率足够高(' + Math.round(batchCacheRatio * 100) + '%)，跳过倒计时显示');
                return;
            }
        }

        batchStartTime = Date.now();

        // 【优化】第一批30秒，其他批次20秒
        var estimatedBatchTime;
        if (loadingStage === 1) {
            estimatedBatchTime = 30; // 第一批预估30秒
        } else {
            estimatedBatchTime = 20; // 其他批次预估20秒
        }

        estimatedTimeRemaining = estimatedBatchTime;

        // 显示倒计时提示
        showCountdownMessage();

        // 开始倒计时
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }

        countdownInterval = setInterval(function() {
            estimatedTimeRemaining--;
            updateCountdownMessage();

            if (estimatedTimeRemaining <= 0) {
                clearInterval(countdownInterval);
                hideCountdownMessage();
                // 【新增】标记倒计时自然结束，阻止后续重新启动
                countdownManager.markExpired();
            }
        }, 1000);

        console.log('开始第' + loadingStage + '批倒计时，预估时间:', estimatedBatchTime + '秒');
    }

    function showCountdownMessage() {
        var message = getCountdownMessage();

        // 如果倒计时元素不存在，创建它
        if ($('.ss-countdown-message').length === 0) {
            var countdownHtml = '<div class="ss-countdown-message">' +
                '<div class="ss-countdown-content">' +
                '<span class="ss-countdown-icon">' +
                    '<svg class="ss-timer-icon" viewBox="0 0 24 24" width="16" height="16">' +
                        '<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>' +
                        '<path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>' +
                        '<circle cx="12" cy="12" r="1" fill="currentColor"/>' +
                    '</svg>' +
                '</span>' +
                '<span class="ss-countdown-text">' + message + '</span>' +
                '</div>' +
                '</div>';

            $('.ss-loading-progress').before(countdownHtml);
        } else {
            $('.ss-countdown-text').text(message);
            $('.ss-countdown-message').fadeIn(300);
        }
    }

    function updateCountdownMessage() {
        var message = getCountdownMessage();
        $('.ss-countdown-text').text(message);
    }

    function getCountdownMessage() {
        var batchText = loadingStage === 1 ? 'first batch' :
                       loadingStage === 2 ? 'second batch' :
                       'batch ' + loadingStage;

        if (estimatedTimeRemaining > 0) {
            return 'Loading ' + batchText + ' of designs... Estimated time: ' + estimatedTimeRemaining + 's';
        } else {
            return 'Loading ' + batchText + ' of designs... Almost done!';
        }
    }

    function hideCountdownMessage() {
        $('.ss-countdown-message').fadeOut(300);
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
    }

    // 【新增】检查当前批次完成状态的函数
    function checkBatchCompletionStatus() {
        // 获取当前批次应该检查的项目
        var currentBatchItems = [];

        if (loadingStage === 1) {
            // 第一批：前8张显示的图片
            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex < 8) {
                    currentBatchItems.push($(this));
                }
            });
        } else {
            // 【修复】后续批次：使用固定的8张图作为批次大小
            var batchStart = (loadingStage - 1) * 8;
            var batchEnd = loadingStage * 8;

            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex >= batchStart && displayIndex < batchEnd) {
                    currentBatchItems.push($(this));
                }
            });
        }

        // 统计已完成的项目
        var completedInBatch = 0;
        currentBatchItems.forEach(function($item) {
            if ($item.find('.ss-svg-preview-image').hasClass('loaded')) {
                completedInBatch++;
            }
        });

        console.log('批次完成状态检查:', {
            stage: loadingStage,
            totalInBatch: currentBatchItems.length,
            completedInBatch: completedInBatch,
            isComplete: completedInBatch >= currentBatchItems.length
        });

        // 【修复】如果当前批次已完成且倒计时还在显示，立即隐藏
        if (completedInBatch >= currentBatchItems.length && currentBatchItems.length > 0) {
            if ($('.ss-countdown-message:visible').length > 0) {
                console.log('检测到当前批次已完成，立即隐藏倒计时 (二次访问优化)');
                hideCountdownMessage();

                // 更新累计进度
                updateProgress();

                // 【修复】标记当前批次为已完成，避免重复处理
                if (!currentBatchCompleted) {
                    currentBatchCompleted = true;
                }
            }
        }
    }
    
    function checkPreviewStatus() {
        // 如果已经完成，不再检查
        if (progressCompleted) {
            return;
        }
        
        // 【修复】检查当前批次的缓存覆盖率，而不是整体覆盖率
        if (cachedPreviewsLoaded && loadingStage > 1) {
            // 获取当前批次的项目
            var currentBatchItems = [];
            var batchStart = (loadingStage - 1) * 8;
            var batchEnd = loadingStage * 8;

            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex >= batchStart && displayIndex < batchEnd) {
                    currentBatchItems.push($(this));
                }
            });

            // 统计当前批次中已加载的项目
            var batchLoadedItems = 0;
            currentBatchItems.forEach(function($item) {
                var $image = $item.find('.ss-svg-preview-image');
                if ($image.hasClass('loaded') || $image.hasClass('cached-preview')) {
                    batchLoadedItems++;
                }
            });

            var batchCacheRatio = currentBatchItems.length > 0 ? batchLoadedItems / currentBatchItems.length : 0;

            // 【关键修复】只有当前批次缓存覆盖率非常高时才减少检查频率
            if (batchCacheRatio >= 0.9) {
                console.log('当前批次缓存覆盖率极高(' + Math.round(batchCacheRatio * 100) + '%)，适度减少检查频率');
                // 【修复】减少延迟时间，从5秒改为2秒
                setTimeout(checkPreviewStatus, 2000);
                return;
            }
        }

        // 【重构】基于display-index的分批加载逻辑
        var itemsToCheck = [];
        var displayIndexes = [];

        if (loadingStage === 1) {
            // 第一阶段：只检查前8张显示的图片（display-index 0-7）
            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                if (displayIndex < 8) {
                    var originalIndex = parseInt($(this).data('index'));
                    itemsToCheck.push(originalIndex);
                    displayIndexes.push(displayIndex);
                }
            });
            console.log('第一阶段：检查前8张显示的图片，display-index:', displayIndexes, 'original-index:', itemsToCheck);
        } else {
            // 【修复】后续阶段：按批次检查，每批8张图
            var batchStart = (loadingStage - 1) * 8; // 当前批次的起始display-index
            var batchEnd = loadingStage * 8; // 当前批次的结束display-index

            $('.ss-svg-preview-item:visible').each(function() {
                var displayIndex = parseInt($(this).data('display-index'));
                // 【关键修复】只检查当前批次范围内的项目
                if (displayIndex >= batchStart && displayIndex < batchEnd) {
                    var $image = $(this).find('.ss-svg-preview-image');
                    // 只检查未加载且非缓存的项目
                    if (!$image.hasClass('loaded') && !$image.hasClass('cached-preview')) {
                        var originalIndex = parseInt($(this).data('index'));
                        itemsToCheck.push(originalIndex);
                        displayIndexes.push(displayIndex);
                    }
                }
            });
            console.log('第' + loadingStage + '阶段：检查批次范围 ' + batchStart + '-' + (batchEnd-1) + '，display-index:', displayIndexes, 'original-index:', itemsToCheck);
        }

        // 如果没有要检查的项目，跳过
        if (itemsToCheck.length === 0) {
            console.log('没有要检查的项目，跳过检查');
            scheduleNextCheck();
            return;
        }
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'ss_check_previews_status',
                nonce: '<?php echo wp_create_nonce('ss_frontend_nonce'); ?>',
                term_id: window.ss_category_data.term_id,
                visible_indexes: itemsToCheck.join(','),
                display_indexes: displayIndexes.join(','),
                is_first_visit: isFirstVisit,
                loading_stage: loadingStage
            },
            success: function(response) {
                if (response.success && response.data && response.data.total_count > 0) {
                    // 更新首次访问标记
                    isFirstVisit = response.data.is_first_visit;
                    
                    updatePreviewItems(response.data.previews);
                    
                    // 添加调试日志
                    console.log('进度更新:', {
                        completed: response.data.completed_count,
                        total: response.data.total_count,
                        lastCompleted: lastCompletedCount,
                        lastTotal: lastTotalCount
                    });

                    // 【优化】基于当前批次计算进度
                    var currentBatchItems = itemsToCheck.length;
                    var currentBatchCompleted = 0;

                    // 统计当前批次中已完成的项目
                    response.data.previews.forEach(function(preview) {
                        if (preview.status === 'completed' && itemsToCheck.includes(preview.index)) {
                            currentBatchCompleted++;
                        }
                    });

                    console.log('当前批次进度:', {
                        stage: loadingStage,
                        completed: currentBatchCompleted,
                        total: currentBatchItems,
                        items: itemsToCheck
                    });

                    // 检查数据是否有变化
                    if (lastCompletedCount !== currentBatchCompleted || lastTotalCount !== currentBatchItems) {
                        lastCompletedCount = currentBatchCompleted;
                        lastTotalCount = currentBatchItems;

                        var isBatchComplete = currentBatchCompleted >= currentBatchItems;

                        // 更新进度条
                        if (!progressCompleted) {
                            // 【修复】不传递批次参数，让updateProgress函数自己计算实际数量
                            updateProgress();

                            // 如果当前批次完成，检查是否需要进入下一阶段
                            if (isBatchComplete) {
                                // 【修复】移除错误的条件判断，确保倒计时能正确隐藏
                                if (!currentBatchCompleted) {
                                    currentBatchCompleted = true;
                                }

                                // 【优化】当前批次完成时立即停止倒计时
                                countdownManager.stop();
                                console.log('当前批次完成，立即停止倒计时');

                                if (loadingStage === 1) {
                                    // 第一批完成，检查是否有更多项目需要加载
                                    var totalVisibleItems = $('.ss-svg-preview-item:visible').length;
                                    if (totalVisibleItems > 8) {
                                        console.log('前8张图完成，开始加载下一批');
                                        loadingStage = 2;
                                        currentBatchCompleted = false;

                                        // 【修复】使用倒计时管理器启动下一批倒计时
                                        countdownManager.start();

                                        // 立即检查下一批
                                        setTimeout(checkPreviewStatus, 500);
                                    } else {
                                        // 所有可见项目都完成了
                                        progressCompleted = true;
                                        // 【修复】不隐藏进度条，保持显示数量
                                        countdownManager.stop();
                                        isFirstVisit = false;
                                        console.log('所有可见SVG预览图加载完成，进度条保持显示');
                                    }
                                } else {
                                    // 【修复】检查是否还有未加载的可见项目
                                    var unloadedItems = $('.ss-svg-preview-item:visible .ss-svg-preview-image:not(.loaded):not(.cached-preview)').length;

                                    if (unloadedItems > 0) {
                                        console.log('第' + loadingStage + '批完成，还有' + unloadedItems + '个未加载项目，继续下一批');
                                        loadingStage++;
                                        currentBatchCompleted = false;

                                        // 【修复】使用倒计时管理器启动下一批倒计时
                                        countdownManager.start();

                                        setTimeout(checkPreviewStatus, 500);
                                    } else {
                                        progressCompleted = true;
                                        // 【修复】不隐藏进度条，保持显示数量
                                        countdownManager.stop();
                                        isFirstVisit = false;
                                        console.log('所有可见SVG预览图加载完成，进度条保持显示');
                                    }
                                }
                            }
                        }
                    }
                    
                    // 如果未完成，继续检查
                    if (!progressCompleted) {
                        scheduleNextCheck();
                    }
                } else {
                    scheduleNextCheck();
                }
            },
            error: function(xhr, status, error) {
                scheduleNextCheck();
            }
        });
    }
    
    function updatePreviewItems(previews) {
        previews.forEach(function(preview) {
            var $item = $('.ss-svg-preview-item[data-index="' + preview.index + '"]');
            var $image = $item.find('.ss-svg-preview-image');

            // 【新增】跳过已经从缓存加载的项目
            if ($image.hasClass('cached-preview')) {
                console.log('跳过缓存预览图: index=' + preview.index);
                return;
            }

            if (preview.status === 'completed' && !$image.hasClass('loaded')) {
                // 【重构】基于display-index判断优先级
                var img = new Image();
                var displayIndex = parseInt($item.data('display-index'));
                var isHighPriority = displayIndex < 8; // 前8张显示的图片为高优先级

                img.onload = function() {
                    // 移除骨架加载器
                    $image.removeClass('skeleton-loader')
                        .addClass('loaded')
                        .find('.skeleton-placeholder').remove();

                    // 插入图片并添加淡入效果
                    $image.append('<img src="' + preview.preview_url + '" alt="' + $item.data('file') + '" class="fade-in">');

                    console.log('图片加载完成: display-index=' + displayIndex + ', original-index=' + preview.index + ', 高优先级=' + isHighPriority);

                    // 【新增】立即更新进度条和检查倒计时状态
                    setTimeout(function() {
                        updateProgress(); // 触发进度条重新计算
                        countdownManager.checkAndStop();
                    }, 100);

                    // 【优化】前8张显示的图片完成时立即触发下一次检查
                    if (isHighPriority) {
                        console.log('前8张显示图片之一加载完成，立即检查其他图片');
                        setTimeout(checkPreviewStatus, 200);
                    }
                };

                img.onerror = function() {
                    console.error('图片加载失败:', preview.preview_url, 'index:', preview.index);
                    $image.removeClass('skeleton-loader')
                        .addClass('load-error')
                        .find('.skeleton-placeholder').remove();

                    $image.append('<div class="error-message">Failed to load</div>' +
                        '<button class="retry-btn">Retry</button>');
                };

                img.src = preview.preview_url;
            }
            else if (preview.status === 'processing' && !$image.hasClass('loading-active')) {
                // 标记为活跃加载状态
                $image.addClass('loading-active');
            }
        });
    }
    
    function updateProgress(completed, total) {
        // 【修复】统计所有已加载的项目（包括可见和不可见的）
        var actualLoadedCount = $('.ss-svg-preview-item .ss-svg-preview-image.loaded, .ss-svg-preview-item .ss-svg-preview-image.cached-preview').length;

        // 【修复】使用类目下所有SVG的总数量，而不是可见项目数量
        var totalSvgCount = <?php echo count($svg_designs); ?>;

        // 【优化】基于实际加载数量和总SVG数量计算进度
        var overallProgress = Math.round((actualLoadedCount / totalSvgCount) * 100);

        // 【修复】确保进度条始终可见且正确显示
        $('.ss-progress-bar').css('width', overallProgress + '%');
        $('.ss-progress-text').text(actualLoadedCount + '/' + totalSvgCount);

        // 【修复】确保进度条容器可见
        if ($('.ss-loading-progress').is(':hidden')) {
            $('.ss-loading-progress').show();
        }

        console.log('进度条更新:', {
            batchCompleted: completed || 'auto',
            batchTotal: total || 'auto',
            actualLoadedCount: actualLoadedCount,
            totalSvgCount: totalSvgCount,
            overallProgress: overallProgress + '%',
            stage: loadingStage,
            progressVisible: $('.ss-loading-progress').is(':visible')
        });

        // 【修复】检查是否所有SVG都已完成
        if (actualLoadedCount >= totalSvgCount && totalSvgCount > 0) {
            progressCompleted = true;
            // 【修复】完成时不隐藏进度条，保持显示最终状态
            $('.ss-progress-bar').addClass('completed');
            console.log('所有SVG预览图已完成，标记进度为完成状态，进度条保持显示');
        }
    }
    
    // 【修复】初始化进度条 - 使用类目下所有SVG的总数量
    var totalSvgCount = <?php echo count($svg_designs); ?>;
    $('.ss-progress-bar').css('width', '0%');
    $('.ss-progress-text').text('0/' + totalSvgCount);
    $('.ss-loading-progress').removeClass('completed');

    console.log('进度条初始化完成，SVG总数:', totalSvgCount, '第一批目标:', Math.min(8, totalSvgCount), '张图');
    
    // 处理重试按钮点击
    $(document).on('click', '.retry-btn', function() {
        // 重试时重置进度条完成状态
        progressCompleted = false;
        
        var $item = $(this).closest('.ss-svg-preview-item');
        var index = parseInt($item.data('index'));
        var $image = $item.find('.ss-svg-preview-image');
        
        // 重置状态
        $image.removeClass('load-error')
            .addClass('skeleton-loader loading-active')
            .empty()
            .append('<div class="skeleton-placeholder"></div>');
        
        // 请求重新生成
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'ss_regenerate_preview',
                nonce: '<?php echo wp_create_nonce('ss_frontend_nonce'); ?>',
                term_id: window.ss_category_data.term_id,
                index: index
            },
            complete: function() {
                // 重新开始检查
                checkPreviewStatus();
            }
        });
    });
    
    // 处理加载更多按钮点击事件
    $('.ss-load-more-btn').on('click', function() {
        // 获取所有隐藏的项目
        var $hiddenItems = $('.ss-svg-preview-item:hidden');

        // 如果有隐藏项目，显示下一批
        if ($hiddenItems.length > 0) {
            // 点击加载更多后，标记为非首次访问，确保能正确处理加载更多后的预览图
            isFirstVisit = false;

            console.log('加载更多: 显示下一批 ' + batchSize + ' 个项目');

            // 获取即将显示的项目
            var $itemsToShow = $hiddenItems.slice(0, batchSize);

            // 只显示下一批
            $itemsToShow.fadeIn(300);

            // 如果没有更多隐藏的项目，隐藏加载更多按钮
            if ($hiddenItems.length <= batchSize) {
                $(this).fadeOut(300);
                console.log('所有项目已显示，隐藏加载更多按钮');
            }

            // 【性能优化】优先使用本地缓存，大幅提升Load more速度
            console.log('【性能优化】检查新显示的', $itemsToShow.length, '个项目的缓存预览图');
            var loadStartTime = performance.now();

            // 首先尝试从本地缓存快速加载
            var localCacheResult = loadCachedPreviewsFromLocalCache($itemsToShow.toArray());

            if (localCacheResult !== null) {
                // 本地缓存成功
                var totalLoadTime = performance.now() - loadStartTime;
                window.ss_performance_stats.localCacheHits++;
                window.ss_performance_stats.avgLocalCacheTime =
                    (window.ss_performance_stats.avgLocalCacheTime * (window.ss_performance_stats.localCacheHits - 1) + totalLoadTime) /
                    window.ss_performance_stats.localCacheHits;

                console.log('【性能优化】本地缓存加载完成，总耗时:', totalLoadTime.toFixed(2), 'ms，成功加载', localCacheResult.count, '个预览图');
                console.log('【性能统计】本地缓存命中次数:', window.ss_performance_stats.localCacheHits, '，平均耗时:', window.ss_performance_stats.avgLocalCacheTime.toFixed(2), 'ms');

                if (localCacheResult.cached && localCacheResult.count > 0) {
                    console.log('【性能优化】从本地缓存成功加载', localCacheResult.count, '个新显示项目的预览图');
                }
            } else {
                // 本地缓存不可用，fallback到AJAX方式
                console.log('【性能优化】本地缓存不可用，使用AJAX方式加载');
                checkCachedPreviews($itemsToShow.toArray()).then(function(result) {
                    var totalLoadTime = performance.now() - loadStartTime;
                    window.ss_performance_stats.ajaxFallbacks++;
                    window.ss_performance_stats.avgAjaxTime =
                        (window.ss_performance_stats.avgAjaxTime * (window.ss_performance_stats.ajaxFallbacks - 1) + totalLoadTime) /
                        window.ss_performance_stats.ajaxFallbacks;

                    console.log('【性能优化】AJAX加载完成，总耗时:', totalLoadTime.toFixed(2), 'ms，结果:', result);
                    console.log('【性能统计】AJAX fallback次数:', window.ss_performance_stats.ajaxFallbacks, '，平均耗时:', window.ss_performance_stats.avgAjaxTime.toFixed(2), 'ms');

                    if (result.cached && result.count > 0) {
                        console.log('【性能优化】通过AJAX成功加载', result.count, '个新显示项目的缓存预览图');
                    }
                }).catch(function(error) {
                    console.log('【性能优化】AJAX加载失败:', error);
                });
            }

            // 【优化】重置分批加载状态，开始处理新显示的项目
            setTimeout(function() {
                // 重置加载状态
                progressCompleted = false;
                currentBatchCompleted = false;

                // 【新增】重置倒计时过期状态，允许新的倒计时
                countdownManager.resetExpiredStatus();

                // 【修复】重新计算加载阶段，确保从下一个未加载的批次开始
                var visibleItems = $('.ss-svg-preview-item:visible').length;
                var loadedItems = $('.ss-svg-preview-item:visible .ss-svg-preview-image.loaded').length;

                // 基于已加载的数量计算当前应该处于的阶段
                if (loadedItems < 8) {
                    loadingStage = 1; // 第一批还没完成
                } else {
                    loadingStage = Math.floor(loadedItems / batchSize) + 1; // 下一个需要加载的批次
                }

                console.log('重新计算加载阶段:', {
                    visibleItems: visibleItems,
                    loadedItems: loadedItems,
                    newStage: loadingStage,
                    batchSize: batchSize
                });

                // 【修复】重新计算实际加载数量和总SVG数量
                var actualLoadedCount = $('.ss-svg-preview-item .ss-svg-preview-image.loaded, .ss-svg-preview-item .ss-svg-preview-image.cached-preview').length;
                var totalSvgCount = <?php echo count($svg_designs); ?>;

                // 【修复】更新进度条显示
                var overallProgress = Math.round((actualLoadedCount / totalSvgCount) * 100);
                $('.ss-progress-bar').css('width', overallProgress + '%');
                $('.ss-progress-text').text(actualLoadedCount + '/' + totalSvgCount);

                console.log('加载更多后重置状态:', {
                    visibleItems: visibleItems,
                    newStage: loadingStage,
                    actualLoadedCount: actualLoadedCount,
                    totalSvgCount: totalSvgCount
                });

                // 【修复】使用倒计时管理器启动新阶段的倒计时
                countdownManager.start();

                checkPreviewStatus();
            }, 500);
        } else {
            // 如果没有更多隐藏的项目，隐藏按钮
            $(this).fadeOut(300);
            console.log('没有更多隐藏项目，隐藏加载更多按钮');
        }
    });

    // 【性能监控】添加性能报告功能
    window.showPerformanceReport = function() {
        var stats = window.ss_performance_stats;
        var report = '\n=== Load More 性能优化报告 ===\n';
        report += '本地缓存命中次数: ' + stats.localCacheHits + '\n';
        report += '本地缓存平均耗时: ' + (stats.avgLocalCacheTime || 0).toFixed(2) + 'ms\n';
        report += 'AJAX fallback次数: ' + stats.ajaxFallbacks + '\n';
        report += 'AJAX平均耗时: ' + (stats.avgAjaxTime || 0).toFixed(2) + 'ms\n';

        if (stats.localCacheHits > 0 && stats.ajaxFallbacks > 0) {
            var speedup = (stats.avgAjaxTime / stats.avgLocalCacheTime).toFixed(1);
            report += '性能提升倍数: ' + speedup + 'x\n';
        }

        report += '缓存数据量: ' + (window.ss_all_cached_previews ? window.ss_all_cached_previews.length : 0) + '个预览图\n';
        report += '缓存状态: ' + (window.ss_all_cached_previews ? '可用' : '不可用') + '\n';
        report += '===============================';

        console.log(report);
        return stats;
    };

    // 【性能监控】页面卸载时显示性能报告
    $(window).on('beforeunload', function() {
        if (window.ss_performance_stats.localCacheHits > 0 || window.ss_performance_stats.ajaxFallbacks > 0) {
            window.showPerformanceReport();
        }
    });
});
</script>

<?php
get_footer();
?> 