<?php
/**
 * WP-CLI命令类
 *
 * 提供WP-CLI命令来触发产品生成和其他功能
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// 确保WP-CLI存在
if (!class_exists('WP_CLI')) {
    return;
}

/**
 * 鞋子SVG生成器的WP-CLI命令
 */
class SS_CLI_Commands {

    /**
     * 权限修复函数 - 确保目录和文件所有者为www用户
     */
    private function ensure_www_permissions($path, $is_file = false) {
        if (!file_exists($path)) {
            return false;
        }
        
        // 设置基本权限
        $mode = $is_file ? 0644 : 0755;
        @chmod($path, $mode);
        
        // 尝试设置www用户所有权（优先使用www用户）
        $www_users = ['www', 'www-data', 'apache', 'nginx', 'httpd'];
        
        foreach ($www_users as $www_user) {
            if (function_exists('posix_getpwnam')) {
                $user_info = posix_getpwnam($www_user);
                if ($user_info !== false) {
                    $www_uid = $user_info['uid'];
                    $www_gid = $user_info['gid'];
                    
                    // 尝试使用PHP函数设置所有者
                    if (@chown($path, $www_uid) && @chgrp($path, $www_gid)) {
                        error_log("[权限修复] 成功设置所有者: " . basename($path) . " -> {$www_user}");
                        return true;
                    }
                }
            }
            
            // 尝试使用系统命令
            $cmd = "chown {$www_user}:{$www_user} " . escapeshellarg($path) . " 2>/dev/null";
            $result = shell_exec($cmd);
            if ($result === null || empty(trim($result))) {
                error_log("[权限修复] 使用系统命令设置所有者成功: " . basename($path) . " -> {$www_user}");
                return true;
            }
        }
        
        error_log("[权限修复] 无法设置www用户所有权: " . basename($path));
        return false;
    }

    /**
     * 创建目录并设置正确权限
     */
    private function create_directory_with_permissions($dir_path) {
        if (file_exists($dir_path)) {
            $this->ensure_www_permissions($dir_path, false);
            return true;
        }
        
        // 创建目录
        $created = wp_mkdir_p($dir_path);
        if ($created) {
            @chmod($dir_path, 0755);
            $this->ensure_www_permissions($dir_path, false);
            error_log("[权限修复] 创建目录并设置权限: {$dir_path}");
            return true;
        }
        
        error_log("[权限修复] 创建目录失败: {$dir_path}");
        return false;
    }

    /**
     * 递归设置整个目录树的权限
     */
    private function fix_directory_tree_permissions($dir_path) {
        if (!file_exists($dir_path) || !is_dir($dir_path)) {
            return false;
        }

        // 首先设置当前目录权限
        $this->ensure_www_permissions($dir_path, false);
        error_log("[权限修复] 递归修复目录权限: {$dir_path}");

        // 递归处理所有子目录和文件
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir_path, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                $this->ensure_www_permissions($item->getPathname(), false);
                error_log("[权限修复] 递归修复子目录权限: " . $item->getPathname());
            } else {
                $this->ensure_www_permissions($item->getPathname(), true);
                error_log("[权限修复] 递归修复文件权限: " . $item->getPathname());
            }
        }

        return true;
    }

    /**
     * 创建类目完整目录结构并设置权限
     */
    private function create_category_directory_structure($category_name) {
        $upload_dir = wp_upload_dir();
        
        // 获取安全的类目名称
        $safe_category_name = sanitize_title($category_name);
        
        // 主类目目录
        $category_main_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/';
        
        // 预览图子目录
        $previews_dir = $category_main_dir . 'previews/';
        
        // 创建主目录
        if (!file_exists($category_main_dir)) {
            $this->create_directory_with_permissions($category_main_dir);
        } else {
            $this->ensure_www_permissions($category_main_dir, false);
        }
        
        // 创建预览图子目录
        if (!file_exists($previews_dir)) {
            $this->create_directory_with_permissions($previews_dir);
        } else {
            $this->ensure_www_permissions($previews_dir, false);
        }
        
        // 递归修复整个目录树的权限
        $this->fix_directory_tree_permissions($category_main_dir);
        
        error_log("[权限修复] 完整类目目录结构创建完成: {$category_main_dir}");
        
        return $category_main_dir;
    }

    /**
     * 服务器资源配置
     */
    private $server_config = array(
        'max_parallel_processes' => 8,  // 默认最大并行进程数（提高到8）
        'cpu_threshold' => 90,          // CPU使用率阈值（%）（提高到90%）
        'memory_threshold' => 85,       // 内存使用率阈值（%）（提高到85%）
        'batch_size' => 8,              // 每批处理的类目数量（提高到8）
        'process_delay' => 1,           // 处理间隔（秒）（减少到1秒）
        'resource_check_interval' => 2, // 资源检查间隔（秒）（减少到2秒）
        'adaptive_delay' => true,       // 启用自适应延迟
        'max_wait_time' => 120,         // 最大等待时间（秒）（减少到2分钟）
        'quick_check_interval' => 1,   // 快速检查间隔（秒）
        'performance_mode' => false     // 高性能模式
    );

    /**
     * 触发产品生成过程
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * [--max-parallel=<number>]
     * : 最大并行进程数（默认：4，建议不超过CPU核心数的1/6）
     *
     * [--batch-size=<number>]
     * : 每批处理的类目数量（默认：5）
     *
     * [--cpu-threshold=<percentage>]
     * : CPU使用率阈值，超过此值将暂停处理（默认：80%）
     *
     * [--memory-threshold=<percentage>]
     * : 内存使用率阈值，超过此值将暂停处理（默认：85%）
     *
     * [--performance-mode]
     * : 启用高性能模式，提高资源利用率和处理速度
     *
     * [--adaptive-delay]
     * : 启用自适应延迟，根据资源使用情况动态调整延迟时间
     *
     * [--max-wait-time=<seconds>]
     * : 资源等待最大时间（默认：120秒）
     *
     * [--skip-json-records]
     * : 跳过JSON记录生成（默认：否，会自动生成JSON记录）
     *
     * ## EXAMPLES
     *
     *     # 为所有底层类目生成产品
     *     $ wp ss generate_products
     *     # 或者使用连字符版本（两者都支持）
     *     $ wp ss generate-products
     *
     *     # 为指定类目生成产品
     *     $ wp ss generate_products --category=123
     *
     *     # 为多个指定类目生成产品
     *     $ wp ss generate_products --category=123,456,789
     *
     *     # 显示详细日志
     *     $ wp ss generate_products --verbose
     *
     *     # 自定义资源限制
     *     $ wp ss generate_products --max-parallel=2 --cpu-threshold=70 --batch-size=3
     *
     *     # 启用高性能模式
     *     $ wp ss generate_products --performance-mode --max-parallel=12 --cpu-threshold=95
     *
     *     # 启用自适应延迟
     *     $ wp ss generate_products --adaptive-delay --max-wait-time=60
     *
     *     # 生成产品但跳过JSON记录生成
     *     $ wp ss generate_products --skip-json-records --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function generate_products($args, $assoc_args) {
        // 初始化服务器配置
        $this->init_server_config($assoc_args);

        // 设置详细日志模式
        $verbose = isset($assoc_args['verbose']);

        // 🔥 新增：自动生成JSON记录功能
        $auto_generate_json = true;
        if (isset($assoc_args['skip-json-records'])) {
            $auto_generate_json = !filter_var($assoc_args['skip-json-records'], FILTER_VALIDATE_BOOLEAN);
        }

        // 启用高性能模式
        if (isset($assoc_args['performance-mode'])) {
            $this->enable_performance_mode();
            WP_CLI::log('🚀 高性能模式已启用');
        }

        // 显示服务器配置信息
        $this->display_server_config($verbose);

        // 检查并显示parallel扩展状态
        $this->check_parallel_extension($verbose);

        if ($verbose) {
            // 设置直接输出日志到控制台
            add_action('error_log', function($message) {
                // 过滤掉GTK警告和fill path错误
                if (strpos($message, 'GtkRecentManager') !== false ||
                    strpos($message, 'fill path has no segments') !== false) {
                    return;
                }

                if (strpos($message, '[PARALLEL DEBUG]') !== false) {
                    WP_CLI::log($message);
                }
            });
        }

        WP_CLI::log('开始产品生成过程...');

        // 获取插件实例
        $generator = SS_SVG_Generator::get_instance();

        // 如果指定了类目ID
        if (isset($assoc_args['category'])) {
            // 解析类目ID，支持逗号分隔的多个ID
            $category_ids = explode(',', $assoc_args['category']);
            $category_ids = array_map('trim', $category_ids);
            $category_ids = array_map('intval', $category_ids);
            $category_ids = array_filter($category_ids); // 移除0值

            if (empty($category_ids)) {
                WP_CLI::error("无效的类目ID格式。请使用逗号分隔多个ID，例如：--category=123,456,789");
                return;
            }

            // 创建进度条
            $progress = \WP_CLI\Utils\make_progress_bar('生成产品', count($category_ids));

            $total_products = 0;
            $total_start_time = microtime(true);
            $processed_categories = 0;
            $skipped_categories = 0;

            // 准备要处理的类目数组
            $categories_to_process = [];

            // 首先检查所有类目是否有效，并且有产品模板
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');

                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("无法找到ID为 {$category_id} 的产品类目，跳过");
                    $progress->tick();
                    $skipped_categories++;
                    continue;
                }

                // 检查是否已在前端生成过SVG预览图
                $frontend_preview_generated = get_term_meta($category_id, 'ss_frontend_preview_generated', true);
                if (!empty($frontend_preview_generated)) {
                    WP_CLI::warning("类目 \"{$term->name}\" 已在前端生成SVG预览图，跳过");
                    error_log("[冲突检测] 类目 '" . $term->name . "' (ID: " . $category_id . ") 已在前端生成SVG预览图，WP CLI命令跳过处理");
                    $progress->tick();
                    $skipped_categories++;
                    continue;
                }

                // 检查是否有产品模板
                $has_template = $this->category_has_template($category_id);
                if (!$has_template) {
                    WP_CLI::warning("类目 \"{$term->name}\" 没有产品模板，跳过");
                    $progress->tick();
                    $skipped_categories++;
                    continue;
                }

                // 添加到要处理的类目数组
                $categories_to_process[] = $term;
            }

            if (empty($categories_to_process)) {
                WP_CLI::error("没有有效的类目可以处理");
                return;
            }

            // 🔥 新增：在产品生成前确保JSON记录存在
            if ($auto_generate_json) {
                WP_CLI::log("🔍 步骤1: 检查并生成JSON记录...");
                $this->ensure_json_records_for_categories($categories_to_process, $verbose);
            }

            WP_CLI::log("🏭 步骤2: 开始批量并行处理 " . count($categories_to_process) . " 个类目...");

            // 使用资源控制的批处理并行处理
            if (extension_loaded('parallel')) {
                try {
                    $this->process_categories_with_resource_control($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories, $verbose);
                } catch (\Throwable $e) {
                    WP_CLI::error("并行处理失败: " . $e->getMessage());

                    // 如果并行处理失败，回退到顺序处理
                    WP_CLI::log("回退到顺序处理...");
                    $this->process_categories_sequentially($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories);
                }
            } else {
                // 如果没有 parallel 扩展，使用顺序处理
                WP_CLI::warning("PHP parallel 扩展未加载，使用顺序处理...");
                $this->process_categories_sequentially($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories);
            }

            $progress->finish();

            $total_end_time = microtime(true);
            $total_duration = round($total_end_time - $total_start_time, 2);

            WP_CLI::success("处理完成！共处理 " . count($category_ids) . " 个类目，成功生成 {$processed_categories} 个类目的产品，跳过 {$skipped_categories} 个类目，总共生成 {$total_products} 个产品，总耗时: {$total_duration}秒");
        } else {
            // 获取所有底层类目
            $categories = $this->get_leaf_categories();

            if (empty($categories)) {
                WP_CLI::error("没有找到底层产品类目");
                return;
            }

            WP_CLI::log("找到 " . count($categories) . " 个底层产品类目");

            // 创建进度条
            $progress = \WP_CLI\Utils\make_progress_bar('生成产品', count($categories));

            $total_products = 0;
            $total_start_time = microtime(true);
            $processed_categories = 0;
            $skipped_categories = 0;

            // 准备要处理的类目数组
            $categories_to_process = [];

            // 高效批量筛选类目
            $categories_to_process = $this->filter_processable_categories($categories, $progress, $skipped_categories, $verbose);

            if (empty($categories_to_process)) {
                WP_CLI::error("没有有效的类目可以处理");
                return;
            }

            // 🔥 新增：在产品生成前确保JSON记录存在
            if ($auto_generate_json) {
                WP_CLI::log("🔍 步骤1: 检查并生成JSON记录...");
                $this->ensure_json_records_for_categories($categories_to_process, $verbose);
            }

            WP_CLI::log("🏭 步骤2: 开始批量并行处理 " . count($categories_to_process) . " 个底层类目...");

            // 使用资源控制的批处理并行处理
            if (extension_loaded('parallel')) {
                try {
                    $this->process_categories_with_resource_control($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories, $verbose);
                } catch (\Throwable $e) {
                    WP_CLI::error("并行处理失败: " . $e->getMessage());

                    // 如果并行处理失败，回退到顺序处理
                    WP_CLI::log("回退到顺序处理...");
                    $this->process_categories_sequentially($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories);
                }
            } else {
                // 如果没有 parallel 扩展，使用顺序处理
                WP_CLI::warning("PHP parallel 扩展未加载，使用顺序处理...");
                $this->process_categories_sequentially($generator, $categories_to_process, $progress, $total_products, $processed_categories, $skipped_categories);
            }

            $progress->finish();

            $total_end_time = microtime(true);
            $total_duration = round($total_end_time - $total_start_time, 2);

            WP_CLI::success("产品生成完成！共处理 " . count($categories) . " 个类目，成功生成 {$processed_categories} 个类目的产品，跳过 {$skipped_categories} 个类目，总共生成 {$total_products} 个产品，总耗时: {$total_duration}秒");
        }
    }

    /**
     * 获取所有底层产品类目（没有子类目的类目）
     *
     * @return array 底层类目数组
     */
    private function get_leaf_categories() {
        $all_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));

        $leaf_categories = array();

        foreach ($all_categories as $category) {
            $children = get_terms(array(
                'taxonomy' => 'product_cat',
                'parent' => $category->term_id,
                'hide_empty' => false,
                'fields' => 'ids',
            ));

            if (empty($children) || is_wp_error($children)) {
                $leaf_categories[] = $category;
            }
        }

        return $leaf_categories;
    }

    /**
     * 高效批量筛选可处理的类目
     *
     * @param array $categories 所有类目
     * @param object $progress 进度条对象
     * @param int &$skipped_categories 跳过的类目数（引用）
     * @param bool $verbose 是否显示详细信息
     * @return array 可处理的类目数组
     */
    private function filter_processable_categories($categories, $progress, &$skipped_categories, $verbose = false) {
        $categories_to_process = [];
        $category_ids = array_map(function($cat) { return $cat->term_id; }, $categories);

        // 批量查询所有类目的元数据，避免逐个查询
        $frontend_flags = $this->batch_get_term_meta($category_ids, 'ss_frontend_preview_generated');
        $template_categories = $this->batch_check_template_categories($category_ids);

        WP_CLI::log("🔍 开始高效筛选 " . count($categories) . " 个类目...");

        foreach ($categories as $category) {
            $category_id = $category->term_id;

            // 检查是否已在前端生成过SVG预览图
            if (!empty($frontend_flags[$category_id])) {
                if ($verbose) {
                    WP_CLI::log("⏭️  跳过：类目 \"{$category->name}\" 已在前端生成SVG预览图");
                }
                $progress->tick();
                $skipped_categories++;
                continue;
            }

            // 检查是否有产品模板
            if (!isset($template_categories[$category_id]) || !$template_categories[$category_id]) {
                if ($verbose) {
                    WP_CLI::log("⏭️  跳过：类目 \"{$category->name}\" 没有产品模板");
                }
                $progress->tick();
                $skipped_categories++;
                continue;
            }

            // 添加到要处理的类目数组
            $categories_to_process[] = $category;
        }

        WP_CLI::log("✅ 筛选完成：{$skipped_categories} 个类目被跳过，" . count($categories_to_process) . " 个类目待处理");
        return $categories_to_process;
    }

    /**
     * 批量获取类目元数据
     *
     * @param array $category_ids 类目ID数组
     * @param string $meta_key 元数据键
     * @return array 类目ID => 元数据值的映射
     */
    private function batch_get_term_meta($category_ids, $meta_key) {
        global $wpdb;

        if (empty($category_ids)) {
            return [];
        }

        $placeholders = implode(',', array_fill(0, count($category_ids), '%d'));
        $query = $wpdb->prepare(
            "SELECT term_id, meta_value FROM {$wpdb->termmeta}
             WHERE term_id IN ($placeholders) AND meta_key = %s",
            array_merge($category_ids, [$meta_key])
        );

        $results = $wpdb->get_results($query);
        $meta_data = [];

        foreach ($results as $row) {
            $meta_data[$row->term_id] = $row->meta_value;
        }

        return $meta_data;
    }

    /**
     * 批量检查类目是否有模板产品
     *
     * @param array $category_ids 类目ID数组
     * @return array 类目ID => 是否有模板的映射
     */
    private function batch_check_template_categories($category_ids) {
        global $wpdb;

        if (empty($category_ids)) {
            return [];
        }

        $placeholders = implode(',', array_fill(0, count($category_ids), '%d'));

        // 批量查询有模板产品的类目
        $query = $wpdb->prepare("
            SELECT DISTINCT tr.term_taxonomy_id as term_id
            FROM {$wpdb->term_relationships} tr
            INNER JOIN {$wpdb->posts} p ON tr.object_id = p.ID
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE tr.term_taxonomy_id IN ($placeholders)
            AND p.post_type = 'product'
            AND p.post_status = 'publish'
            AND pm.meta_key = '_ss_is_template'
            AND pm.meta_value = '1'
        ", $category_ids);

        $results = $wpdb->get_col($query);
        $template_categories = [];

        // 初始化所有类目为false
        foreach ($category_ids as $category_id) {
            $template_categories[$category_id] = false;
        }

        // 设置有模板的类目为true
        foreach ($results as $category_id) {
            $template_categories[$category_id] = true;
        }

        return $template_categories;
    }

    /**
     * 检查类目是否有产品模板（保留兼容性）
     *
     * @param int $category_id 类目ID
     * @return bool 是否有产品模板
     */
    private function category_has_template($category_id) {
        $template_categories = $this->batch_check_template_categories([$category_id]);
        return isset($template_categories[$category_id]) ? $template_categories[$category_id] : false;
    }

    /**
     * 🔥 新增：确保指定类目的JSON记录存在
     *
     * @param array $categories 类目数组
     * @param bool $verbose 是否显示详细日志
     */
    private function ensure_json_records_for_categories($categories, $verbose = false) {
        if (!class_exists('SS_SVG_File_Manager')) {
            WP_CLI::warning("SS_SVG_File_Manager 类不存在，跳过JSON记录生成");
            return;
        }

        $svg_file_manager = SS_SVG_File_Manager::get_instance();
        $json_start_time = microtime(true);

        $success_count = 0;
        $skipped_count = 0;
        $error_count = 0;
        $total_files = 0;

        WP_CLI::log("检查 " . count($categories) . " 个类目的JSON记录...");

        foreach ($categories as $category) {
            // 检查是否已有JSON记录
            $existing_record = $svg_file_manager->get_category_json_record($category->term_id);

            if ($existing_record) {
                // 已有JSON记录，跳过
                $skipped_count++;
                if ($verbose) {
                    WP_CLI::log("  ⏭️  跳过类目 \"{$category->name}\" (ID: {$category->term_id})：已有JSON记录（{$existing_record['total_files']} 个文件）");
                }
            } else {
                // 没有JSON记录，生成新记录
                if ($verbose) {
                    WP_CLI::log("  🔄 正在为类目 \"{$category->name}\" (ID: {$category->term_id}) 生成JSON记录...");
                }

                $result = $svg_file_manager->generate_category_json_record($category->term_id);

                if ($result['success']) {
                    $success_count++;
                    $total_files += $result['files_count'];
                    if ($verbose) {
                        WP_CLI::log("  ✅ " . $result['message']);
                    }
                } else {
                    $error_count++;
                    WP_CLI::warning("  ❌ 类目 \"{$category->name}\": " . $result['message']);
                }
            }
        }

        $json_duration = round(microtime(true) - $json_start_time, 2);

        WP_CLI::log("✅ JSON记录检查完成，耗时: {$json_duration}秒");
        WP_CLI::log("📊 统计: 新生成 {$success_count} 个记录，跳过 {$skipped_count} 个，失败 {$error_count} 个，总文件数 {$total_files}");

        if ($error_count > 0) {
            WP_CLI::warning("⚠️  有 {$error_count} 个类目的JSON记录生成失败，但产品生成将继续进行");
        }
    }

    /**
     * 顺序处理类目（用于并行处理失败时的回退）
     *
     * @param SS_SVG_Generator $generator 生成器实例
     * @param array $categories 要处理的类目数组
     * @param object $progress 进度条对象
     * @param int &$total_products 总产品数（引用）
     * @param int &$processed_categories 已处理类目数（引用）
     * @param int &$skipped_categories 已跳过类目数（引用）
     */
    private function process_categories_sequentially($generator, $categories, $progress, &$total_products, &$processed_categories, &$skipped_categories) {
        $category_count = 0;
        $total_count = count($categories);

        foreach ($categories as $term) {
            $category_count++;
            WP_CLI::log("正在为类目 \"{$term->name}\" (ID: {$term->term_id}) 生成产品... ({$category_count}/{$total_count})");

            // 更频繁的资源检查（每个类目都检查）
            $resource_check = $this->check_resource_limits();
            if (!$resource_check['cpu_ok'] || !$resource_check['memory_ok']) {
                WP_CLI::log("⚠️  资源使用率过高 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%, 等待资源释放...");
                $this->wait_for_resources(true);
            } else {
                // 显示当前资源使用情况
                if ($category_count % 5 == 0) { // 每5个类目显示一次资源状态
                    WP_CLI::log("📊 资源状态良好 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%");
                }
            }

            // 检查类目是否有预设颜色
            error_log("[颜色提取][CLI] 开始检查类目 '" . $term->name . "' (ID: " . $term->term_id . ") 的预设颜色");
            $preset_colors = get_term_meta($term->term_id, 'ss_category_colors', true);

            if (empty($preset_colors)) {
                WP_CLI::log("类目 \"{$term->name}\" 没有预设颜色，尝试提取颜色...");
                error_log("[颜色提取][CLI][错误] 类目 '" . $term->name . "' (ID: " . $term->term_id . ") 没有预设颜色，元数据值: " . var_export($preset_colors, true));
            } else {
                WP_CLI::log("类目 \"{$term->name}\" 已有预设颜色: " . implode(', ', $preset_colors));
                error_log("[颜色提取][CLI][成功] 类目 '" . $term->name . "' (ID: " . $term->term_id . ") 获取到预设颜色: " . var_export($preset_colors, true));
                if (is_array($preset_colors)) {
                    error_log("[颜色提取][CLI][详细] 预设颜色数组: [" . implode(', ', $preset_colors) . "]");
                    error_log("[颜色提取][CLI][详细] 预设颜色数量: " . count($preset_colors));
                } else {
                    error_log("[颜色提取][CLI][警告] 预设颜色不是数组格式，类型: " . gettype($preset_colors));
                }
            }

            if (empty($preset_colors)) {

                // 获取分类图片ID
                $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);

                if (!empty($thumbnail_id)) {
                    // 获取图片路径
                    $image_path = get_attached_file($thumbnail_id);

                    if (file_exists($image_path)) {
                        // 确保颜色分析类已加载
                        if (!class_exists('SS_Color_Analyzer')) {
                            require_once plugin_dir_path(__FILE__) . 'includes/class-ss-color-analyzer.php';
                        }

                        // 使用颜色分析类提取颜色
                        $color_analyzer = new SS_Color_Analyzer();
                        $extracted_colors = $color_analyzer->analyze_image_colors($image_path, 10);

                        if (!empty($extracted_colors)) {
                            // 保存提取的颜色
                            update_term_meta($term->term_id, 'ss_category_colors', $extracted_colors);
                            WP_CLI::log("成功从图片提取颜色并保存到类目 \"{$term->name}\": " . implode(', ', $extracted_colors));
                            error_log("[颜色提取][CLI] 成功从图片提取颜色并保存到类目 '" . $term->name . "': " . implode(', ', $extracted_colors));
                        } else {
                            // 如果提取失败，使用默认颜色
                            $default_colors = $color_analyzer->get_default_colors();
                            update_term_meta($term->term_id, 'ss_category_colors', $default_colors);
                            WP_CLI::log("无法从图片提取颜色，使用默认颜色: " . implode(', ', $default_colors));
                            error_log("[颜色提取][CLI] 无法从图片提取颜色，使用默认颜色: " . implode(', ', $default_colors));
                        }
                    } else {
                        WP_CLI::warning("类目 \"{$term->name}\" 的图片文件不存在: {$image_path}");
                        error_log("[颜色提取][CLI][错误] 类目 '" . $term->name . "' 的图片文件不存在: " . $image_path);
                    }
                } else {
                    WP_CLI::warning("类目 \"{$term->name}\" 没有缩略图，无法提取颜色");
                    error_log("[颜色提取][CLI][错误] 类目 '" . $term->name . "' 没有缩略图，无法提取颜色");
                }
            }

            // 直接生成产品，不使用后台处理
            $start_time = microtime(true);
            $result = $generator->ss_generate_products_for_category($term);
            $end_time = microtime(true);
            $duration = round($end_time - $start_time, 2);

            if (is_array($result) && !empty($result)) {
                $total_products += count($result);
                $processed_categories++;
                WP_CLI::log("成功为类目 \"{$term->name}\" 生成了 " . count($result) . " 个产品，耗时: {$duration}秒");
            } else {
                // 检查是否有SVG文件
                $upload_dir = wp_upload_dir();
                $svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/svg/';
                $svg_files = glob($svg_dir . '*.svg');

                if (empty($svg_files)) {
                    WP_CLI::warning("没有找到SVG文件，请确保SVG文件已上传到 {$svg_dir}");
                } else {
                    // 获取已处理的SVG文件记录
                    $processed_svgs = get_term_meta($term->term_id, '_ss_completed_svg_files', true);
                    if (!is_array($processed_svgs)) {
                        $processed_svgs = array();
                    }

                    // 检查是否有新的未处理的SVG文件
                    $new_svg_files = array_filter($svg_files, function($svg_file) use ($processed_svgs) {
                        return !in_array(basename($svg_file), $processed_svgs);
                    });

                    // 检查是否有模板产品
                    $template_products = get_posts(array(
                        'post_type' => 'product',
                        'posts_per_page' => -1,
                        'post_status' => 'publish',
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'product_cat',
                                'field' => 'term_id',
                                'terms' => $term->term_id,
                            ),
                        ),
                        'meta_query' => array(
                            array(
                                'key' => '_ss_is_template',
                                'value' => '1',
                            ),
                        ),
                        'fields' => 'ids',
                    ));

                    if (empty($template_products)) {
                        WP_CLI::warning("类目 \"{$term->name}\" 没有产品模板，跳过");
                        $skipped_categories++;
                    } else {
                        if (empty($new_svg_files)) {
                            WP_CLI::log("类目 \"{$term->name}\" 没有新的SVG文件，但有产品模板，检查是否有新的模板产品...");
                        } else {
                            WP_CLI::log("类目 \"{$term->name}\" 发现新的SVG文件和产品模板，继续处理...");
                        }

                        // 继续处理，无论是否有新的SVG文件
                        $result = $generator->ss_generate_products_for_category($term);
                        if (is_array($result) && !empty($result)) {
                            $total_products += count($result);
                            $processed_categories++;
                            WP_CLI::log("成功为类目 \"{$term->name}\" 生成了 " . count($result) . " 个产品");
                        } else {
                            WP_CLI::warning("类目 \"{$term->name}\" 处理新内容时未能生成产品");
                            $skipped_categories++;
                        }
                    }
                }
            }

            $progress->tick();

            // 每10个类目显示一次性能统计
            if ($category_count % 10 == 0) {
                $this->display_performance_stats($total_count, $processed_categories, $total_products, $total_start_time);
            }

            // 智能延迟，避免服务器过载
            if ($category_count < $total_count) {
                $delay = $this->calculate_adaptive_delay();
                if ($delay > 0) {
                    if ($delay < 1) {
                        usleep($delay * 1000000); // 微秒延迟
                    } else {
                        sleep($delay);
                    }
                }
            }
        }

        error_log("[资源控制] 顺序处理完成，共处理 {$total_count} 个类目");
    }

    /**
     * 清除类目的产品生成标志，以便可以重新生成产品
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要清除标志的产品类目ID。如果不指定，将清除所有底层类目的标志。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 清除所有底层类目的产品生成标志
     *     $ wp ss clear_category_flags
     *     # 或者使用连字符版本（两者都支持）
     *     $ wp ss clear-category-flags
     *
     *     # 清除指定类目的产品生成标志
     *     $ wp ss clear_category_flags --category=123
     *
     *     # 清除多个指定类目的产品生成标志
     *     $ wp ss clear_category_flags --category=123,456,789
     *
     *     # 显示详细日志
     *     $ wp ss clear_category_flags --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function clear_category_flags($args, $assoc_args) {
        // 设置详细日志模式
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('开始清除类目产品生成标志...');

        // 如果指定了类目ID
        if (isset($assoc_args['category'])) {
            // 解析类目ID，支持逗号分隔的多个ID
            $category_ids = explode(',', $assoc_args['category']);
            $category_ids = array_map('trim', $category_ids);
            $category_ids = array_map('intval', $category_ids);
            $category_ids = array_filter($category_ids); // 移除0值

            if (empty($category_ids)) {
                WP_CLI::error("无效的类目ID格式。请使用逗号分隔多个ID，例如：--category=123,456,789");
                return;
            }

            // 创建进度条
            $progress = \WP_CLI\Utils\make_progress_bar('清除类目标志', count($category_ids));

            $processed_categories = 0;
            $skipped_categories = 0;

            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');

                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("无法找到ID为 {$category_id} 的产品类目，跳过");
                    $progress->tick();
                    $skipped_categories++;
                    continue;
                }

                // 清除标志
                $result = $this->clear_flags_for_category($term, $verbose);

                if ($result) {
                    $processed_categories++;
                    if ($verbose) {
                        WP_CLI::log("成功清除类目 \"{$term->name}\" 的产品生成标志");
                    }
                } else {
                    $skipped_categories++;
                    WP_CLI::warning("类目 \"{$term->name}\" 清除标志失败");
                }

                $progress->tick();
            }

            $progress->finish();

            WP_CLI::success("处理完成！共处理 " . count($category_ids) . " 个类目，成功清除 {$processed_categories} 个类目的标志，跳过 {$skipped_categories} 个类目");
        } else {
            // 获取所有底层类目
            $categories = $this->get_leaf_categories();

            if (empty($categories)) {
                WP_CLI::error("没有找到底层产品类目");
                return;
            }

            WP_CLI::log("找到 " . count($categories) . " 个底层产品类目");

            // 创建进度条
            $progress = \WP_CLI\Utils\make_progress_bar('清除类目标志', count($categories));

            $processed_categories = 0;
            $skipped_categories = 0;

            foreach ($categories as $category) {
                // 清除标志
                $result = $this->clear_flags_for_category($category, $verbose);

                if ($result) {
                    $processed_categories++;
                    if ($verbose) {
                        WP_CLI::log("成功清除类目 \"{$category->name}\" 的产品生成标志");
                    }
                } else {
                    $skipped_categories++;
                    WP_CLI::warning("类目 \"{$category->name}\" 清除标志失败");
                }

                $progress->tick();
            }

            $progress->finish();

            WP_CLI::success("处理完成！共处理 " . count($categories) . " 个类目，成功清除 {$processed_categories} 个类目的标志，跳过 {$skipped_categories} 个类目");
        }
    }

    /**
     * 清除指定类目的产品生成标志
     *
     * @param WP_Term $term 类目对象
     * @param bool $verbose 是否显示详细日志
     * @return bool 是否成功清除标志
     */
    private function clear_flags_for_category($term, $verbose = false) {
        try {
            $term_id = $term->term_id;

            // 1. 清除前端生成SVG预览图标志
            $frontend_preview_generated = get_term_meta($term_id, 'ss_frontend_preview_generated', true);
            if (!empty($frontend_preview_generated)) {
                delete_term_meta($term_id, 'ss_frontend_preview_generated');
                if ($verbose) {
                    $this->log_message("已清除类目 \"{$term->name}\" 的前端生成SVG预览图标志");
                }
                error_log("[清除标志] 已清除类目 '" . $term->name . "' (ID: " . $term_id . ") 的前端生成SVG预览图标志");
            }

            // 2. 清除WP CLI生成产品标志
            $wp_cli_generated = get_term_meta($term_id, 'ss_wp_cli_products_generated', true);
            if (!empty($wp_cli_generated)) {
                delete_term_meta($term_id, 'ss_wp_cli_products_generated');
                if ($verbose) {
                    $this->log_message("已清除类目 \"{$term->name}\" 的WP CLI生成产品标志");
                }
                error_log("[清除标志] 已清除类目 '" . $term->name . "' (ID: " . $term_id . ") 的WP CLI生成产品标志");
            }

            // 2.1 清除后台自动生成产品标志
            $backend_generated = get_term_meta($term_id, 'ss_backend_products_generated', true);
            if (!empty($backend_generated)) {
                delete_term_meta($term_id, 'ss_backend_products_generated');
                if ($verbose) {
                    $this->log_message("已清除类目 \"{$term->name}\" 的后台自动生成产品标志");
                }
                error_log("[清除标志] 已清除类目 '" . $term->name . "' (ID: " . $term_id . ") 的后台自动生成产品标志");
            }

            // 3. 清除已完成的SVG文件列表
            $completed_files = get_term_meta($term_id, '_ss_completed_svg_files', true);
            // 无论是否为空，都重置为空数组
            update_term_meta($term_id, '_ss_completed_svg_files', array());
            if ($verbose) {
                $this->log_message("已重置类目 \"{$term->name}\" 的已完成SVG文件列表" . (is_array($completed_files) ? "，原有 " . count($completed_files) . " 个文件" : ""));
            }
            error_log("[清除标志] 已重置类目 '" . $term->name . "' (ID: " . $term_id . ") 的已完成SVG文件列表" . (is_array($completed_files) ? "，原有 " . count($completed_files) . " 个文件" : ""));

            // 检查重置后的状态
            $check_files = get_term_meta($term_id, '_ss_completed_svg_files', true);
            error_log("[清除标志][验证] _ss_completed_svg_files 重置后状态: " . (is_array($check_files) ? "数组，包含 " . count($check_files) . " 个元素" : gettype($check_files)));

            // 4. 清除已处理的设计列表
            $processed_designs = get_term_meta($term_id, 'ss_processed_designs', true);
            if (is_array($processed_designs) && !empty($processed_designs)) {
                delete_term_meta($term_id, 'ss_processed_designs');
                if ($verbose) {
                    $this->log_message("已清除类目 \"{$term->name}\" 的已处理设计列表，共 " . count($processed_designs) . " 个设计");
                }
                error_log("[清除标志] 已清除类目 '" . $term->name . "' (ID: " . $term_id . ") 的已处理设计列表，共 " . count($processed_designs) . " 个设计");
            }

            return true;
        } catch (\Throwable $e) {
            $error_message = "清除类目 \"{$term->name}\" 标志时出错: " . $e->getMessage();
            $this->log_message($error_message, 'warning');
            error_log("[清除标志][错误] " . $error_message);
            error_log("[清除标志][错误] 堆栈跟踪: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 清理指定类目下的生成产品数据
     *
     * ## OPTIONS
     *
     * [--category_id=<id>]
     * : 指定要清理的产品类目ID（支持逗号分隔多个ID）
     *
     * [--all]
     * : 清理所有类目的生成产品
     *
     * [--dry-run]
     * : 预览模式，不实际删除数据
     *
     * [--force]
     * : 强制删除，跳过确认提示
     *
     * [--parallel=<number>]
     * : 并行清理进程数（默认：4，最大：16）
     *
     * [--include-json-records]
     * : 同时清理JSON记录文件（默认：是）
     *
     * [--verbose]
     * : 显示详细清理过程
     *
     * ## EXAMPLES
     *
     *     # 清理单个类目
     *     wp ss clean --category_id=123
     *
     *     # 清理多个类目（逗号分隔）
     *     wp ss clean --category_id=123,456,789
     *
     *     # 预览模式
     *     wp ss clean --category_id=123,456 --dry-run
     *
     *     # 并行清理（8个进程）
     *     wp ss clean --category_id=123,456,789 --parallel=8 --verbose
     *
     *     # 强制清理所有类目
     *     wp ss clean --all --force
     *
     *     # 清理时保留JSON记录文件
     *     wp ss clean --category_id=123 --include-json-records=false
     *
     * @param array $args
     * @param array $assoc_args
     */
    public function clean($args, $assoc_args) {
        // 在WP CLI环境中，通常不需要权限检查，因为CLI本身需要服务器访问权限
        // 但为了安全起见，我们检查是否在CLI环境中运行
        if (!defined('WP_CLI') || !WP_CLI) {
            WP_CLI::error('此命令只能在WP CLI环境中运行');
        }

        // 解析参数
        $category_ids_input = isset($assoc_args['category_id']) ? $assoc_args['category_id'] : '';
        $all_categories = isset($assoc_args['all']);
        $dry_run = isset($assoc_args['dry-run']);
        $force = isset($assoc_args['force']);
        $verbose = isset($assoc_args['verbose']);

        // 🔥 新增：JSON记录清理控制参数（默认为true）
        $include_json_records = true;
        if (isset($assoc_args['include-json-records'])) {
            $include_json_records = filter_var($assoc_args['include-json-records'], FILTER_VALIDATE_BOOLEAN);
        }

        // 🔥 新增：并行清理参数
        $parallel_processes = 1; // 默认单进程
        if (isset($assoc_args['parallel'])) {
            $parallel_processes = max(1, min(16, intval($assoc_args['parallel']))); // 限制1-16个进程
            WP_CLI::log("🚀 启用并行清理模式：{$parallel_processes} 个进程");
        }

        // 🔥 修复：解析多个类目ID（支持逗号分隔）
        $category_ids = array();
        if (!empty($category_ids_input)) {
            $ids_array = explode(',', $category_ids_input);
            foreach ($ids_array as $id) {
                $id = trim($id);
                if (is_numeric($id) && intval($id) > 0) {
                    $category_ids[] = intval($id);
                }
            }
            // 去重
            $category_ids = array_unique($category_ids);
        }

        // 参数验证
        if (!$all_categories && empty($category_ids)) {
            WP_CLI::error('请指定有效的类目ID（支持逗号分隔多个ID）或使用 --all 参数');
        }

        if (!empty($category_ids) && $all_categories) {
            WP_CLI::error('不能同时指定类目ID和 --all 参数');
        }

        // 获取要处理的类目
        $categories = array();
        if ($all_categories) {
            $categories = $this->get_all_product_categories_with_templates();
        } else {
            // 🔥 修复：处理多个类目ID
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("类目ID {$category_id} 不存在，跳过");
                    continue;
                }
                $categories[] = $term;
            }
        }

        if (empty($categories)) {
            WP_CLI::warning('没有找到要处理的产品类目');
            return;
        }

        WP_CLI::log(sprintf('准备处理 %d 个产品类目', count($categories)));

        // 🔥 新增：显示要处理的类目ID列表
        if (!empty($category_ids)) {
            WP_CLI::log('类目ID列表: ' . implode(', ', $category_ids));
        }

        // 统计信息
        $total_stats = array(
            'categories_processed' => 0,
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0,
            'errors' => 0
        );

        // 确认操作（除非是dry-run或force模式）
        if (!$dry_run && !$force) {
            $category_names = array_map(function($cat) { return $cat->name; }, $categories);
            WP_CLI::log('将要清理以下类目的生成产品：');
            foreach ($category_names as $name) {
                WP_CLI::log("  - {$name}");
            }

            // 修复确认操作，使用更明确的提示
            WP_CLI::log('');
            WP_CLI::log('此操作将删除：');
            WP_CLI::log('- 生成的产品（保留模板产品）');
            WP_CLI::log('- 生成产品的关联图片');
            WP_CLI::log('- SVG处理文件');
            WP_CLI::log('- 预览图和最终产品文件');
            if ($include_json_records) {
                WP_CLI::log('- JSON记录文件（svg-records目录）');
                WP_CLI::log('- 已处理SVG文件记录');
                WP_CLI::log('- 类目生成标志');
            } else {
                WP_CLI::log('- 保留JSON记录文件（--include-json-records=false）');
            }
            WP_CLI::log('');

            // 手动实现确认逻辑，确保正确处理用户输入
            WP_CLI::log('请输入 "yes" 或 "y" 确认，输入 "no" 或 "n" 取消：');
            $handle = fopen("php://stdin", "r");
            $input = trim(fgets($handle));
            fclose($handle);

            $confirmed = in_array(strtolower($input), ['yes', 'y', '是', '确认']);

            if (!$confirmed) {
                WP_CLI::log('操作已取消');
                return;
            }

            WP_CLI::log('确认执行清理操作...');
        }

        // 🔥 新增：选择处理模式（并行 vs 顺序）
        $total_categories = count($categories);

        if ($parallel_processes > 1 && $total_categories > 1 && extension_loaded('parallel')) {
            WP_CLI::log("🚀 使用并行清理模式：{$parallel_processes} 个进程处理 {$total_categories} 个类目");
            $this->process_categories_parallel($categories, $total_stats, $dry_run, $parallel_processes, $verbose, $include_json_records);
        } else {
            if ($parallel_processes > 1 && !extension_loaded('parallel')) {
                WP_CLI::warning("⚠️  PHP parallel扩展未加载，回退到顺序处理");
            }
            WP_CLI::log("📝 使用顺序清理模式处理 {$total_categories} 个类目");
            $this->process_categories_sequential($categories, $total_stats, $dry_run, $verbose, $include_json_records);
        }

        // 显示最终统计
        $this->display_clean_stats($total_stats, $dry_run);

        if ($dry_run) {
            WP_CLI::success('预览完成。移除 --dry-run 参数执行实际删除操作');
        } else {
            WP_CLI::success('清理操作完成');
        }
    }

    /**
     * 顺序处理类目清理
     */
    private function process_categories_sequential($categories, &$total_stats, $dry_run, $verbose, $include_json_records = true) {
        $total_categories = count($categories);

        foreach ($categories as $index => $category) {
            $current_num = $index + 1;
            WP_CLI::log("处理类目 [{$current_num}/{$total_categories}]: {$category->name} (ID: {$category->term_id})");

            $stats = $this->clean_category_products($category->term_id, $dry_run, $include_json_records);

            // 累计统计
            $total_stats['categories_processed']++;
            $total_stats['products_deleted'] += $stats['products_deleted'];
            $total_stats['images_deleted'] += $stats['images_deleted'];
            $total_stats['files_deleted'] += $stats['files_deleted'];
            $total_stats['errors'] += $stats['errors'];

            // 显示单个类目处理结果
            if ($stats['errors'] > 0) {
                WP_CLI::warning("  ⚠️  类目处理完成，但有 {$stats['errors']} 个错误");
            } else {
                WP_CLI::log("  ✅ 类目处理完成 - 删除产品: {$stats['products_deleted']}, 删除图片: {$stats['images_deleted']}, 删除文件: {$stats['files_deleted']}");
            }
        }
    }

    /**
     * 并行处理类目清理
     */
    private function process_categories_parallel($categories, &$total_stats, $dry_run, $parallel_processes, $verbose, $include_json_records = true) {
        $total_categories = count($categories);
        $start_time = microtime(true);

        WP_CLI::log("🚀 开始并行清理：{$parallel_processes} 个进程，{$total_categories} 个类目");

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('并行清理类目', $total_categories);

        $runtimes = [];
        $futures = [];
        $active_processes = 0;
        $category_queue = $categories;
        $results = [];
        $total_tasks_started = 0;
        $total_tasks_completed = 0;

        while (!empty($category_queue) || $active_processes > 0) {
            // 启动新的并行进程
            while ($active_processes < $parallel_processes && !empty($category_queue)) {
                $category = array_shift($category_queue);
                $total_tasks_started++;

                if ($verbose) {
                    WP_CLI::log("🚀 启动清理任务 #{$total_tasks_started}: {$category->name} (活跃进程: {$active_processes}/{$parallel_processes})");
                }

                try {
                    // 为每个任务创建独立的Runtime
                    $runtime = new \parallel\Runtime();

                    // 创建并行清理任务
                    $futures[$category->term_id] = $runtime->run(function($category_id, $category_name, $dry_run, $include_json_records) {
                        try {
                            // 🔥 修复：设置必要的环境变量，避免Lumise插件警告
                            if (!isset($_SERVER['SERVER_PORT'])) {
                                $_SERVER['SERVER_PORT'] = '80';
                            }
                            if (!isset($_SERVER['HTTP_HOST'])) {
                                $_SERVER['HTTP_HOST'] = 'localhost';
                            }
                            if (!isset($_SERVER['REQUEST_URI'])) {
                                $_SERVER['REQUEST_URI'] = '/';
                            }
                            if (!isset($_SERVER['REQUEST_METHOD'])) {
                                $_SERVER['REQUEST_METHOD'] = 'GET';
                            }
                            if (!isset($_SERVER['HTTPS'])) {
                                $_SERVER['HTTPS'] = 'off';
                            }
                            if (!isset($_SERVER['HTTP_USER_AGENT'])) {
                                $_SERVER['HTTP_USER_AGENT'] = 'WP-CLI/2.0';
                            }

                            // 在子进程中重新初始化WordPress
                            if (!defined('ABSPATH')) {
                                define('ABSPATH', '/www/wwwroot/matchbeast.com/');
                            }

                            // 🔥 修复：设置并行进程标志，避免WP_CLI调用
                            define('SS_PARALLEL_PROCESS', true);

                            // 🔥 修复：静默加载WordPress，避免插件初始化输出
                            ob_start();
                            // 禁用错误输出到避免污染并行进程输出
                            $old_error_reporting = error_reporting(E_ERROR | E_PARSE);
                            if (file_exists(ABSPATH . 'wp-load.php')) {
                                require_once ABSPATH . 'wp-load.php';
                            }
                            error_reporting($old_error_reporting);
                            ob_end_clean();

                            // 确保插件文件已加载
                            if (!class_exists('SS_CLI_Commands')) {
                                $plugin_file = ABSPATH . 'wp-content/plugins/shoe-svg-generator/class-ss-cli-commands.php';
                                if (file_exists($plugin_file)) {
                                    require_once $plugin_file;
                                }
                            }

                            // 创建CLI命令实例
                            $cli_instance = new SS_CLI_Commands();

                            // 执行清理
                            $start_time = microtime(true);
                            $stats = $cli_instance->clean_category_products($category_id, $dry_run, $include_json_records);
                            $duration = microtime(true) - $start_time;

                            return [
                                'category_id' => $category_id,
                                'category_name' => $category_name,
                                'success' => true,
                                'duration' => round($duration, 2),
                                'stats' => $stats
                            ];

                        } catch (\Throwable $e) {
                            return [
                                'category_id' => $category_id,
                                'category_name' => $category_name,
                                'success' => false,
                                'duration' => 0,
                                'error' => $e->getMessage(),
                                'stats' => [
                                    'products_deleted' => 0,
                                    'images_deleted' => 0,
                                    'files_deleted' => 0,
                                    'errors' => 1
                                ]
                            ];
                        }
                    }, [$category->term_id, $category->name, $dry_run, $include_json_records]);

                    $runtimes[$category->term_id] = $runtime;
                    $active_processes++;

                } catch (\Throwable $e) {
                    WP_CLI::warning("创建清理任务失败: {$category->name} - " . $e->getMessage());
                    $total_stats['errors']++;
                    $progress->tick();
                }
            }

            // 检查已完成的进程
            foreach ($futures as $category_id => $future) {
                if ($future->done()) {
                    try {
                        $result = $future->value();
                        $results[$category_id] = $result;
                        $total_tasks_completed++;

                        // 更新进度条
                        $progress->tick();

                        if ($result['success']) {
                            // 累计统计
                            $total_stats['categories_processed']++;
                            $total_stats['products_deleted'] += $result['stats']['products_deleted'];
                            $total_stats['images_deleted'] += $result['stats']['images_deleted'];
                            $total_stats['files_deleted'] += $result['stats']['files_deleted'];
                            $total_stats['errors'] += $result['stats']['errors'];

                            if ($verbose) {
                                WP_CLI::log("✅ 任务 #{$total_tasks_completed}: {$result['category_name']} - 耗时: {$result['duration']}s");
                                WP_CLI::log("   删除产品: {$result['stats']['products_deleted']}, 删除图片: {$result['stats']['images_deleted']}, 删除文件: {$result['stats']['files_deleted']}");
                            }
                        } else {
                            $total_stats['errors']++;
                            WP_CLI::warning("❌ 任务 #{$total_tasks_completed}: {$result['category_name']} 失败 - {$result['error']}");
                        }

                    } catch (\Throwable $e) {
                        WP_CLI::warning("处理清理结果时出错: " . $e->getMessage());
                        $total_stats['errors']++;
                    }

                    // 清理已完成的进程
                    unset($futures[$category_id]);
                    if (isset($runtimes[$category_id])) {
                        unset($runtimes[$category_id]);
                    }
                    $active_processes--;
                }
            }

            // 短暂休眠，避免CPU占用过高
            usleep(10000); // 10ms
        }

        $progress->finish();

        $total_time = microtime(true) - $start_time;
        WP_CLI::log("🎯 并行清理完成 - 总耗时: " . round($total_time, 2) . "s, 平均: " . round($total_time / $total_categories, 2) . "s/类目");
    }

    /**
     * 清理空的processed_svg目录
     *
     * ## OPTIONS
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 清理空的processed_svg目录
     *     $ wp ss cleanup_empty_dirs
     *     # 或者使用连字符版本（两者都支持）
     *     $ wp ss cleanup-empty-dirs
     *
     *     # 显示详细日志
     *     $ wp ss cleanup_empty_dirs --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function cleanup_empty_dirs($args, $assoc_args) {
        // 设置详细日志模式
        $verbose = isset($assoc_args['verbose']);

        if ($verbose) {
            // 设置直接输出日志到控制台
            add_action('error_log', function($message) {
                WP_CLI::log($message);
            });
        }

        WP_CLI::log('开始清理空的processed_svg目录...');

        // 获取插件实例
        $generator = SS_SVG_Generator::get_instance();

        // 执行清理
        $removed_count = $generator->ss_cleanup_empty_processed_svg_dirs();

        if ($removed_count === false) {
            WP_CLI::error('清理过程中出错');
        } else if ($removed_count === 0) {
            WP_CLI::success('没有找到空目录，无需清理');
        } else {
            WP_CLI::success("清理完成！共移除 {$removed_count} 个空目录");
        }
    }

    /**
     * 启用高性能模式（不覆盖用户设置）
     */
    private function enable_performance_mode() {
        $this->server_config['performance_mode'] = true;

        // 只在用户没有指定且未被锁定时才设置高性能默认值
        if (!isset($this->server_config['max_parallel_processes']) && !isset($this->server_config['user_max_parallel_locked'])) {
            $this->server_config['max_parallel_processes'] = max(32, $this->get_cpu_cores() / 2); // 更激进的并行数，无上限
        }
        if (!isset($this->server_config['cpu_threshold'])) {
            $this->server_config['cpu_threshold'] = 95;  // 提高CPU阈值到95%
        }
        if (!isset($this->server_config['memory_threshold'])) {
            $this->server_config['memory_threshold'] = 90; // 提高内存阈值到90%
        }
        if (!isset($this->server_config['batch_size'])) {
            // 批次大小应该至少等于并行数，确保充分利用并行能力
            $this->server_config['batch_size'] = max(32, $this->server_config['max_parallel_processes']);
        }
        if (!isset($this->server_config['process_delay'])) {
            $this->server_config['process_delay'] = 0.2; // 减少延迟到0.2秒
        }
        if (!isset($this->server_config['resource_check_interval'])) {
            $this->server_config['resource_check_interval'] = 0.5; // 更频繁的资源检查
        }
        if (!isset($this->server_config['max_wait_time'])) {
            $this->server_config['max_wait_time'] = 30; // 减少等待时间到30秒
        }
        if (!isset($this->server_config['quick_check_interval'])) {
            $this->server_config['quick_check_interval'] = 0.2; // 更快的检查间隔
        }
        if (!isset($this->server_config['adaptive_delay'])) {
            $this->server_config['adaptive_delay'] = true; // 默认启用自适应延迟
        }
    }

    /**
     * 检查parallel扩展状态
     *
     * @param bool $verbose 是否显示详细信息
     */
    private function check_parallel_extension($verbose) {
        if (extension_loaded('parallel')) {
            WP_CLI::log("✅ PHP parallel扩展已加载，将使用并行处理");
            if ($verbose) {
                $parallel_version = phpversion('parallel');
                WP_CLI::log("   📦 Parallel扩展版本: " . ($parallel_version ?: '未知'));

                // 测试parallel扩展基本功能
                try {
                    $test_runtime = new \parallel\Runtime();
                    $test_future = $test_runtime->run(function() {
                        return "parallel扩展测试成功";
                    });
                    $test_result = $test_future->value();
                    WP_CLI::log("   🧪 Parallel扩展测试: " . $test_result);
                } catch (\Throwable $e) {
                    WP_CLI::warning("   ⚠️  Parallel扩展测试失败: " . $e->getMessage());
                }
            }
        } else {
            WP_CLI::warning("❌ PHP parallel扩展未加载，将使用顺序处理");
            WP_CLI::log("   💡 要启用并行处理，请安装PHP parallel扩展");
            if ($verbose) {
                WP_CLI::log("   📖 安装指南: https://www.php.net/manual/en/parallel.installation.php");
            }
        }
    }



    /**
     * 初始化服务器配置
     *
     * @param array $assoc_args 关联参数
     */
    private function init_server_config($assoc_args) {
        // 自动检测CPU核心数并设置合理的并行进程数
        $cpu_cores = $this->get_cpu_cores();
        $recommended_parallel = max(1, min(8, intval($cpu_cores / 6))); // 不超过核心数的1/6，最少1个，最多8个

        // 自动检测服务器性能并设置基础配置（在用户参数之前）
        $this->auto_detect_server_performance();

        // 从命令行参数更新配置（用户参数优先级最高）
        if (isset($assoc_args['max-parallel'])) {
            // 严格按照用户输入，不设置上限（用户自己负责）
            $user_parallel = max(1, intval($assoc_args['max-parallel']));
            $this->server_config['max_parallel_processes'] = $user_parallel;

            // 🔒 锁定用户设置，防止被其他地方覆盖
            $this->server_config['user_max_parallel_locked'] = true;

            WP_CLI::log("🎯 用户指定并行进程数: {$this->server_config['max_parallel_processes']} (严格按照用户输入，已锁定)");

            // 高并行数警告
            if ($user_parallel > $cpu_cores) {
                WP_CLI::warning("⚠️  并行进程数({$user_parallel})超过CPU核心数({$cpu_cores})，可能影响性能");
            } elseif ($user_parallel > 50) {
                WP_CLI::warning("⚠️  使用了非常高的并行进程数({$user_parallel})，请确保服务器资源充足");
            }
        } else {
            // 如果用户没有指定，使用推荐值或自动检测值
            if (!isset($this->server_config['max_parallel_processes'])) {
                $this->server_config['max_parallel_processes'] = $recommended_parallel;
            }
        }

        if (isset($assoc_args['batch-size'])) {
            // 严格按照用户输入，不设置上限（用户自己负责）
            $user_batch_size = max(1, intval($assoc_args['batch-size']));
            $this->server_config['batch_size'] = $user_batch_size;
            WP_CLI::log("🎯 用户指定批次大小: {$this->server_config['batch_size']} (严格按照用户输入)");
        }

        if (isset($assoc_args['cpu-threshold'])) {
            // 严格按照用户输入，允许1-100%的任何值
            $user_cpu_threshold = max(1, min(100, intval($assoc_args['cpu-threshold'])));
            $this->server_config['cpu_threshold'] = $user_cpu_threshold;
            WP_CLI::log("🎯 用户指定CPU阈值: {$this->server_config['cpu_threshold']}% (严格按照用户输入)");
        }

        if (isset($assoc_args['memory-threshold'])) {
            // 严格按照用户输入，允许1-100%的任何值
            $user_memory_threshold = max(1, min(100, intval($assoc_args['memory-threshold'])));
            $this->server_config['memory_threshold'] = $user_memory_threshold;
            WP_CLI::log("🎯 用户指定内存阈值: {$this->server_config['memory_threshold']}% (严格按照用户输入)");
        }

        // 新增参数支持
        if (isset($assoc_args['adaptive-delay'])) {
            $this->server_config['adaptive_delay'] = true;
            WP_CLI::log("🎯 用户启用自适应延迟");
        }

        if (isset($assoc_args['max-wait-time'])) {
            $this->server_config['max_wait_time'] = intval($assoc_args['max-wait-time']);
            WP_CLI::log("🎯 用户指定最大等待时间: {$this->server_config['max_wait_time']}秒");
        }

        // 🔍 最终验证：确保用户参数被正确应用
        if (isset($assoc_args['max-parallel'])) {
            $expected_parallel = max(1, intval($assoc_args['max-parallel']));
            if ($this->server_config['max_parallel_processes'] != $expected_parallel) {
                WP_CLI::warning("⚠️  检测到并行进程数被意外修改！");
                WP_CLI::log("   期望值: {$expected_parallel}");
                WP_CLI::log("   实际值: {$this->server_config['max_parallel_processes']}");
                WP_CLI::log("   🔧 强制恢复用户设置...");
                $this->server_config['max_parallel_processes'] = $expected_parallel;
            }
        }

        // 记录配置到日志
        $user_locked = isset($this->server_config['user_max_parallel_locked']) ? '(用户锁定)' : '(自动检测)';
        error_log("[资源控制] 服务器配置初始化完成 - CPU核心数: {$cpu_cores}, 推荐并行数: {$recommended_parallel}, 实际并行数: {$this->server_config['max_parallel_processes']} {$user_locked}");
    }

    /**
     * 自动检测服务器性能并设置基础配置（不覆盖用户设置）
     */
    private function auto_detect_server_performance() {
        $cpu_cores = $this->get_cpu_cores();
        $memory_gb = $this->get_memory_gb();

        // 根据服务器性能设置基础配置（仅在未设置且未被用户锁定时）
        if ($cpu_cores >= 24 && $memory_gb >= 64) {
            // 高性能服务器
            if (!isset($this->server_config['max_parallel_processes']) && !isset($this->server_config['user_max_parallel_locked'])) {
                $this->server_config['max_parallel_processes'] = max(48, $cpu_cores / 2); // 移除上限，更激进
            }
            if (!isset($this->server_config['cpu_threshold'])) {
                $this->server_config['cpu_threshold'] = 92;
            }
            if (!isset($this->server_config['memory_threshold'])) {
                $this->server_config['memory_threshold'] = 88;
            }
            if (!isset($this->server_config['batch_size'])) {
                $this->server_config['batch_size'] = 12;
            }
            if (!isset($this->server_config['process_delay'])) {
                $this->server_config['process_delay'] = 0.5;
            }
            if (!isset($this->server_config['resource_check_interval'])) {
                $this->server_config['resource_check_interval'] = 1;
            }
            if (!isset($this->server_config['max_wait_time'])) {
                $this->server_config['max_wait_time'] = 60;
            }
            WP_CLI::log("🔥 检测到高性能服务器 (CPU: {$cpu_cores}核, 内存: {$memory_gb}GB)，已优化配置");
        } elseif ($cpu_cores >= 8 && $memory_gb >= 16) {
            // 中等性能服务器
            if (!isset($this->server_config['max_parallel_processes']) && !isset($this->server_config['user_max_parallel_locked'])) {
                $this->server_config['max_parallel_processes'] = max(16, $cpu_cores / 1.5); // 移除上限
            }
            if (!isset($this->server_config['cpu_threshold'])) {
                $this->server_config['cpu_threshold'] = 88;
            }
            if (!isset($this->server_config['memory_threshold'])) {
                $this->server_config['memory_threshold'] = 85;
            }
            if (!isset($this->server_config['batch_size'])) {
                $this->server_config['batch_size'] = 8;
            }
            WP_CLI::log("⚡ 检测到中等性能服务器 (CPU: {$cpu_cores}核, 内存: {$memory_gb}GB)，已调整配置");
        } else {
            // 低性能服务器，使用保守配置
            if (!isset($this->server_config['max_parallel_processes']) && !isset($this->server_config['user_max_parallel_locked'])) {
                $this->server_config['max_parallel_processes'] = max(8, $cpu_cores); // 移除上限，但保持保守
            }
            if (!isset($this->server_config['cpu_threshold'])) {
                $this->server_config['cpu_threshold'] = 80;
            }
            if (!isset($this->server_config['memory_threshold'])) {
                $this->server_config['memory_threshold'] = 80;
            }
            if (!isset($this->server_config['batch_size'])) {
                $this->server_config['batch_size'] = 4;
            }
            WP_CLI::log("🐌 检测到低性能服务器 (CPU: {$cpu_cores}核, 内存: {$memory_gb}GB)，使用保守配置");
        }
    }

    /**
     * 获取系统内存大小（GB）
     */
    private function get_memory_gb() {
        $memory_gb = 4; // 默认值
        if (is_file('/proc/meminfo')) {
            $meminfo = file_get_contents('/proc/meminfo');
            if (preg_match('/MemTotal:\s+(\d+)\s+kB/', $meminfo, $matches)) {
                $memory_gb = round($matches[1] / 1024 / 1024, 1);
            }
        }
        return $memory_gb;
    }

    /**
     * 显示服务器配置信息
     *
     * @param bool $verbose 是否显示详细信息
     */
    private function display_server_config($verbose) {
        $cpu_cores = $this->get_cpu_cores();
        $memory_info = $this->get_memory_info();

        WP_CLI::log("=== 服务器资源配置 ===");
        WP_CLI::log("CPU核心数: {$cpu_cores} (物理核心: " . intval($cpu_cores/2) . ")");
        WP_CLI::log("总内存: " . round($memory_info['total'] / 1024, 2) . " GB");
        WP_CLI::log("最大并行进程数: {$this->server_config['max_parallel_processes']}");
        WP_CLI::log("每批处理类目数: {$this->server_config['batch_size']}");
        WP_CLI::log("CPU阈值: {$this->server_config['cpu_threshold']}%");
        WP_CLI::log("内存阈值: {$this->server_config['memory_threshold']}%");
        WP_CLI::log("========================");

        if ($verbose) {
            $current_usage = $this->get_system_usage();
            WP_CLI::log("当前CPU使用率: {$current_usage['cpu']}%");
            WP_CLI::log("当前内存使用率: {$current_usage['memory']}%");
        }
    }

    /**
     * 获取CPU核心数
     *
     * @return int CPU核心数
     */
    private function get_cpu_cores() {
        $cores = 1; // 默认值

        if (function_exists('shell_exec')) {
            // Linux系统
            $output = shell_exec('nproc 2>/dev/null');
            if ($output !== null) {
                $cores = intval(trim($output));
            } else {
                // 备用方法
                $output = shell_exec('grep -c ^processor /proc/cpuinfo 2>/dev/null');
                if ($output !== null) {
                    $cores = intval(trim($output));
                }
            }
        }

        return max(1, $cores);
    }

    /**
     * 获取内存信息
     *
     * @return array 内存信息数组
     */
    private function get_memory_info() {
        $memory_info = array(
            'total' => 0,
            'available' => 0,
            'used' => 0
        );

        if (function_exists('shell_exec')) {
            $output = shell_exec('free -m 2>/dev/null');
            if ($output !== null) {
                $lines = explode("\n", $output);
                foreach ($lines as $line) {
                    if (strpos($line, 'Mem:') === 0) {
                        $parts = preg_split('/\s+/', $line);
                        if (count($parts) >= 4) {
                            $memory_info['total'] = intval($parts[1]);
                            $memory_info['used'] = intval($parts[2]);
                            $memory_info['available'] = intval($parts[6] ?? $parts[3]);
                        }
                        break;
                    }
                }
            }
        }

        return $memory_info;
    }

    /**
     * 获取系统资源使用情况
     *
     * @return array 包含CPU和内存使用率的数组
     */
    private function get_system_usage() {
        $usage = array(
            'cpu' => 0,
            'memory' => 0
        );

        if (function_exists('shell_exec')) {
            // 获取CPU使用率
            $cpu_output = shell_exec("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null");
            if ($cpu_output !== null) {
                $usage['cpu'] = round(floatval(trim($cpu_output)), 1);
            }

            // 获取内存使用率
            $memory_info = $this->get_memory_info();
            if ($memory_info['total'] > 0) {
                $usage['memory'] = round(($memory_info['used'] / $memory_info['total']) * 100, 1);
            }
        }

        return $usage;
    }

    /**
     * 检查系统资源是否超过阈值
     *
     * @return array 检查结果
     */
    private function check_resource_limits() {
        $usage = $this->get_system_usage();
        $result = array(
            'cpu_ok' => $usage['cpu'] <= $this->server_config['cpu_threshold'],
            'memory_ok' => $usage['memory'] <= $this->server_config['memory_threshold'],
            'current_cpu' => $usage['cpu'],
            'current_memory' => $usage['memory']
        );

        return $result;
    }

    /**
     * 等待资源释放（优化版）
     *
     * @param bool $verbose 是否显示详细信息
     */
    private function wait_for_resources($verbose = false) {
        $max_wait_time = $this->server_config['max_wait_time']; // 使用配置的最大等待时间
        $wait_start = time();
        $check_interval = $this->server_config['quick_check_interval']; // 使用更快的检查间隔
        $consecutive_checks = 0;
        $required_consecutive_ok = 3; // 需要连续3次检查都正常才认为资源释放

        if ($verbose) {
            WP_CLI::log("⏳ 开始等待资源释放，最大等待时间: {$max_wait_time}秒");
        }

        while (time() - $wait_start < $max_wait_time) {
            $resource_check = $this->check_resource_limits();
            $wait_time = time() - $wait_start;

            if ($resource_check['cpu_ok'] && $resource_check['memory_ok']) {
                $consecutive_checks++;
                if ($consecutive_checks >= $required_consecutive_ok) {
                    if ($verbose) {
                        WP_CLI::log("✅ 资源使用率正常，继续处理... (等待了 {$wait_time}秒)");
                    }
                    return true;
                }
                if ($verbose) {
                    WP_CLI::log("🔄 资源使用率正常 ({$consecutive_checks}/{$required_consecutive_ok}) - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%");
                }
            } else {
                $consecutive_checks = 0; // 重置连续检查计数
                if ($verbose) {
                    WP_CLI::log("⚠️  资源使用率过高 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}% (已等待 {$wait_time}秒)");
                }
            }

            error_log("[资源控制] 等待资源释放 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%, 已等待: {$wait_time}秒");

            // 使用更智能的等待策略
            if ($this->server_config['adaptive_delay']) {
                // 自适应延迟：资源使用率越高，等待时间越长
                $cpu_factor = max(1, $resource_check['current_cpu'] / 50);
                $memory_factor = max(1, $resource_check['current_memory'] / 50);
                $adaptive_delay = $check_interval * max($cpu_factor, $memory_factor);
                usleep($adaptive_delay * 1000000); // 转换为微秒
            } else {
                sleep($check_interval);
            }
        }

        WP_CLI::warning("⏰ 等待资源释放超时 ({$max_wait_time}秒)，继续处理（请注意服务器性能）");
        return false;
    }

    /**
     * 计算自适应延迟时间
     *
     * @return float 延迟时间（秒）
     */
    private function calculate_adaptive_delay() {
        if (!$this->server_config['adaptive_delay']) {
            return $this->server_config['process_delay'];
        }

        $usage = $this->get_system_usage();
        $cpu_usage = $usage['cpu'];
        $memory_usage = $usage['memory'];

        // 基础延迟时间
        $base_delay = $this->server_config['process_delay'];

        // 根据资源使用率计算延迟系数
        if ($cpu_usage < 50 && $memory_usage < 50) {
            // 资源使用率很低，可以立即继续
            return 0;
        } elseif ($cpu_usage < 70 && $memory_usage < 70) {
            // 资源使用率适中，使用较短延迟
            return $base_delay * 0.5;
        } elseif ($cpu_usage < 85 && $memory_usage < 85) {
            // 资源使用率较高，使用标准延迟
            return $base_delay;
        } else {
            // 资源使用率很高，使用较长延迟
            $cpu_factor = max(1, $cpu_usage / 70);
            $memory_factor = max(1, $memory_usage / 70);
            return $base_delay * max($cpu_factor, $memory_factor);
        }
    }

    /**
     * 使用资源控制的批处理并行处理类目
     *
     * @param SS_SVG_Generator $generator 生成器实例
     * @param array $categories 要处理的类目数组
     * @param object $progress 进度条对象
     * @param int &$total_products 总产品数（引用）
     * @param int &$processed_categories 已处理类目数（引用）
     * @param int &$skipped_categories 已跳过类目数（引用）
     * @param bool $verbose 是否显示详细信息
     */
    private function process_categories_with_resource_control($generator, $categories, $progress, &$total_products, &$processed_categories, &$skipped_categories, $verbose = false) {
        $total_categories = count($categories);
        $batch_size = $this->server_config['batch_size'];
        $max_parallel = $this->server_config['max_parallel_processes'];

        WP_CLI::log("🚀 开始批处理：总共 {$total_categories} 个类目，每批 {$batch_size} 个，最大并行数 {$max_parallel}");
        error_log("[资源控制] 开始批处理：总共 {$total_categories} 个类目，每批 {$batch_size} 个，最大并行数 {$max_parallel}");

        // 将类目分批处理（恢复原始逻辑）
        $batches = array_chunk($categories, $batch_size);
        $batch_count = count($batches);

        foreach ($batches as $batch_index => $batch_categories) {
            $current_batch = $batch_index + 1;
            WP_CLI::log("📦 处理第 {$current_batch}/{$batch_count} 批，包含 " . count($batch_categories) . " 个类目");

            // 检查资源使用情况
            $resource_check = $this->check_resource_limits();
            if (!$resource_check['cpu_ok'] || !$resource_check['memory_ok']) {
                WP_CLI::log("资源使用率过高，等待资源释放...");
                $this->wait_for_resources($verbose);
            }

            // 处理当前批次
            $this->process_batch_parallel($generator, $batch_categories, $progress, $total_products, $processed_categories, $skipped_categories, $max_parallel, $verbose);

            // 智能批次间延迟
            if ($current_batch < $batch_count) {
                $delay = $this->calculate_adaptive_delay();
                if ($delay > 0) {
                    WP_CLI::log("📊 批次处理完成，智能延迟 {$delay} 秒后继续...");
                    if ($delay < 1) {
                        usleep($delay * 1000000); // 微秒延迟
                    } else {
                        sleep($delay);
                    }
                } else {
                    WP_CLI::log("🚀 资源充足，立即继续下一批次...");
                }
            }
        }

        WP_CLI::log("所有批次处理完成");
        error_log("[资源控制] 所有批次处理完成");
    }

    /**
     * 获取所有有模板的产品类目
     */
    private function get_all_product_categories_with_templates() {
        return get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'meta_query' => array(
                array(
                    'key' => 'ss_selected_templates',
                    'compare' => 'EXISTS'
                )
            )
        ));
    }

    /**
     * 清理指定类目的产品数据
     */
    public function clean_category_products($category_id, $dry_run = false, $include_json_records = true) {
        $stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0,
            'errors' => 0
        );

        // 获取类目下的所有产品
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                )
            )
        ));

        if (empty($products)) {
            $this->log_message("没有找到产品");
            return $stats;
        }

        // 获取模板产品列表（需要保护）
        $template_products = $this->get_template_products_in_category($products);
        $template_image_ids = $this->get_template_image_ids($template_products);

        $this->log_message(sprintf("找到 %d 个产品，其中 %d 个模板产品", count($products), count($template_products)));

        // 分离生成产品和模板产品
        $generated_products = array();
        foreach ($products as $product) {
            if (!in_array($product->ID, $template_products)) {
                $generated_products[] = $product;
            }
        }

        $this->log_message(sprintf("将删除 %d 个生成产品", count($generated_products)));

        if (empty($generated_products)) {
            $this->log_message("没有需要删除的生成产品，但仍需检查文件夹");
            // 🔥 修复：即使没有产品也要检查和清理文件夹
        } else {
            // 创建进度条（兼容并行进程）
            if (defined('SS_PARALLEL_PROCESS')) {
                // 并行进程中的简化进度条
                $progress = new class {
                    public function tick() {}
                    public function finish() {}
                };
            } else {
                // 检查WP_CLI类是否存在，避免并行进程中的错误
                if (class_exists('WP_CLI')) {
                    $progress = \WP_CLI\Utils\make_progress_bar('删除产品', count($generated_products));
                } else {
                    // 如果WP_CLI不存在，使用简化进度条
                    $progress = new class {
                        public function tick() {}
                        public function finish() {}
                    };
                }
            }

            // 删除生成产品
            foreach ($generated_products as $product) {
                try {
                    $product_stats = $this->delete_generated_product($product->ID, $template_image_ids, $dry_run);
                    $stats['products_deleted'] += $product_stats['products_deleted'];
                    $stats['images_deleted'] += $product_stats['images_deleted'];
                    $stats['files_deleted'] += $product_stats['files_deleted'];
                } catch (Exception $e) {
                    $stats['errors']++;
                    $this->log_message("删除产品 {$product->ID} 时出错: " . $e->getMessage(), 'warning');
                }

                $progress->tick();
            }

            $progress->finish();
        }



        // 清理类目相关的文件夹
        if (!$dry_run) {
            $file_stats = $this->clean_category_folders($category_id);
            $stats['files_deleted'] += $file_stats;

            // 【新增】清理类目的JSON记录文件（可选）
            if ($include_json_records) {
                $json_cleanup_result = $this->clean_category_json_record($category_id);
                if ($json_cleanup_result['success']) {
                    $stats['files_deleted'] += $json_cleanup_result['files_deleted'];
                    WP_CLI::log("✅ " . $json_cleanup_result['message']);
                } else {
                    WP_CLI::warning("❌ JSON记录清理失败: " . $json_cleanup_result['message']);
                    $stats['errors']++;
                }
            } else {
                WP_CLI::log("ℹ️  跳过JSON记录文件清理（--include-json-records=false）");
            }

            // 【新增】清理已处理的SVG文件记录
            $this->clear_processed_svg_records($category_id);

            // 【新增】清理类目的产品生成标志
            $this->clear_category_generation_flags($category_id);
        } else {
            // 🔥 新增：dry-run模式下预览将要清理的文件
            $file_stats = $this->preview_category_folders($category_id);
            $stats['files_deleted'] += $file_stats;

            // 【新增】预览JSON记录文件清理（可选）
            if ($include_json_records) {
                $json_preview_result = $this->preview_category_json_record($category_id);
                if ($json_preview_result['exists']) {
                    $stats['files_deleted'] += 1;
                    WP_CLI::log("  📄 " . $json_preview_result['message']);
                } else {
                    WP_CLI::log("  ℹ️  " . $json_preview_result['message']);
                }
            } else {
                WP_CLI::log("  ℹ️  跳过JSON记录文件清理预览（--include-json-records=false）");
            }
        }

        return $stats;
    }

    /**
     * 获取模板产品列表
     */
    public function get_template_products_in_category($products) {
        $template_products = array();
        foreach ($products as $product) {
            if (get_post_meta($product->ID, '_ss_is_template', true) === '1') {
                $template_products[] = $product->ID;
            }
        }
        return $template_products;
    }

    /**
     * 获取模板产品的图片ID列表（需要保护）
     */
    public function get_template_image_ids($template_products) {
        $protected_image_ids = array();

        foreach ($template_products as $product_id) {
            $product = wc_get_product($product_id);
            if (!$product) continue;

            // 主图
            $image_id = $product->get_image_id();
            if ($image_id) {
                $protected_image_ids[] = $image_id;
            }

            // 画廊图片
            $gallery_ids = $product->get_gallery_image_ids();
            if (!empty($gallery_ids)) {
                $protected_image_ids = array_merge($protected_image_ids, $gallery_ids);
            }

            // 模板图片
            $template_image_id = get_post_meta($product_id, '_ss_template_image_id', true);
            if ($template_image_id) {
                $protected_image_ids[] = intval($template_image_id);
            }

            // Gallery模板图片
            $gallery_templates = get_post_meta($product_id, '_ss_gallery_templates', true);
            if (is_array($gallery_templates)) {
                foreach ($gallery_templates as $template) {
                    if (isset($template['image_id'])) {
                        $protected_image_ids[] = intval($template['image_id']);
                    }
                }
            }
        }

        return array_unique($protected_image_ids);
    }

    /**
     * 并行处理单个批次的类目（性能监控版）
     *
     * @param SS_SVG_Generator $generator 生成器实例
     * @param array $batch_categories 当前批次的类目数组
     * @param object $progress 进度条对象
     * @param int &$total_products 总产品数（引用）
     * @param int &$processed_categories 已处理类目数（引用）
     * @param int &$skipped_categories 已跳过类目数（引用）
     * @param int $max_parallel 最大并行数
     * @param bool $verbose 是否显示详细信息
     */
    private function process_batch_parallel($generator, $batch_categories, $progress, &$total_products, &$processed_categories, &$skipped_categories, $max_parallel, $verbose = false) {
        // 性能监控变量
        $batch_start_time = microtime(true);
        $process_creation_time = 0;
        $wp_load_time = 0;
        $actual_work_time = 0;
        $wait_time = 0;

        // 🔥 关键修复：恢复到每任务创建Runtime的方式（避免进程池阻塞）
        // 进程池复用会导致Runtime阻塞，限制实际并行数
        WP_CLI::log("🚀 使用动态Runtime创建，支持真正的 {$max_parallel} 个并行进程");

        $runtimes = [];
        $futures = [];
        $active_processes = 0;
        $category_queue = $batch_categories;
        $results = [];
        $total_tasks_started = 0;
        $total_tasks_completed = 0;
        $task_start_times = []; // 记录每个任务的启动时间
        $last_slot_check = microtime(true); // 上次槽位检查时间

        WP_CLI::log("📊 开始处理批次：{$max_parallel} 个并行槽位，" . count($batch_categories) . " 个任务");
        WP_CLI::log("🎯 目标：保持 {$max_parallel} 个进程槽位始终满载，动态补位新任务");

        while (!empty($category_queue) || $active_processes > 0) {
            $loop_start_time = microtime(true);

            // 🔥 优化：积极填充所有空闲槽位
            $available_slots = $max_parallel - $active_processes;
            $tasks_to_start = min($available_slots, count($category_queue));

            if ($tasks_to_start > 0) {
                WP_CLI::log("⚡ 检测到 {$available_slots} 个空闲槽位，准备启动 {$tasks_to_start} 个新任务");
            }

            // 启动新的并行进程（填满所有空闲槽位）
            for ($i = 0; $i < $tasks_to_start; $i++) {
                $category = array_shift($category_queue);
                $task_start_time = microtime(true);

                WP_CLI::log("🚀 启动任务 #{$total_tasks_started}: 类目 \"{$category->name}\" (活跃进程: {$active_processes}/{$max_parallel})");

                try {
                    // 🔥 关键修复：为每个任务创建独立的Runtime（避免阻塞）
                    $runtime = new \parallel\Runtime();
                    $total_tasks_started++;

                    // 记录任务启动时间，用于性能监控
                    $task_start_times[$category->term_id] = microtime(true);

                    // 创建并行任务（性能监控版）
                    $futures[$category->term_id] = $runtime->run(function($term_id, $term_name) {
                        // 在子进程中重新初始化 WordPress（性能监控版）
                        try {
                            $total_start_time = microtime(true);
                            $wp_load_start = microtime(true);

                            // 快速WordPress环境初始化
                            if (!defined('ABSPATH')) {
                                define('ABSPATH', '/www/wwwroot/matchbeast.com/');
                            }

                            // 禁用不必要的钩子和插件，提高加载速度
                            define('WP_USE_THEMES', false);
                            define('SHORTINIT', false);

                            if (file_exists(ABSPATH . 'wp-load.php')) {
                                require_once ABSPATH . 'wp-load.php';
                            }

                            $wp_load_time = microtime(true) - $wp_load_start;

                            // 确保插件文件已加载
                            if (!class_exists('SS_SVG_Generator')) {
                                $plugin_file = ABSPATH . 'wp-content/plugins/shoe-svg-generator/shoe-svg-generator.php';
                                if (file_exists($plugin_file)) {
                                    require_once $plugin_file;
                                }
                            }

                            // 获取插件实例
                            $generator = SS_SVG_Generator::get_instance();

                            // 获取类目对象
                            $term = get_term($term_id, 'product_cat');
                            if (!$term || is_wp_error($term)) {
                                return [
                                    'category_id' => $term_id,
                                    'category_name' => $term_name,
                                    'success' => false,
                                    'message' => "无法获取类目信息",
                                    'products' => []
                                ];
                            }

                            // 确保_ss_completed_svg_files元数据存在且为数组
                            $completed_files = get_term_meta($term_id, '_ss_completed_svg_files', true);
                            if (!is_array($completed_files)) {
                                $empty_array = array();
                                $update_result = update_term_meta($term_id, '_ss_completed_svg_files', $empty_array);
                                error_log("[资源控制][并行] 初始化类目 '" . $term->name . "' (ID: " . $term_id . ") 的_ss_completed_svg_files元数据");
                            }

                        // 检查类目是否有预设颜色
                        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
                        if (empty($preset_colors)) {
                            error_log("[资源控制][并行] 类目 '" . $term->name . "' (ID: " . $term_id . ") 没有预设颜色，尝试提取颜色");

                            // 获取分类图片ID
                            $thumbnail_id = get_term_meta($term_id, 'thumbnail_id', true);

                            if (!empty($thumbnail_id)) {
                                // 获取图片路径
                                $image_path = get_attached_file($thumbnail_id);

                                if (file_exists($image_path)) {
                                    // 确保颜色分析类已加载
                                    if (!class_exists('SS_Color_Analyzer')) {
                                        require_once plugin_dir_path(__FILE__) . 'includes/class-ss-color-analyzer.php';
                                    }

                                    // 使用颜色分析类提取颜色
                                    $color_analyzer = new SS_Color_Analyzer();
                                    $extracted_colors = $color_analyzer->analyze_image_colors($image_path, 10);

                                    if (!empty($extracted_colors)) {
                                        // 保存提取的颜色
                                        update_term_meta($term_id, 'ss_category_colors', $extracted_colors);
                                        error_log("[资源控制][并行] 成功提取颜色：" . implode(', ', $extracted_colors));
                                    } else {
                                        // 如果提取失败，使用默认颜色
                                        $default_colors = $color_analyzer->get_default_colors();
                                        update_term_meta($term_id, 'ss_category_colors', $default_colors);
                                        error_log("[资源控制][并行] 使用默认颜色：" . implode(', ', $default_colors));
                                    }
                                }
                            }
                        }

                        // 记录实际工作开始时间
                        $work_start_time = microtime(true);

                        // 生成产品
                        $result = $generator->ss_generate_products_for_category($term);

                        // 记录结束时间
                        $work_end_time = microtime(true);
                        $work_duration = round($work_end_time - $work_start_time, 2);
                        $total_duration = round($work_end_time - $total_start_time, 2);

                            // 返回详细的性能数据
                            return [
                                'category_id' => $term_id,
                                'category_name' => $term_name,
                                'success' => is_array($result) && !empty($result),
                                'work_duration' => $work_duration,
                                'wp_load_time' => $wp_load_time,
                                'total_duration' => $total_duration,
                                'products' => is_array($result) ? $result : [],
                                'product_count' => is_array($result) ? count($result) : 0
                            ];

                        } catch (\Throwable $e) {
                            // 并行进程中的错误处理
                            error_log("[资源控制][并行][错误] 类目 {$term_id} 处理失败: " . $e->getMessage());
                            return [
                                'category_id' => $term_id,
                                'category_name' => $term_name,
                                'success' => false,
                                'duration' => 0,
                                'products' => [],
                                'product_count' => 0,
                                'message' => $e->getMessage()
                            ];
                        }
                    }, [$category->term_id, $category->name]);

                    // 保存运行时
                    $runtimes[$category->term_id] = $runtime;
                    $active_processes++;

                } catch (\Throwable $e) {
                    WP_CLI::warning("为类目 \"{$category->name}\" 创建并行任务失败: " . $e->getMessage());
                    error_log("[资源控制][错误] 创建并行任务失败: " . $e->getMessage());
                    $skipped_categories++;
                    $progress->tick();

                    // 清理失败任务的启动时间记录
                    if (isset($task_start_times[$category->term_id])) {
                        unset($task_start_times[$category->term_id]);
                    }
                }
            }

            // 🔥 优化：更频繁的槽位利用率检查
            $current_time = microtime(true);
            if ($current_time - $last_slot_check > 0.1) { // 每100ms检查一次
                $slot_utilization = ($active_processes / $max_parallel) * 100;
                if ($slot_utilization < 95 && !empty($category_queue)) {
                    $available_slots = $max_parallel - $active_processes;
                    WP_CLI::log("📊 槽位利用率: " . round($slot_utilization, 1) . "% ({$active_processes}/{$max_parallel})，{$available_slots} 个槽位可用");
                }
                $last_slot_check = $current_time;
            }

            // 检查已完成的进程
            foreach ($futures as $category_id => $future) {
                if ($future->done()) {
                    try {
                        // 获取结果
                        $result = $future->value();
                        $results[$category_id] = $result;

                        // 更新进度条
                        $progress->tick();

                        $total_tasks_completed++;

                        if ($result['success']) {
                            $processed_categories++;
                            $total_products += $result['product_count'];

                            // 显示详细的性能数据
                            $wp_load_time = isset($result['wp_load_time']) ? $result['wp_load_time'] : 0;
                            $work_duration = isset($result['work_duration']) ? $result['work_duration'] : $result['total_duration'];
                            $total_duration = isset($result['total_duration']) ? $result['total_duration'] : $work_duration;

                            WP_CLI::log("✅ 任务 #{$total_tasks_completed}: \"{$result['category_name']}\" - {$result['product_count']} 产品");
                            WP_CLI::log("   ⏱️  总耗时: {$total_duration}s | WP加载: " . round($wp_load_time, 2) . "s | 实际工作: {$work_duration}s");

                            // 累计性能数据
                            $wp_load_time += $wp_load_time;
                            $actual_work_time += $work_duration;

                            error_log("[资源控制] 成功处理类目 '" . $result['category_name'] . "'，生成 " . $result['product_count'] . " 个产品");
                        } else {
                            $skipped_categories++;
                            WP_CLI::warning("❌ 任务 #{$total_tasks_completed}: \"{$result['category_name']}\" 处理失败");
                            error_log("[资源控制] 类目 '" . $result['category_name'] . "' 处理失败");
                        }

                    } catch (\Throwable $e) {
                        $error_message = "处理类目 ID: {$category_id} 时出错: " . $e->getMessage();
                        WP_CLI::warning($error_message);
                        error_log("[资源控制][错误] " . $error_message);
                        $skipped_categories++;
                        $progress->tick();
                    }

                    // 清理已完成的进程
                    unset($futures[$category_id]);
                    if (isset($runtimes[$category_id])) {
                        unset($runtimes[$category_id]);
                    }
                    if (isset($task_start_times[$category_id])) {
                        unset($task_start_times[$category_id]);
                    }
                    $active_processes--;

                    // 🔥 优化：任务完成后立即检查是否可以启动新任务
                    if (!empty($category_queue)) {
                        WP_CLI::log("⚡ 任务完成，立即检查新任务启动机会...");
                    }

                    // 定期清理内存，避免内存泄漏
                    if ($total_tasks_completed % 10 == 0) {
                        if (function_exists('gc_collect_cycles')) {
                            gc_collect_cycles();
                        }
                    }
                }
            }

            // 🔥 优化：更激进的动态休眠策略（确保槽位满载）
            $slot_utilization = ($active_processes / $max_parallel) * 100;

            if ($slot_utilization >= 95) {
                // 槽位几乎满载，适中检查频率
                usleep(50000); // 50ms
            } elseif ($slot_utilization >= 80) {
                // 槽位利用率较高，稍快检查
                usleep(20000); // 20ms
            } elseif (!empty($category_queue)) {
                // 有待处理任务但槽位未满，极快检查
                usleep(5000); // 5ms - 极快补位
            } else {
                // 无待处理任务，标准检查
                usleep(100000); // 100ms
            }

            // 更智能的资源检查
            static $last_resource_check = 0;
            $check_interval = $this->server_config['resource_check_interval'];

            if (time() - $last_resource_check > $check_interval) {
                $resource_check = $this->check_resource_limits();

                // 显示详细的实时监控信息
                $status_icon = ($resource_check['cpu_ok'] && $resource_check['memory_ok']) ? "✅" : "⚠️";
                $batch_elapsed = microtime(true) - $batch_start_time;
                $avg_task_time = $total_tasks_completed > 0 ? $batch_elapsed / $total_tasks_completed : 0;

                WP_CLI::log("{$status_icon} 实时监控 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%");
                WP_CLI::log("   🔄 活跃进程: {$active_processes}/{$max_parallel} | 已完成: {$total_tasks_completed} | 队列: " . count($category_queue));
                WP_CLI::log("   ⚡ 平均任务时间: " . round($avg_task_time, 2) . "s | 批次运行: " . round($batch_elapsed, 1) . "s");

                // 🔥 新增：槽位利用率监控
                $slot_utilization = ($active_processes / $max_parallel) * 100;
                $utilization_icon = $slot_utilization >= 95 ? "🔥" : ($slot_utilization >= 80 ? "⚡" : "⚠️");
                WP_CLI::log("   {$utilization_icon} 槽位利用率: " . round($slot_utilization, 1) . "% | 目标: 保持95%+");

                if ($verbose && $total_tasks_completed > 0) {
                    $avg_wp_load = $wp_load_time / $total_tasks_completed;
                    $avg_work = $actual_work_time / $total_tasks_completed;
                    WP_CLI::log("   📊 平均WP加载: " . round($avg_wp_load, 2) . "s | 平均工作: " . round($avg_work, 2) . "s");
                }

                if (!$resource_check['cpu_ok'] || !$resource_check['memory_ok']) {
                    // 只有在资源使用率真的很高时才暂停
                    if ($resource_check['current_cpu'] > 95 || $resource_check['current_memory'] > 95) {
                        WP_CLI::log("🔴 资源使用率极高，暂停启动新任务...");
                        error_log("[资源控制] 资源使用率极高，暂停启动新任务 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%");
                        $this->wait_for_resources($verbose);
                    } else {
                        // 轻微超标时只是警告，不暂停
                        WP_CLI::log("⚠️  资源使用率较高，继续监控 - CPU: {$resource_check['current_cpu']}%, 内存: {$resource_check['current_memory']}%");
                    }
                } elseif ($resource_check['current_cpu'] < 30 && $resource_check['current_memory'] < 60) {
                    // CPU使用率过低，可能存在瓶颈
                    WP_CLI::warning("⚠️  CPU使用率过低 ({$resource_check['current_cpu']}%)，可能存在瓶颈:");

                    if ($active_processes < $max_parallel * 0.5) {
                        WP_CLI::log("   🔍 活跃进程数过低 ({$active_processes}/{$max_parallel})，可能原因:");
                        WP_CLI::log("     - WordPress环境加载过慢");
                        WP_CLI::log("     - 数据库连接瓶颈");
                        WP_CLI::log("     - 文件I/O瓶颈");
                    }

                    if ($total_tasks_completed > 0) {
                        $avg_wp_load = $wp_load_time / $total_tasks_completed;
                        $avg_work = $actual_work_time / $total_tasks_completed;

                        if ($avg_wp_load > 2.0) {
                            WP_CLI::warning("     - WordPress加载过慢 (平均 " . round($avg_wp_load, 2) . "s)");
                        }
                        if ($avg_work > 10.0) {
                            WP_CLI::warning("     - 产品生成过慢 (平均 " . round($avg_work, 2) . "s)");
                        }
                    }
                }

                $last_resource_check = time();
            }
        }

        // 批次处理完成，显示性能总结
        $batch_total_time = microtime(true) - $batch_start_time;
        WP_CLI::log("\n📊 批次性能总结:");
        WP_CLI::log("   ⏱️  批次总耗时: " . round($batch_total_time, 2) . "s");
        WP_CLI::log("   🎯 完成任务: {$total_tasks_completed} | 平均: " . round($batch_total_time / max(1, $total_tasks_completed), 2) . "s/任务");

        if ($total_tasks_completed > 0) {
            $avg_wp_load = $wp_load_time / $total_tasks_completed;
            $avg_work = $actual_work_time / $total_tasks_completed;
            $wp_load_percentage = ($wp_load_time / $batch_total_time) * 100;
            $work_percentage = ($actual_work_time / $batch_total_time) * 100;

            WP_CLI::log("   📈 时间分布:");
            WP_CLI::log("     - WP加载: " . round($avg_wp_load, 2) . "s/任务 (" . round($wp_load_percentage, 1) . "%)");
            WP_CLI::log("     - 实际工作: " . round($avg_work, 2) . "s/任务 (" . round($work_percentage, 1) . "%)");

            // 性能建议
            if ($wp_load_percentage > 30) {
                WP_CLI::warning("   💡 建议: WordPress加载时间占比过高，考虑优化WordPress环境");
            }
            if ($work_percentage < 50) {
                WP_CLI::warning("   💡 建议: 实际工作时间占比过低，存在性能瓶颈");
            }
        }
    }

    /**
     * 删除单个生成产品
     */
    public function delete_generated_product($product_id, $protected_image_ids, $dry_run = false) {
        $stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0
        );

        $product = wc_get_product($product_id);
        if (!$product) {
            return $stats;
        }

        // 收集产品的图片
        $product_image_ids = array();

        // 主图
        $image_id = $product->get_image_id();
        if ($image_id && !in_array($image_id, $protected_image_ids)) {
            $product_image_ids[] = $image_id;
        }

        // 画廊图片
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ($gallery_ids as $gallery_id) {
            if (!in_array($gallery_id, $protected_image_ids)) {
                $product_image_ids[] = $gallery_id;
            }
        }

        if ($dry_run) {
            $stats['products_deleted'] = 1;
            $stats['images_deleted'] = count($product_image_ids);
            return $stats;
        }

        // 删除图片
        foreach ($product_image_ids as $image_id) {
            if (wp_delete_attachment($image_id, true)) {
                $stats['images_deleted']++;
            }
        }

        // 删除产品
        if (wp_delete_post($product_id, true)) {
            $stats['products_deleted'] = 1;
        }

        return $stats;
    }

    /**
     * 清理类目相关的文件夹
     */
    public function clean_category_folders($category_id) {
        $files_deleted = 0;
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return $files_deleted;
        }

        // 🔥 修复：生成不同格式的文件夹名称
        $svg_folder_name = $this->generate_category_safe_name($term->name);  // 连字符格式，用于SVG
        $product_folder_name = $this->generate_product_folder_name($term->name);  // 下划线格式，用于产品
        $upload_dir = wp_upload_dir();

        $this->log_message("  🔍 清理类目文件: {$term->name}");
        $this->log_message("    SVG文件夹格式: {$svg_folder_name}");
        $this->log_message("    产品文件夹格式: {$product_folder_name}");

        // 🔥 修复：清理final-products文件夹（使用正确的下划线格式）
        $final_products_dir = $upload_dir['basedir'] . '/shoe-svg-generator/final-products/' . $product_folder_name;
        if (is_dir($final_products_dir)) {
            $files_count = $this->count_files_in_directory($final_products_dir);
            $this->remove_directory($final_products_dir);
            $files_deleted += $files_count;
            $this->log_message("  ✅ 已清理产品文件夹: {$product_folder_name} ({$files_count} 个文件)");
        } else {
            $this->log_message("  ℹ️  产品文件夹不存在: {$product_folder_name}");
        }

        // 🔥 修复：清理frontend-gallery文件夹（包含预览图和产品图）
        // 【修复】确保不删除已生成产品使用的预览图
        $frontend_gallery_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $svg_folder_name;
        error_log("[清理调试][修复] 检查前端Gallery目录: {$frontend_gallery_dir}");
        if (is_dir($frontend_gallery_dir)) {
            $gallery_files_count = $this->count_files_in_directory($frontend_gallery_dir);
            error_log("[清理调试][修复] 前端Gallery目录存在，包含 {$gallery_files_count} 个文件");

            // 【修复】检查是否有已生成产品使用此类目的预览图
            $has_generated_products = $this->check_category_has_generated_products($svg_folder_name);

            if ($has_generated_products) {
                error_log("[清理调试][修复] 类目 '{$svg_folder_name}' 有已生成产品，保护预览图不被删除");
                $this->log_message("  🛡️  保护前端Gallery文件夹: {$svg_folder_name} (包含已生成产品的预览图)");
            } else {
                $this->remove_directory($frontend_gallery_dir);
                $files_deleted += $gallery_files_count;
                $this->log_message("  ✅ 已清理前端Gallery文件夹: {$svg_folder_name} ({$gallery_files_count} 个文件，包含预览图)");
                error_log("[清理调试][修复] 前端Gallery目录清理完成");
            }
        } else {
            $this->log_message("  ℹ️  前端Gallery文件夹不存在: {$svg_folder_name}");
            error_log("[清理调试][修复] 前端Gallery目录不存在: {$frontend_gallery_dir}");
        }

        // 🔥 优化：清理processed_svg文件夹中的类目相关SVG填色文件
        $processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
        if (is_dir($processed_svg_dir)) {
            $this->log_message("  🔍 检查SVG填色文件目录: {$processed_svg_dir}");

            // 1. 清理完全匹配的SVG填色文件夹（使用连字符格式）
            $exact_svg_dir = $processed_svg_dir . $svg_folder_name;
            if (is_dir($exact_svg_dir)) {
                $svg_files_count = $this->count_files_in_directory($exact_svg_dir);
                $this->remove_directory($exact_svg_dir);
                $files_deleted += $svg_files_count;
                $this->log_message("  ✅ 已清理SVG填色文件夹: {$svg_folder_name} ({$svg_files_count} 个文件)");
            } else {
                $this->log_message("  ℹ️  SVG填色文件夹不存在: {$svg_folder_name}");
            }

            // 2. 查找以类目名称开头的SVG变体文件夹
            $category_svg_pattern = $processed_svg_dir . $svg_folder_name . '*';
            $category_svg_dirs = glob($category_svg_pattern, GLOB_ONLYDIR);

            foreach ($category_svg_dirs as $svg_dir) {
                if (is_dir($svg_dir) && $svg_dir !== $exact_svg_dir) { // 避免重复删除
                    $svg_files_count = $this->count_files_in_directory($svg_dir);
                    $this->remove_directory($svg_dir);
                    $files_deleted += $svg_files_count;
                    $this->log_message("  ✅ 已清理SVG变体文件夹: " . basename($svg_dir) . " ({$svg_files_count} 个文件)");
                }
            }

            // 3. 清理直接的SVG文件（如果有的话）
            $svg_files = glob($processed_svg_dir . $svg_folder_name . '*.svg');
            foreach ($svg_files as $svg_file) {
                if (unlink($svg_file)) {
                    $files_deleted++;
                    $this->log_message("  ✅ 已删除SVG文件: " . basename($svg_file));
                }
            }

            // 4. 清理以类目名称开头的其他文件
            $other_files = glob($processed_svg_dir . $svg_folder_name . '*');
            foreach ($other_files as $file) {
                if (is_file($file)) {
                    if (unlink($file)) {
                        $files_deleted++;
                        $this->log_message("  ✅ 已删除相关文件: " . basename($file));
                    }
                }
            }

            // 5. 显示清理总结
            $remaining_dirs = glob($processed_svg_dir . $svg_folder_name . '*', GLOB_ONLYDIR);
            $remaining_files = glob($processed_svg_dir . $svg_folder_name . '*');
            $remaining_files = array_filter($remaining_files, 'is_file');

            if (empty($remaining_dirs) && empty($remaining_files)) {
                $this->log_message("  ✅ SVG填色文件清理完成，无残留文件");
            } else {
                $this->log_message("  ⚠️  检测到残留文件: " . count($remaining_dirs) . " 个目录, " . count($remaining_files) . " 个文件", 'warning');
            }
        }

        // 注意：所有产品相关文件（包括gallery、previews等）都已包含在final-products文件夹中

        return $files_deleted;
    }

    /**
     * 清理类目的JSON记录文件
     *
     * @param int $category_id 类目ID
     * @return array 操作结果
     */
    public function clean_category_json_record($category_id) {
        error_log("[清理JSON] 开始清理类目 {$category_id} 的JSON记录文件");

        if (!class_exists('SS_SVG_File_Manager')) {
            return array(
                'success' => false,
                'message' => 'SS_SVG_File_Manager 类不存在',
                'files_deleted' => 0
            );
        }

        $svg_file_manager = SS_SVG_File_Manager::get_instance();
        $result = $svg_file_manager->delete_category_json_record($category_id);

        if ($result['success']) {
            error_log("[清理JSON] 成功清理类目 {$category_id} 的JSON记录: " . $result['message']);
            return array(
                'success' => true,
                'message' => $result['message'],
                'files_deleted' => 1
            );
        } else {
            error_log("[清理JSON] 清理类目 {$category_id} 的JSON记录失败: " . $result['message']);
            return array(
                'success' => false,
                'message' => $result['message'],
                'files_deleted' => 0
            );
        }
    }

    /**
     * 预览类目的JSON记录文件清理（dry-run模式）
     *
     * @param int $category_id 类目ID
     * @return array 预览结果
     */
    public function preview_category_json_record($category_id) {
        if (!class_exists('SS_SVG_File_Manager')) {
            return array(
                'exists' => false,
                'message' => 'SS_SVG_File_Manager 类不存在，无法预览JSON记录清理'
            );
        }

        $svg_file_manager = SS_SVG_File_Manager::get_instance();
        $existing_record = $svg_file_manager->get_category_json_record($category_id);

        if ($existing_record) {
            $term = get_term($category_id, 'product_cat');
            $safe_category_name = $term ? sanitize_title($term->name) : 'unknown';
            $json_filename = "category_{$category_id}_{$safe_category_name}.json";

            return array(
                'exists' => true,
                'message' => "将清理JSON记录文件: {$json_filename} (包含 {$existing_record['total_files']} 个文件记录)"
            );
        } else {
            return array(
                'exists' => false,
                'message' => 'JSON记录文件不存在，无需清理'
            );
        }
    }

    /**
     * 清理类目的已处理SVG文件记录
     *
     * @param int $category_id 类目ID
     * @return bool
     */
    public function clear_processed_svg_records($category_id) {
        error_log("[清理SVG记录] 开始清理类目 {$category_id} 的已处理SVG文件记录");

        // 清理term meta中的已处理SVG文件列表
        $result = delete_term_meta($category_id, '_ss_processed_svg_files');

        if ($result !== false) {
            error_log("[清理SVG记录] 成功清理类目 {$category_id} 的已处理SVG文件记录");
            WP_CLI::log("✅ 已清理类目的已处理SVG文件记录");
            return true;
        } else {
            error_log("[清理SVG记录] 清理类目 {$category_id} 的已处理SVG文件记录失败或记录不存在");
            WP_CLI::log("ℹ️  类目的已处理SVG文件记录不存在或已清理");
            return true; // 不存在也算成功
        }
    }

    /**
     * 【修复B】清理类目的产品生成标志 - 使用正确的清理逻辑
     */
    public function clear_category_generation_flags($category_id) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            $this->log_message("✗ 无法找到类目ID {$category_id}", 'warning');
            return false;
        }

        $this->log_message("清理类目产品生成标志: {$term->name} (ID: {$category_id})");

        // 【修复】使用现有的clear_flags_for_category方法，传递正确的参数
        $result = $this->clear_flags_for_category($term, true); // 启用详细日志

        if ($result) {
            $this->log_message("✓ 已成功清理类目 '{$term->name}' 的所有产品生成标志");

            // 验证清理结果
            $wp_cli_flag = get_term_meta($category_id, 'ss_wp_cli_products_generated', true);
            $backend_flag = get_term_meta($category_id, 'ss_backend_products_generated', true);
            $frontend_flag = get_term_meta($category_id, 'ss_frontend_preview_generated', true);

            if (empty($wp_cli_flag) && empty($backend_flag) && empty($frontend_flag)) {
                $this->log_message("✓ 验证通过：所有产品生成标志已清理");
            } else {
                $this->log_message("⚠ 验证失败：部分标志可能未完全清理", 'warning');
                $this->log_message("  - WP CLI标志: " . ($wp_cli_flag ? '仍存在' : '已清理'));
                $this->log_message("  - 后台标志: " . ($backend_flag ? '仍存在' : '已清理'));
                $this->log_message("  - 前端标志: " . ($frontend_flag ? '仍存在' : '已清理'));
            }
        } else {
            $this->log_message("✗ 清理类目 '{$term->name}' 的产品生成标志失败", 'warning');
        }

        return $result;
    }

    /**
     * 显示性能统计信息
     *
     * @param int $total_categories 总类目数
     * @param int $processed_categories 已处理类目数
     * @param int $total_products 总产品数
     * @param float $start_time 开始时间
     */
    private function display_performance_stats($total_categories, $processed_categories, $total_products, $start_time) {
        $current_time = microtime(true);
        $elapsed_time = $current_time - $start_time;
        $usage = $this->get_system_usage();

        $categories_per_minute = $elapsed_time > 0 ? ($processed_categories / $elapsed_time) * 60 : 0;
        $products_per_minute = $elapsed_time > 0 ? ($total_products / $elapsed_time) * 60 : 0;
        $estimated_remaining = $categories_per_minute > 0 ? (($total_categories - $processed_categories) / $categories_per_minute) : 0;

        WP_CLI::log("📈 性能统计:");
        WP_CLI::log("   ⏱️  已运行: " . gmdate("H:i:s", $elapsed_time));
        WP_CLI::log("   📊 处理速度: " . round($categories_per_minute, 1) . " 类目/分钟, " . round($products_per_minute, 1) . " 产品/分钟");
        WP_CLI::log("   🎯 进度: {$processed_categories}/{$total_categories} 类目 ({$total_products} 产品)");
        WP_CLI::log("   ⏳ 预计剩余: " . gmdate("H:i:s", $estimated_remaining * 60));
        WP_CLI::log("   💻 资源使用: CPU {$usage['cpu']}%, 内存 {$usage['memory']}%");

        // 记录到日志
        error_log("[性能统计] 已处理 {$processed_categories}/{$total_categories} 类目, 生成 {$total_products} 产品, 速度: " . round($categories_per_minute, 1) . " 类目/分钟");
    }

    /**
     * 计算目录中的文件数量
     */
    private function count_files_in_directory($dir) {
        $count = 0;
        if (!is_dir($dir)) {
            return $count;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * 递归删除目录
     */
    private function remove_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->remove_directory($path);
            } else {
                unlink($path);
            }
        }
        return rmdir($dir);
    }

    /**
     * 预览将要清理的类目文件夹（dry-run模式）
     */
    public function preview_category_folders($category_id) {
        $files_count = 0;
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return $files_count;
        }

        $safe_category_name = $this->generate_category_safe_name($term->name);
        $upload_dir = wp_upload_dir();

        WP_CLI::log("  🔍 预览清理类目文件: {$term->name} → {$safe_category_name}");

        // 检查SVG填色文件夹
        $processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
        if (is_dir($processed_svg_dir)) {
            $exact_category_dir = $processed_svg_dir . $safe_category_name;
            if (is_dir($exact_category_dir)) {
                $svg_files_count = $this->count_files_in_directory($exact_category_dir);
                $files_count += $svg_files_count;
                WP_CLI::log("  📁 将清理SVG填色文件夹: {$safe_category_name} ({$svg_files_count} 个文件)");
            }

            // 检查变体文件夹
            $category_svg_dirs = glob($processed_svg_dir . $safe_category_name . '*', GLOB_ONLYDIR);
            foreach ($category_svg_dirs as $svg_dir) {
                if ($svg_dir !== $exact_category_dir) {
                    $svg_files_count = $this->count_files_in_directory($svg_dir);
                    $files_count += $svg_files_count;
                    WP_CLI::log("  📁 将清理SVG变体文件夹: " . basename($svg_dir) . " ({$svg_files_count} 个文件)");
                }
            }
        }

        // 检查frontend-gallery文件夹（包含预览图）
        $frontend_gallery_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name;
        if (is_dir($frontend_gallery_dir)) {
            $gallery_files_count = $this->count_files_in_directory($frontend_gallery_dir);
            $files_count += $gallery_files_count;
            WP_CLI::log("  📁 将清理前端Gallery文件夹: {$safe_category_name} ({$gallery_files_count} 个文件，包含预览图)");
        }

        // 检查其他文件夹
        $other_dirs = [
            '/shoe-svg-generator/final-products/' . $safe_category_name,
            '/shoe-svg-generator/category_svg/' . $safe_category_name
        ];

        foreach ($other_dirs as $dir_path) {
            $full_path = $upload_dir['basedir'] . $dir_path;
            if (is_dir($full_path)) {
                $dir_files_count = $this->count_files_in_directory($full_path);
                $files_count += $dir_files_count;
                WP_CLI::log("  📁 将清理文件夹: " . basename($full_path) . " ({$dir_files_count} 个文件)");
            }
        }

        if ($files_count == 0) {
            WP_CLI::log("  ✅ 未找到需要清理的文件");
        }

        return $files_count;
    }

    /**
     * 条件化日志输出（兼容并行进程）
     */
    private function log_message($message, $type = 'log') {
        if (defined('SS_PARALLEL_PROCESS')) {
            $prefix = $type === 'warning' ? '[并行清理][警告]' : '[并行清理]';
            error_log($prefix . ' ' . $message);
        } else {
            if ($type === 'warning') {
                WP_CLI::warning($message);
            } else {
                WP_CLI::log($message);
            }
        }
    }

    /**
     * 生成与SVG文件夹命名一致的安全文件名（使用连字符）
     *
     * @param string $category_name 类目名称
     * @return string 安全的文件名
     */
    private function generate_category_safe_name($category_name) {
        // 转换为小写
        $safe_name = strtolower($category_name);

        // 替换空格为连字符
        $safe_name = str_replace(' ', '-', $safe_name);

        // 移除特殊字符，只保留字母、数字和连字符
        $safe_name = preg_replace('/[^a-z0-9\-]/', '', $safe_name);

        // 移除多余的连字符
        $safe_name = preg_replace('/-+/', '-', $safe_name);

        // 移除开头和结尾的连字符
        $safe_name = trim($safe_name, '-');

        return $safe_name;
    }

    /**
     * 生成产品文件夹命名格式（使用下划线）
     *
     * @param string $category_name 类目名称
     * @return string 产品文件夹名称
     */
    private function generate_product_folder_name($category_name) {
        // 替换空格为下划线，保持原始大小写
        $folder_name = str_replace(' ', '_', $category_name);

        // 移除特殊字符，只保留字母、数字和下划线
        $folder_name = preg_replace('/[^a-zA-Z0-9_]/', '', $folder_name);

        // 移除多余的下划线
        $folder_name = preg_replace('/_+/', '_', $folder_name);

        // 移除开头和结尾的下划线
        $folder_name = trim($folder_name, '_');

        return $folder_name;
    }

    /**
     * 显示清理统计信息
     */
    private function display_clean_stats($stats, $dry_run) {
        $mode = $dry_run ? '预览模式' : '执行模式';

        WP_CLI::log("\n=== 清理统计 ({$mode}) ===");
        WP_CLI::log("处理类目数: {$stats['categories_processed']}");
        WP_CLI::log("删除产品数: {$stats['products_deleted']}");
        WP_CLI::log("删除图片数: {$stats['images_deleted']}");
        WP_CLI::log("删除文件数: {$stats['files_deleted']}");

        if ($stats['errors'] > 0) {
            WP_CLI::warning("错误数量: {$stats['errors']}");
        }
    }

    /**
     * 清理自定义匹配类目的所有数据和文件
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要清理的自定义匹配类目ID。可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     * 如果不指定，将清理所有自定义匹配类目
     *
     * [--all]
     * : 清理所有自定义匹配类目（与不指定--category效果相同）
     *
     * [--dry-run]
     * : 预览模式，只显示将要清理的内容，不实际执行删除操作
     *
     * [--verbose]
     * : 显示详细的清理过程日志
     *
     * [--force]
     * : 强制执行清理，跳过确认提示
     *
     * [--older-than=<hours>]
     * : 只清理指定小时数之前创建的自定义匹配类目。例如：--older-than=72 表示清理72小时前创建的类目
     *
     * [--created-before=<date>]
     * : 只清理指定日期之前创建的自定义匹配类目。支持格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
     *
     * ## EXAMPLES
     *
     *     # 预览清理所有自定义匹配类目
     *     $ wp ss clean_custom_categories --dry-run --verbose --allow_root
     *
     *     # 清理指定的自定义匹配类目
     *     $ wp ss clean_custom_categories --category=123,456 --verbose --allow_root
     *
     *     # 强制清理所有自定义匹配类目
     *     $ wp ss clean_custom_categories --all --force --verbose --allow_root
     *
     *     # 清理72小时前创建的自定义匹配类目
     *     $ wp ss clean_custom_categories --older-than=72 --verbose --allow_root
     *
     *     # 清理2024年1月1日之前创建的自定义匹配类目
     *     $ wp ss clean_custom_categories --created-before="2024-01-01" --verbose --allow_root
     *
     *     # 预览清理一周前创建的类目
     *     $ wp ss clean_custom_categories --older-than=168 --dry-run --verbose --allow_root
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function clean_custom_categories($args, $assoc_args) {
        $dry_run = isset($assoc_args['dry-run']);
        $verbose = isset($assoc_args['verbose']);
        $force = isset($assoc_args['force']);
        $all_categories = isset($assoc_args['all']);

        // 解析类目ID参数
        $category_ids = array();
        if (isset($assoc_args['category'])) {
            $category_ids = array_map('intval', explode(',', $assoc_args['category']));
            $category_ids = array_filter($category_ids); // 移除无效ID
        }

        // 【新增】解析时间过滤参数
        $time_filter = $this->parse_time_filter_params($assoc_args, $verbose);

        WP_CLI::log('🧹 开始清理自定义匹配类目数据和文件...');

        if ($dry_run) {
            WP_CLI::log('📋 运行模式: 预览模式 (不会实际删除任何数据)');
        } else {
            WP_CLI::log('⚠️  运行模式: 执行模式 (将实际删除数据和文件)');
        }

        // 获取要处理的自定义匹配类目
        $categories = $this->get_custom_matching_categories($category_ids, $all_categories, $time_filter);

        if (empty($categories)) {
            if (!empty($category_ids)) {
                WP_CLI::warning('指定的类目ID中没有找到自定义匹配类目');
            } else {
                WP_CLI::log('✅ 没有找到需要清理的自定义匹配类目');
            }
            return;
        }

        $total_categories = count($categories);

        // 【新增】显示时间过滤结果
        if ($time_filter) {
            WP_CLI::log(sprintf('🎯 根据时间过滤条件，找到 %d 个符合条件的自定义匹配类目需要清理', $total_categories));
            if ($verbose) {
                WP_CLI::log("⏰ 过滤条件: " . $time_filter['date'] . " 之前创建的类目");
            }
        } else {
            WP_CLI::log(sprintf('🎯 找到 %d 个自定义匹配类目需要清理', $total_categories));
        }

        // 显示要处理的类目列表
        if ($verbose) {
            WP_CLI::log('📋 待清理的自定义匹配类目列表:');
            foreach ($categories as $index => $category) {
                $search_term = get_term_meta($category->term_id, '_custom_matching_search_term', true);
                $created_time = get_term_meta($category->term_id, '_custom_matching_created_time', true);

                // 【新增】如果有时间过滤，高亮显示创建时间
                $time_display = $created_time;
                if ($time_filter && $created_time) {
                    $created_timestamp = strtotime($created_time);
                    // 【修复】使用WordPress时间函数确保时区正确
                    $current_timestamp = current_time('timestamp');
                    $hours_ago = round(($current_timestamp - $created_timestamp) / 3600, 1);
                    $time_display = "{$created_time} ({$hours_ago}小时前)";
                }

                WP_CLI::log(sprintf('  %d. %s (ID: %d) - 搜索词: "%s" - 创建时间: %s',
                    $index + 1, $category->name, $category->term_id, $search_term, $time_display));
            }
        }

        // 安全确认（非强制模式且非dry-run模式）
        if (!$force && !$dry_run) {
            WP_CLI::confirm(
                sprintf('⚠️  确认要清理 %d 个自定义匹配类目的所有数据和文件吗？此操作不可逆！', $total_categories),
                $assoc_args
            );
        }

        // 统计信息
        $total_stats = array(
            'categories_processed' => 0,
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0,
            'directories_deleted' => 0,
            'categories_with_templates' => 0,
            'template_products_protected' => 0,
            'parent_categories_cleaned' => 0,
            'category_thumbnails_deleted' => 0,
            'errors' => 0
        );

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('清理自定义匹配类目', $total_categories);

        // 处理每个类目
        foreach ($categories as $index => $category) {
            $current_num = $index + 1;
            WP_CLI::log("🔄 处理类目 [{$current_num}/{$total_categories}]: {$category->name} (ID: {$category->term_id})");

            $stats = $this->clean_custom_matching_category($category->term_id, $dry_run, $verbose);

            // 累计统计
            $total_stats['categories_processed']++;
            $total_stats['products_deleted'] += $stats['products_deleted'];
            $total_stats['images_deleted'] += $stats['images_deleted'];
            $total_stats['files_deleted'] += $stats['files_deleted'];
            $total_stats['directories_deleted'] += $stats['directories_deleted'];
            $total_stats['category_thumbnails_deleted'] += $stats['category_thumbnails_deleted'];
            $total_stats['errors'] += $stats['errors'];

            // 模板产品保护统计
            if (isset($stats['has_template_products']) && $stats['has_template_products']) {
                $total_stats['categories_with_templates']++;
                $total_stats['template_products_protected'] += $stats['template_products_count'];
            }

            $progress->tick();

            if ($verbose) {
                WP_CLI::log(sprintf('  ✅ 类目处理完成 - 产品: %d, 图片: %d, 文件: %d, 目录: %d',
                    $stats['products_deleted'], $stats['images_deleted'],
                    $stats['files_deleted'], $stats['directories_deleted']));
            }
        }

        $progress->finish();

        // 清理完所有子类目后，检查并清理父类目
        WP_CLI::log("\n🔍 检查自定义上传产品父类目...");
        $parent_stats = $this->clean_custom_matching_parent_categories($dry_run, $verbose);
        $total_stats['parent_categories_cleaned'] += $parent_stats['parent_categories_cleaned'];
        $total_stats['category_thumbnails_deleted'] += $parent_stats['category_thumbnails_deleted'];
        $total_stats['errors'] += $parent_stats['errors'];

        // 显示最终统计
        $this->display_custom_clean_stats($total_stats, $dry_run);

        if ($dry_run) {
            WP_CLI::success('📋 预览完成！使用 --force 参数执行实际清理操作');
        } else {
            WP_CLI::success('🎉 自定义匹配类目清理完成！');
        }
    }

    /**
     * 【新增】解析时间过滤参数
     *
     * @param array $assoc_args 关联参数
     * @param bool $verbose 是否显示详细日志
     * @return array|null 时间过滤配置，null表示无时间过滤
     */
    private function parse_time_filter_params($assoc_args, $verbose = false) {
        $older_than_hours = isset($assoc_args['older-than']) ? intval($assoc_args['older-than']) : null;
        $created_before = isset($assoc_args['created-before']) ? $assoc_args['created-before'] : null;

        // 验证参数互斥性
        if ($older_than_hours && $created_before) {
            WP_CLI::error('不能同时使用 --older-than 和 --created-before 参数，请选择其中一个');
        }

        // 处理 --older-than 参数
        if ($older_than_hours) {
            if ($older_than_hours <= 0) {
                WP_CLI::error('--older-than 参数必须是正整数（小时数）');
            }

            // 【修复】使用WordPress时间函数确保时区正确
            $current_timestamp = current_time('timestamp');
            $cutoff_timestamp = $current_timestamp - ($older_than_hours * 3600);
            $cutoff_date = date('Y-m-d H:i:s', $cutoff_timestamp);

            if ($verbose) {
                WP_CLI::log("⏰ 时间过滤: 只清理 {$older_than_hours} 小时前（{$cutoff_date} 之前）创建的类目");
                WP_CLI::log("🕐 当前时间: " . date('Y-m-d H:i:s', $current_timestamp));
            }

            return array(
                'type' => 'older_than',
                'hours' => $older_than_hours,
                'timestamp' => $cutoff_timestamp,
                'date' => $cutoff_date
            );
        }

        // 处理 --created-before 参数
        if ($created_before) {
            // 尝试解析日期格式
            $timestamp = strtotime($created_before);
            if ($timestamp === false) {
                WP_CLI::error('--created-before 参数日期格式无效。支持格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS');
            }

            $formatted_date = date('Y-m-d H:i:s', $timestamp);

            if ($verbose) {
                WP_CLI::log("📅 时间过滤: 只清理 {$formatted_date} 之前创建的类目");
            }

            return array(
                'type' => 'created_before',
                'input' => $created_before,
                'timestamp' => $timestamp,
                'date' => $formatted_date
            );
        }

        return null; // 无时间过滤
    }

    /**
     * 获取自定义匹配类目列表
     *
     * @param array $category_ids 指定的类目ID数组
     * @param bool $all_categories 是否获取所有自定义匹配类目
     * @param array|null $time_filter 时间过滤配置
     * @return array 自定义匹配类目数组
     */
    private function get_custom_matching_categories($category_ids = array(), $all_categories = false, $time_filter = null) {
        if (!empty($category_ids)) {
            // 获取指定ID的自定义匹配类目
            $categories = array();
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("类目ID {$category_id} 不存在，跳过");
                    continue;
                }

                // 检查是否为自定义匹配类目
                $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
                if (empty($is_custom_matching)) {
                    WP_CLI::warning("类目ID {$category_id} ({$term->name}) 不是自定义匹配类目，跳过");
                    continue;
                }

                $categories[] = $term;
            }

            // 【新增】对指定类目应用时间过滤
            if ($time_filter) {
                $categories = $this->apply_time_filter_to_categories($categories, $time_filter);
            }

            return $categories;
        } else {
            // 获取所有自定义匹配类目
            $categories = get_terms(array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'meta_query' => array(
                    array(
                        'key' => '_is_custom_matching_category',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            ));

            // 【新增】应用时间过滤
            if ($time_filter) {
                $categories = $this->apply_time_filter_to_categories($categories, $time_filter);
            }

            return $categories;
        }
    }

    /**
     * 【新增】对类目列表应用时间过滤
     *
     * @param array $categories 类目列表
     * @param array $time_filter 时间过滤配置
     * @return array 过滤后的类目列表
     */
    private function apply_time_filter_to_categories($categories, $time_filter) {
        if (!$time_filter || empty($categories)) {
            return $categories;
        }

        $filtered_categories = array();
        $cutoff_timestamp = $time_filter['timestamp'];
        $debug_info = array();

        foreach ($categories as $category) {
            // 获取类目创建时间
            $created_time = get_term_meta($category->term_id, '_custom_matching_created_time', true);

            if (empty($created_time)) {
                // 【调试】记录没有创建时间的类目
                $debug_info[] = "类目 {$category->name} (ID: {$category->term_id}) 没有创建时间元数据";
                continue;
            }

            // 解析创建时间
            $created_timestamp = strtotime($created_time);
            if ($created_timestamp === false) {
                // 【调试】记录时间格式无效的类目
                $debug_info[] = "类目 {$category->name} (ID: {$category->term_id}) 时间格式无效: {$created_time}";
                continue;
            }

            // 【调试】记录时间比较信息
            $hours_diff = round(($cutoff_timestamp - $created_timestamp) / 3600, 1);
            $match_result = $created_timestamp < $cutoff_timestamp ? "匹配" : "不匹配";
            $debug_info[] = "类目 {$category->name}: {$created_time} ({$created_timestamp}) vs 截止时间 ({$cutoff_timestamp}), 时差: {$hours_diff}小时, 结果: {$match_result}";

            // 检查是否符合时间条件
            if ($created_timestamp < $cutoff_timestamp) {
                $filtered_categories[] = $category;
            }
        }

        // 【调试】输出详细信息
        if (!empty($debug_info)) {
            WP_CLI::log("🔍 时间过滤调试信息:");
            foreach ($debug_info as $info) {
                WP_CLI::log("  " . $info);
            }
        }

        return $filtered_categories;
    }

    /**
     * 清理单个自定义匹配类目的所有数据和文件
     *
     * @param int $category_id 类目ID
     * @param bool $dry_run 是否为预览模式
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_matching_category($category_id, $dry_run = false, $verbose = false) {
        $stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0,
            'directories_deleted' => 0,
            'category_thumbnails_deleted' => 0,
            'errors' => 0
        );

        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            $stats['errors']++;
            return $stats;
        }

        // 验证是否为自定义匹配类目
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
        if (empty($is_custom_matching)) {
            if ($verbose) {
                WP_CLI::warning("类目 {$term->name} (ID: {$category_id}) 不是自定义匹配类目");
            }
            $stats['errors']++;
            return $stats;
        }

        if ($verbose) {
            WP_CLI::log("  🔍 开始清理自定义匹配类目: {$term->name} (slug: {$term->slug})");
        }

        // 1. 清理产品数据
        $product_stats = $this->clean_custom_category_products($category_id, $dry_run, $verbose);
        $stats['products_deleted'] += $product_stats['products_deleted'];
        $stats['images_deleted'] += $product_stats['images_deleted'];
        $stats['errors'] += $product_stats['errors'];

        // 2. 清理自定义匹配专用文件和目录
        $file_stats = $this->clean_custom_category_files($category_id, $term->slug, $dry_run, $verbose);
        $stats['files_deleted'] += $file_stats['files_deleted'];
        $stats['directories_deleted'] += $file_stats['directories_deleted'];
        $stats['errors'] += $file_stats['errors'];

        // 3. 清理类目缩略图和相关图片
        $thumbnail_stats = $this->clean_custom_category_thumbnails($category_id, $dry_run, $verbose);
        $stats['category_thumbnails_deleted'] += $thumbnail_stats['category_thumbnails_deleted'];
        $stats['images_deleted'] += $thumbnail_stats['images_deleted'];
        $stats['errors'] += $thumbnail_stats['errors'];

        // 4. 清理类目元数据
        if (!$dry_run) {
            $meta_stats = $this->clean_custom_category_metadata($category_id, $verbose);
            $stats['errors'] += $meta_stats['errors'];
        } else if ($verbose) {
            WP_CLI::log("  📋 [预览] 将清理类目元数据");
        }

        // 5. 【修复】直接删除产品类目（删除类目不会影响模板产品）
        $has_template_products = isset($product_stats['has_template_products']) ? $product_stats['has_template_products'] : false;

        if ($has_template_products && $verbose) {
            WP_CLI::log("  ℹ️  类目中包含模板产品，但仍会删除类目（模板产品不受影响）");
        }

        // 直接删除整个类目
        if (!$dry_run) {
            $delete_result = wp_delete_term($category_id, 'product_cat');
            if (is_wp_error($delete_result)) {
                if ($verbose) {
                    WP_CLI::warning("  ❌ 删除类目失败: " . $delete_result->get_error_message());
                }
                $stats['errors']++;
            } else {
                if ($verbose) {
                    WP_CLI::log("  ✅ 已删除类目: {$term->name}");
                }
            }
        } else if ($verbose) {
            WP_CLI::log("  📋 [预览] 将删除类目: {$term->name}");
        }

        return $stats;
    }

    /**
     * 清理自定义匹配类目的产品数据
     *
     * @param int $category_id 类目ID
     * @param bool $dry_run 是否为预览模式
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_category_products($category_id, $dry_run = false, $verbose = false) {
        $stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'errors' => 0
        );

        // 获取类目下的所有产品
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                )
            )
        ));

        if (empty($products)) {
            if ($verbose) {
                WP_CLI::log("  ℹ️  没有找到产品");
            }
            return $stats;
        }

        // 🔥 修复：获取模板产品列表（需要保护）
        $template_products = $this->get_template_products_in_category($products);
        $template_image_ids = $this->get_template_image_ids($template_products);

        if ($verbose) {
            WP_CLI::log(sprintf("  📦 找到 %d 个产品，其中 %d 个模板产品需要保护", count($products), count($template_products)));
        }

        // 分离生成产品和模板产品
        $generated_products = array();
        $protected_products = array();
        foreach ($products as $product) {
            if (in_array($product->ID, $template_products)) {
                $protected_products[] = $product;
            } else {
                $generated_products[] = $product;
            }
        }

        if ($verbose && !empty($protected_products)) {
            WP_CLI::log("  🛡️  受保护的模板产品:");
            foreach ($protected_products as $product) {
                WP_CLI::log("    - {$product->post_title} (ID: {$product->ID})");
            }
        }

        if ($verbose) {
            WP_CLI::log(sprintf("  🗑️  将删除 %d 个生成产品", count($generated_products)));
        }

        // 只删除生成产品，跳过模板产品
        foreach ($generated_products as $product) {
            if (!$dry_run) {
                try {
                    $product_stats = $this->delete_generated_product($product->ID, $template_image_ids, false);
                    $stats['products_deleted'] += $product_stats['products_deleted'];
                    $stats['images_deleted'] += $product_stats['images_deleted'];
                    if ($verbose) {
                        WP_CLI::log("    ✅ 已删除生成产品: {$product->post_title} (ID: {$product->ID})");
                    }
                } catch (Exception $e) {
                    $stats['errors']++;
                    if ($verbose) {
                        WP_CLI::warning("    ❌ 删除产品 {$product->ID} 时出错: " . $e->getMessage());
                    }
                }
            } else {
                $stats['products_deleted']++; // 预览模式下计数
                if ($verbose) {
                    WP_CLI::log("    📋 [预览] 将删除产品: {$product->post_title} (ID: {$product->ID})");
                }
            }
        }

        // 如果有模板产品被跳过，显示警告信息
        if (!empty($protected_products)) {
            if ($verbose) {
                WP_CLI::log(sprintf("  ⚠️  跳过了 %d 个模板产品，这些产品将保留在类目中", count($protected_products)));
            }
        }

        // 添加模板产品信息到统计中
        $stats['has_template_products'] = !empty($protected_products);
        $stats['template_products_count'] = count($protected_products);

        return $stats;
    }

    /**
     * 清理自定义匹配类目的专用文件和目录
     *
     * @param int $category_id 类目ID
     * @param string $category_slug 类目slug
     * @param bool $dry_run 是否为预览模式
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_category_files($category_id, $category_slug, $dry_run = false, $verbose = false) {
        $stats = array(
            'files_deleted' => 0,
            'directories_deleted' => 0,
            'errors' => 0
        );

        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/';

        // 自定义匹配类目的专用目录列表
        $directories_to_clean = array(
            'svg' => $base_dir . 'svg/' . $category_slug . '/',
            'previews' => $base_dir . 'previews/' . $category_slug . '/',
            'products' => $base_dir . 'products/' . $category_slug . '/',
            'images' => $base_dir . 'images/' . $category_slug . '/'  // 如果存在的话
        );

        if ($verbose) {
            WP_CLI::log("  📁 检查自定义匹配专用目录:");
        }

        foreach ($directories_to_clean as $type => $dir_path) {
            if (is_dir($dir_path)) {
                $files_count = $this->count_files_in_directory($dir_path);

                if ($verbose) {
                    WP_CLI::log("    📂 {$type}: {$dir_path} ({$files_count} 个文件)");
                }

                if (!$dry_run) {
                    try {
                        if ($this->remove_directory($dir_path)) {
                            $stats['files_deleted'] += $files_count;
                            $stats['directories_deleted']++;
                            if ($verbose) {
                                WP_CLI::log("    ✅ 已清理 {$type} 目录");
                            }
                        } else {
                            $stats['errors']++;
                            if ($verbose) {
                                WP_CLI::warning("    ❌ 清理 {$type} 目录失败");
                            }
                        }
                    } catch (Exception $e) {
                        $stats['errors']++;
                        if ($verbose) {
                            WP_CLI::warning("    ❌ 清理 {$type} 目录时出错: " . $e->getMessage());
                        }
                    }
                } else {
                    $stats['files_deleted'] += $files_count; // 预览模式下计数
                    $stats['directories_deleted']++;
                    if ($verbose) {
                        WP_CLI::log("    📋 [预览] 将清理 {$type} 目录");
                    }
                }
            } else {
                if ($verbose) {
                    WP_CLI::log("    ℹ️  {$type} 目录不存在: {$dir_path}");
                }
            }
        }

        // 检查并清理可能存在的其他相关文件
        $other_patterns = array(
            $base_dir . 'temp/' . $category_slug . '*',
            $upload_dir['basedir'] . '/shoe-svg-generator/svg-records/category_' . $category_id . '_*.json'
        );

        foreach ($other_patterns as $pattern) {
            $files = glob($pattern);
            foreach ($files as $file) {
                if (is_file($file)) {
                    if (!$dry_run) {
                        if (unlink($file)) {
                            $stats['files_deleted']++;
                            if ($verbose) {
                                WP_CLI::log("    ✅ 已删除相关文件: " . basename($file));
                            }
                        } else {
                            $stats['errors']++;
                            if ($verbose) {
                                WP_CLI::warning("    ❌ 删除文件失败: " . basename($file));
                            }
                        }
                    } else {
                        $stats['files_deleted']++;
                        if ($verbose) {
                            WP_CLI::log("    📋 [预览] 将删除文件: " . basename($file));
                        }
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * 清理自定义匹配类目的元数据
     *
     * @param int $category_id 类目ID
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_category_metadata($category_id, $verbose = false) {
        $stats = array(
            'errors' => 0
        );

        // 自定义匹配类目的专用元数据键
        $meta_keys_to_clean = array(
            '_is_custom_matching_category',
            '_custom_matching_date',
            '_custom_matching_sequence',
            '_custom_matching_search_term',
            '_custom_matching_created_time',
            '_custom_matching_user_email',
            'ss_category_colors',
            'ss_selected_templates',
            '_ss_completed_svg_files',
            '_ss_processed_svg_files',
            'ss_wp_cli_products_generated',
            'ss_backend_products_generated',
            'ss_frontend_preview_generated'
        );

        if ($verbose) {
            WP_CLI::log("  🏷️  清理类目元数据:");
        }

        foreach ($meta_keys_to_clean as $meta_key) {
            $result = delete_term_meta($category_id, $meta_key);
            if ($result !== false) {
                if ($verbose) {
                    WP_CLI::log("    ✅ 已清理元数据: {$meta_key}");
                }
            } else {
                if ($verbose) {
                    WP_CLI::log("    ℹ️  元数据不存在或已清理: {$meta_key}");
                }
            }
        }

        return $stats;
    }

    /**
     * 检查类目是否包含模板产品
     *
     * @param int $category_id 类目ID
     * @return bool 是否包含模板产品
     */
    private function check_category_has_template_products($category_id) {
        // 获取类目下的所有产品
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                )
            )
        ));

        if (empty($products)) {
            return false;
        }

        // 检查是否有模板产品
        foreach ($products as $product) {
            if (get_post_meta($product->ID, '_ss_is_template', true) === '1') {
                return true;
            }
        }

        return false;
    }

    /**
     * 清理自定义匹配类目的缩略图和相关图片
     *
     * @param int $category_id 类目ID
     * @param bool $dry_run 是否为预览模式
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_category_thumbnails($category_id, $dry_run = false, $verbose = false) {
        $stats = array(
            'category_thumbnails_deleted' => 0,
            'images_deleted' => 0,
            'errors' => 0
        );

        if ($verbose) {
            WP_CLI::log("  🖼️  清理类目缩略图和相关图片:");
        }

        // 1. 清理类目缩略图（WordPress term meta）
        $thumbnail_id = get_term_meta($category_id, 'thumbnail_id', true);
        if (!empty($thumbnail_id)) {
            if (!$dry_run) {
                // 检查这个图片是否被其他类目使用
                $other_usage = get_posts(array(
                    'post_type' => 'any',
                    'meta_query' => array(
                        array(
                            'key' => 'thumbnail_id',
                            'value' => $thumbnail_id,
                            'compare' => '='
                        )
                    ),
                    'posts_per_page' => 1,
                    'exclude' => array($category_id)
                ));

                if (empty($other_usage)) {
                    // 没有其他地方使用，可以安全删除
                    if (wp_delete_attachment($thumbnail_id, true)) {
                        $stats['category_thumbnails_deleted']++;
                        if ($verbose) {
                            WP_CLI::log("    ✅ 已删除类目缩略图 (ID: {$thumbnail_id})");
                        }
                    } else {
                        $stats['errors']++;
                        if ($verbose) {
                            WP_CLI::warning("    ❌ 删除类目缩略图失败 (ID: {$thumbnail_id})");
                        }
                    }
                } else {
                    if ($verbose) {
                        WP_CLI::log("    ⚠️  类目缩略图被其他地方使用，跳过删除 (ID: {$thumbnail_id})");
                    }
                }

                // 清理类目的缩略图关联
                delete_term_meta($category_id, 'thumbnail_id');
            } else {
                $stats['category_thumbnails_deleted']++;
                if ($verbose) {
                    WP_CLI::log("    📋 [预览] 将删除类目缩略图 (ID: {$thumbnail_id})");
                }
            }
        }

        // 2. 清理自定义匹配图片（存储在 _custom_matching_image_url）
        $custom_image_url = get_term_meta($category_id, '_custom_matching_image_url', true);
        if (!empty($custom_image_url)) {
            if ($verbose) {
                WP_CLI::log("    🔍 发现自定义匹配图片: " . basename($custom_image_url));
            }

            // 从URL获取文件路径
            $upload_dir = wp_upload_dir();
            $image_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $custom_image_url);

            if (file_exists($image_path)) {
                if (!$dry_run) {
                    if (unlink($image_path)) {
                        $stats['images_deleted']++;
                        if ($verbose) {
                            WP_CLI::log("    ✅ 已删除自定义匹配图片: " . basename($image_path));
                        }
                    } else {
                        $stats['errors']++;
                        if ($verbose) {
                            WP_CLI::warning("    ❌ 删除自定义匹配图片失败: " . basename($image_path));
                        }
                    }
                } else {
                    $stats['images_deleted']++;
                    if ($verbose) {
                        WP_CLI::log("    📋 [预览] 将删除自定义匹配图片: " . basename($image_path));
                    }
                }
            } else {
                if ($verbose) {
                    WP_CLI::log("    ℹ️  自定义匹配图片文件不存在: " . basename($image_path));
                }
            }
        }

        // 3. 清理可能的缩略图文件（在 custom-matching/images 目录中）
        $upload_dir = wp_upload_dir();
        $images_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/images/';

        if (is_dir($images_dir)) {
            $term = get_term($category_id, 'product_cat');
            $search_term = get_term_meta($category_id, '_custom_matching_search_term', true);

            // 查找可能相关的图片文件
            $possible_patterns = array();
            if (!empty($search_term)) {
                $safe_search_term = preg_replace('/[^a-zA-Z0-9\-_]/', '', $search_term);
                $possible_patterns[] = $images_dir . '*' . $safe_search_term . '*';
            }
            if (!empty($term->slug)) {
                $possible_patterns[] = $images_dir . '*' . $term->slug . '*';
            }
            $possible_patterns[] = $images_dir . '*category_' . $category_id . '*';

            foreach ($possible_patterns as $pattern) {
                $files = glob($pattern);
                foreach ($files as $file) {
                    if (is_file($file)) {
                        if (!$dry_run) {
                            if (unlink($file)) {
                                $stats['images_deleted']++;
                                if ($verbose) {
                                    WP_CLI::log("    ✅ 已删除相关图片: " . basename($file));
                                }
                            } else {
                                $stats['errors']++;
                                if ($verbose) {
                                    WP_CLI::warning("    ❌ 删除相关图片失败: " . basename($file));
                                }
                            }
                        } else {
                            $stats['images_deleted']++;
                            if ($verbose) {
                                WP_CLI::log("    📋 [预览] 将删除相关图片: " . basename($file));
                            }
                        }
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * 清理自定义上传产品的父类目
     *
     * @param bool $dry_run 是否为预览模式
     * @param bool $verbose 是否显示详细日志
     * @return array 清理统计信息
     */
    private function clean_custom_matching_parent_categories($dry_run = false, $verbose = false) {
        $stats = array(
            'parent_categories_cleaned' => 0,
            'category_thumbnails_deleted' => 0,
            'errors' => 0
        );

        // 查找自定义匹配父类目
        $parent_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
            'meta_query' => array(
                array(
                    'key' => '_is_custom_matching_parent',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));

        if (empty($parent_categories)) {
            if ($verbose) {
                WP_CLI::log("ℹ️  没有找到自定义匹配父类目");
            }
            return $stats;
        }

        foreach ($parent_categories as $parent_term) {
            if ($verbose) {
                WP_CLI::log("🔍 检查父类目: {$parent_term->name} (ID: {$parent_term->term_id})");
            }

            // 检查是否还有子类目
            $child_categories = get_terms(array(
                'taxonomy' => 'product_cat',
                'parent' => $parent_term->term_id,
                'hide_empty' => false,
                'fields' => 'ids'
            ));

            if (empty($child_categories)) {
                // 没有子类目，可以删除父类目
                if ($verbose) {
                    WP_CLI::log("  📂 父类目下没有子类目，准备清理");
                }

                // 清理父类目的缩略图
                $thumbnail_stats = $this->clean_custom_category_thumbnails($parent_term->term_id, $dry_run, $verbose);
                $stats['category_thumbnails_deleted'] += $thumbnail_stats['category_thumbnails_deleted'];
                $stats['errors'] += $thumbnail_stats['errors'];

                // 删除父类目本身
                if (!$dry_run) {
                    $delete_result = wp_delete_term($parent_term->term_id, 'product_cat');
                    if (is_wp_error($delete_result)) {
                        $stats['errors']++;
                        if ($verbose) {
                            WP_CLI::warning("  ❌ 删除父类目失败: " . $delete_result->get_error_message());
                        }
                    } else {
                        $stats['parent_categories_cleaned']++;
                        if ($verbose) {
                            WP_CLI::log("  ✅ 已删除父类目: {$parent_term->name}");
                        }
                    }
                } else {
                    $stats['parent_categories_cleaned']++;
                    if ($verbose) {
                        WP_CLI::log("  📋 [预览] 将删除父类目: {$parent_term->name}");
                    }
                }
            } else {
                if ($verbose) {
                    WP_CLI::log("  ⚠️  父类目下还有 " . count($child_categories) . " 个子类目，保留父类目");
                }
            }
        }

        return $stats;
    }

    /**
     * 显示自定义匹配类目清理统计信息
     *
     * @param array $stats 统计信息
     * @param bool $dry_run 是否为预览模式
     */
    private function display_custom_clean_stats($stats, $dry_run) {
        $mode = $dry_run ? '预览模式' : '执行模式';

        WP_CLI::log("\n=== 自定义匹配类目清理统计 ({$mode}) ===");
        WP_CLI::log("处理类目数: {$stats['categories_processed']}");
        WP_CLI::log("删除产品数: {$stats['products_deleted']}");
        WP_CLI::log("删除图片数: {$stats['images_deleted']}");
        WP_CLI::log("删除文件数: {$stats['files_deleted']}");
        WP_CLI::log("删除目录数: {$stats['directories_deleted']}");

        // 新增统计项
        if (isset($stats['category_thumbnails_deleted'])) {
            WP_CLI::log("删除类目缩略图数: {$stats['category_thumbnails_deleted']}");
        }
        if (isset($stats['parent_categories_cleaned']) && $stats['parent_categories_cleaned'] > 0) {
            WP_CLI::log("清理父类目数: {$stats['parent_categories_cleaned']}");
        }

        // 模板产品保护统计
        if (isset($stats['categories_with_templates']) && $stats['categories_with_templates'] > 0) {
            WP_CLI::log("包含模板产品的类目数: {$stats['categories_with_templates']}");
            WP_CLI::log("受保护的模板产品数: {$stats['template_products_protected']}");
        }

        if ($stats['errors'] > 0) {
            WP_CLI::warning("错误数量: {$stats['errors']}");
        }

        if ($dry_run) {
            WP_CLI::log("\n💡 这是预览模式，没有实际删除任何数据");
            WP_CLI::log("💡 使用 --force 参数执行实际清理操作");
        }
    }

    /**
     * 生成 SVG 文件的 JSON 记录（智能检查，只为新类目生成）
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 为所有底层类目检查并生成JSON记录（智能跳过已有记录）
     *     $ wp ss generate_svg_records
     *     # 或者使用连字符版本
     *     $ wp ss generate-svg-records
     *
     *     # 为指定类目检查并生成JSON记录
     *     $ wp ss generate_svg_records --category=123
     *
     *     # 为多个指定类目检查并生成JSON记录
     *     $ wp ss generate_svg_records --category=123,456,789
     *
     * ## DESCRIPTION
     *
     * 此命令会智能检查每个类目是否已有JSON记录文件：
     * - 如果已有记录，则跳过该类目
     * - 如果没有记录，则为该类目生成新的JSON记录
     * - 适用于新增类目的初始化和现有系统的增量更新
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function generate_svg_records($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('开始生成 SVG 文件 JSON 记录...');

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $categories_to_process = $this->get_categories_to_process($assoc_args);

        if (empty($categories_to_process)) {
            WP_CLI::error("没有找到要处理的类目");
            return;
        }

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('生成JSON记录', count($categories_to_process));

        $success_count = 0;
        $error_count = 0;
        $skipped_count = 0;
        $total_files = 0;

        foreach ($categories_to_process as $category) {
            // 检查是否已有JSON记录
            $existing_record = $svg_file_manager->get_category_json_record($category->term_id);

            if ($existing_record) {
                // 已有JSON记录，跳过
                $skipped_count++;
                if ($verbose) {
                    WP_CLI::log("⏭️  跳过类目 \"{$category->name}\" (ID: {$category->term_id})：已有JSON记录（{$existing_record['total_files']} 个文件）");
                }
            } else {
                // 没有JSON记录，生成新记录
                if ($verbose) {
                    WP_CLI::log("🔄 正在为类目 \"{$category->name}\" (ID: {$category->term_id}) 生成JSON记录...");
                }

                $result = $svg_file_manager->generate_category_json_record($category->term_id);

                if ($result['success']) {
                    $success_count++;
                    $total_files += $result['files_count'];
                    if ($verbose) {
                        WP_CLI::log("✅ " . $result['message']);
                    }
                } else {
                    $error_count++;
                    WP_CLI::warning("❌ 类目 \"{$category->name}\": " . $result['message']);
                }
            }

            $progress->tick();
        }

        $progress->finish();

        if ($skipped_count > 0) {
            WP_CLI::success("JSON记录检查完成！新生成 {$success_count} 个类目记录，跳过 {$skipped_count} 个已有记录，失败 {$error_count} 个，总共记录 {$total_files} 个新SVG文件");
        } else {
            WP_CLI::success("JSON记录生成完成！成功处理 {$success_count} 个类目，失败 {$error_count} 个，总共记录 {$total_files} 个SVG文件");
        }
    }

    /**
     * 将 SVG 文件转移到 OSS
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 将所有底层类目的SVG文件转移到OSS
     *     $ wp ss migrate_svg_to_oss
     *     # 或者使用连字符版本
     *     $ wp ss migrate-svg-to-oss
     *
     *     # 将指定类目的SVG文件转移到OSS
     *     $ wp ss migrate_svg_to_oss --category=123
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function migrate_svg_to_oss($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('开始将 SVG 文件转移到 OSS...');

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $categories_to_process = $this->get_categories_to_process($assoc_args);

        if (empty($categories_to_process)) {
            WP_CLI::error("没有找到要处理的类目");
            return;
        }

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('转移到OSS', count($categories_to_process));

        $success_count = 0;
        $error_count = 0;
        $total_migrated = 0;
        $total_already_migrated = 0;
        $total_errors = 0;

        foreach ($categories_to_process as $category) {
            if ($verbose) {
                WP_CLI::log("正在转移类目 \"{$category->name}\" (ID: {$category->term_id}) 的SVG文件...");
            }

            $result = $svg_file_manager->migrate_category_to_oss($category->term_id);

            if ($result['success']) {
                $success_count++;
                $total_migrated += $result['migrated_count'];
                $total_already_migrated += $result['already_migrated'];
                $total_errors += $result['error_count'];

                if ($verbose) {
                    WP_CLI::log("✅ " . $result['message']);
                }
            } else {
                $error_count++;
                WP_CLI::warning("❌ 类目 \"{$category->name}\": " . $result['message']);
            }

            $progress->tick();
        }

        $progress->finish();

        WP_CLI::success("SVG文件转移完成！成功处理 {$success_count} 个类目，失败 {$error_count} 个");
        WP_CLI::log("转移统计：新转移 {$total_migrated} 个文件，已存在 {$total_already_migrated} 个文件，错误 {$total_errors} 个文件");
    }

    /**
     * 【新增】一键式产品生成和OSS同步完整流程
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--max-parallel=<number>]
     * : 产品生成最大并行进程数（默认：4）
     *
     * [--batch-size=<number>]
     * : 每批处理的类目数量（默认：5）
     *
     * [--cpu-threshold=<percentage>]
     * : CPU使用率阈值（默认：80%）
     *
     * [--memory-threshold=<percentage>]
     * : 内存使用率阈值（默认：85%）
     *
     * [--threads=<number>]
     * : 转换线程数（默认16）
     *
     * [--conversion-batch-size=<number>]
     * : 转换批次大小（默认50）
     *
     * [--skip-product-generation]
     * : 跳过产品生成步骤
     *
     * [--skip-svg-to-png]
     * : 跳过SVG到PNG转换
     *
     * [--skip-png-to-filf]
     * : 跳过PNG到FILF转换
     *
     * [--skip-oss-upload]
     * : 跳过OSS上传（在转换流程中）
     *
     * [--skip-cleanup]
     * : 跳过本地文件清理
     *
     * [--auto-cleanup]
     * : 自动清理：转换完成后立即删除原始SVG文件，OSS上传完成后立即删除本地FILF文件
     *
     * [--no-overwrite]
     * : 不覆盖OSS中已存在的文件
     *
     * [--backup-old]
     * : 覆盖前备份OSS中的旧文件
     *
     * [--verbose]
     * : 显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 完整一键式流程（推荐）
     *     $ wp ss generate_and_sync_complete --max-parallel=8 --threads=16 --verbose --allow-root
     *
     *     # 完整流程 + 自动清理（推荐，节省磁盘空间）
     *     $ wp ss generate_and_sync_complete --auto-cleanup --max-parallel=8 --threads=16 --verbose --allow-root
     *
     *     # 处理指定类目
     *     $ wp ss generate_and_sync_complete --category=123,456 --verbose --allow-root
     *
     *     # 高性能模式 + 自动清理
     *     $ wp ss generate_and_sync_complete --auto-cleanup --max-parallel=12 --cpu-threshold=90 --threads=24 --verbose --allow-root
     *
     *     # 仅产品生成和记录更新
     *     $ wp ss generate_and_sync_complete --skip-svg-to-png --skip-png-to-filf --skip-oss-upload --verbose --allow-root
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function generate_and_sync_complete($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);

        // 产品生成参数
        $max_parallel = isset($assoc_args['max-parallel']) ? intval($assoc_args['max-parallel']) : 4;
        $batch_size = isset($assoc_args['batch-size']) ? intval($assoc_args['batch-size']) : 5;
        $cpu_threshold = isset($assoc_args['cpu-threshold']) ? intval($assoc_args['cpu-threshold']) : 80;
        $memory_threshold = isset($assoc_args['memory-threshold']) ? intval($assoc_args['memory-threshold']) : 85;

        // 转换参数
        $threads = isset($assoc_args['threads']) ? intval($assoc_args['threads']) : 16;
        $conversion_batch_size = isset($assoc_args['conversion-batch-size']) ? intval($assoc_args['conversion-batch-size']) : 50;

        // 流程控制参数
        $skip_product_generation = isset($assoc_args['skip-product-generation']);
        $skip_svg_to_png = isset($assoc_args['skip-svg-to-png']);
        $skip_png_to_filf = isset($assoc_args['skip-png-to-filf']);
        $skip_oss_upload = isset($assoc_args['skip-oss-upload']);
        $skip_cleanup = isset($assoc_args['skip-cleanup']);
        $auto_cleanup = isset($assoc_args['auto-cleanup']);
        $no_overwrite = isset($assoc_args['no-overwrite']);
        $backup_old = isset($assoc_args['backup-old']);

        WP_CLI::log('🚀 开始一键式产品生成和OSS同步完整流程...');
        WP_CLI::log("配置: 并行数={$max_parallel}, 批次={$batch_size}, 线程数={$threads}, 转换批次={$conversion_batch_size}");

        $total_start_time = microtime(true);
        $step_results = array();

        // 步骤1: 产品生成和JSON记录更新
        if (!$skip_product_generation) {
            WP_CLI::log('');
            WP_CLI::log('📦 步骤1: 产品生成和JSON记录更新');
            WP_CLI::log('=====================================');

            $step_start_time = microtime(true);

            // 子步骤1.1: 确保JSON记录存在
            WP_CLI::log('🔍 子步骤1.1: 检查并生成JSON记录...');
            $json_step_start = microtime(true);

            // 构建JSON记录生成参数
            $json_args = array();
            if (isset($assoc_args['category'])) {
                $json_args['category'] = $assoc_args['category'];
            }
            if ($verbose) {
                $json_args['verbose'] = true;
            }

            // 调用JSON记录生成（确保所有类目都有JSON记录）
            $this->generate_svg_records(array(), $json_args);

            $json_duration = round(microtime(true) - $json_step_start, 2);
            WP_CLI::log("✅ JSON记录检查完成，耗时: {$json_duration}秒");

            // 子步骤1.2: 产品生成
            WP_CLI::log('🏭 子步骤1.2: 开始产品生成...');
            $product_step_start = microtime(true);

            // 构建产品生成参数
            $product_args = array();
            if (isset($assoc_args['category'])) {
                $product_args['category'] = $assoc_args['category'];
            }
            if ($verbose) {
                $product_args['verbose'] = true;
            }
            $product_args['max-parallel'] = $max_parallel;
            $product_args['batch-size'] = $batch_size;
            $product_args['cpu-threshold'] = $cpu_threshold;
            $product_args['memory-threshold'] = $memory_threshold;

            // 调用产品生成
            $this->generate_products(array(), $product_args);

            $product_duration = round(microtime(true) - $product_step_start, 2);
            $step_duration = round(microtime(true) - $step_start_time, 2);

            $step_results['product_generation'] = array(
                'duration' => $step_duration,
                'json_duration' => $json_duration,
                'product_duration' => $product_duration,
                'success' => true
            );

            WP_CLI::log("✅ 产品生成完成，耗时: {$product_duration}秒");
            WP_CLI::log("✅ 步骤1总耗时: {$step_duration}秒");
        }

        // 步骤2: 转换和同步优化流程
        WP_CLI::log('');
        WP_CLI::log('🔄 步骤2: 转换和同步优化流程（SVG→PNG→FILF→OSS）');
        WP_CLI::log('================================================');

        $step_start_time = microtime(true);

        // 构建转换参数
        $convert_args = array();
        if (isset($assoc_args['category'])) {
            $convert_args['category'] = $assoc_args['category'];
        }
        if ($verbose) {
            $convert_args['verbose'] = true;
        }
        $convert_args['threads'] = $threads;
        $convert_args['batch-size'] = $conversion_batch_size;

        if ($skip_svg_to_png) {
            $convert_args['skip-svg-to-png'] = true;
        }
        if ($skip_png_to_filf) {
            $convert_args['skip-png-to-filf'] = true;
        }
        if ($skip_oss_upload) {
            $convert_args['skip-upload'] = true;
        }
        if ($no_overwrite) {
            $convert_args['no-overwrite'] = true;
        }
        if ($backup_old) {
            $convert_args['backup-old'] = true;
        }
        if ($auto_cleanup) {
            $convert_args['auto-cleanup'] = true;
        }

        // 调用转换和同步
        $this->convert_and_sync_optimized(array(), $convert_args);

        $step_duration = round(microtime(true) - $step_start_time, 2);
        $step_results['conversion_sync'] = array(
            'duration' => $step_duration,
            'success' => true
        );

        WP_CLI::log("✅ 转换和同步完成，耗时: {$step_duration}秒");

        // 步骤3: 清理本地文件（可选）
        if (!$skip_cleanup) {
            WP_CLI::log('');
            WP_CLI::log('🗑️  步骤3: 清理本地文件');
            WP_CLI::log('====================');

            $step_start_time = microtime(true);

            // 构建清理参数
            $cleanup_args = array();
            if (isset($assoc_args['category'])) {
                $cleanup_args['category'] = $assoc_args['category'];
            }
            if ($verbose) {
                $cleanup_args['verbose'] = true;
            }
            $cleanup_args['force'] = true; // 自动确认清理

            // 调用清理本地FILF文件
            $this->cleanup_local_filf(array(), $cleanup_args);

            $step_duration = round(microtime(true) - $step_start_time, 2);
            $step_results['cleanup'] = array(
                'duration' => $step_duration,
                'success' => true
            );

            WP_CLI::log("✅ 本地文件清理完成，耗时: {$step_duration}秒");
        }

        // 显示总结
        $total_duration = round(microtime(true) - $total_start_time, 2);

        WP_CLI::log('');
        WP_CLI::log('🎉 一键式完整流程执行完成！');
        WP_CLI::log('================================');
        WP_CLI::log("总耗时: {$total_duration}秒");

        foreach ($step_results as $step => $result) {
            $step_name = $this->get_step_display_name($step);
            WP_CLI::log("  {$step_name}: {$result['duration']}秒");
        }

        WP_CLI::success('所有步骤执行完成！产品生成、JSON记录更新、OSS同步和本地清理已全部完成。');
    }

    /**
     * 获取步骤显示名称
     */
    private function get_step_display_name($step) {
        $names = array(
            'product_generation' => '📦 产品生成',
            'conversion_sync' => '🔄 转换同步',
            'cleanup' => '🗑️  文件清理'
        );

        return isset($names[$step]) ? $names[$step] : $step;
    }

    /**
     * 【已弃用】使用 Rclone 将 SVG 文件直接同步到 OSS
     *
     * @deprecated 此命令已弃用，请使用 migrate_svg_to_oss 代替
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 将所有底层类目的SVG文件同步到OSS
     *     $ wp ss sync_svg_to_oss_rclone
     *     # 或者使用连字符版本
     *     $ wp ss sync-svg-to-oss-rclone
     *
     *     # 将指定类目的SVG文件同步到OSS
     *     $ wp ss sync_svg_to_oss_rclone --category=123
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function sync_svg_to_oss_rclone($args, $assoc_args) {
        // 显示弃用警告
        WP_CLI::warning('⚠️  此命令已弃用！请使用 "wp ss migrate_svg_to_oss" 代替。');
        WP_CLI::warning('⚠️  Rclone 同步方式已不再维护，建议使用 SDK 同步方式。');
        WP_CLI::confirm('确定要继续使用已弃用的 Rclone 同步方式吗？');

        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('🚀 开始使用 Rclone 将 SVG 文件直接同步到 OSS...');

        // 检查 Rclone 是否可用
        $rclone_check = shell_exec('which rclone 2>/dev/null');
        if (empty($rclone_check)) {
            WP_CLI::error('Rclone 未安装或不在 PATH 中，请先安装 Rclone');
            return;
        }

        // 检查 Rclone 配置
        $config_check = shell_exec('rclone config show aliyun 2>/dev/null');
        if (empty($config_check)) {
            WP_CLI::error('Rclone 配置 "aliyun" 不存在，请先配置 Rclone');
            return;
        }

        if ($verbose) {
            WP_CLI::log('✅ Rclone 环境检查通过');
        }

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $categories_to_process = $this->get_categories_to_process($assoc_args);

        if (empty($categories_to_process)) {
            WP_CLI::error("没有找到要处理的类目");
            return;
        }

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('Rclone同步到OSS', count($categories_to_process));

        $success_count = 0;
        $error_count = 0;
        $total_migrated = 0;
        $total_errors = 0;

        foreach ($categories_to_process as $category) {
            if ($verbose) {
                WP_CLI::log("正在同步类目 \"{$category->name}\" (ID: {$category->term_id}) 的SVG文件...");
            }

            $result = $svg_file_manager->sync_category_to_oss_rclone($category->term_id);

            if ($result['success']) {
                $success_count++;
                $total_migrated += $result['migrated_count'];
                $total_errors += $result['error_count'];

                if ($verbose) {
                    WP_CLI::log("✅ " . $result['message']);
                    if (!empty($result['rclone_output'])) {
                        WP_CLI::log("Rclone 输出: " . $result['rclone_output']);
                    }
                }
            } else {
                $error_count++;
                WP_CLI::warning("❌ 类目 \"{$category->name}\": " . $result['message']);
                if ($verbose && !empty($result['rclone_output'])) {
                    WP_CLI::log("Rclone 输出: " . $result['rclone_output']);
                }
            }

            $progress->tick();
        }

        $progress->finish();

        WP_CLI::success("Rclone SVG文件同步完成！成功处理 {$success_count} 个类目，失败 {$error_count} 个");
        WP_CLI::log("同步统计：新同步 {$total_migrated} 个文件，错误 {$total_errors} 个文件");
    }

    /**
     * 多线程转换SVG到PNG到FILF并同步到OSS
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--threads=<number>]
     * : 转换线程数（默认16，建议根据CPU核心数调整）
     *
     * [--batch-size=<number>]
     * : 批次大小（默认50，每批处理的文件数量）
     *
     * [--skip-svg-to-png]
     * : 跳过SVG到PNG转换（如果已经转换过）
     *
     * [--skip-png-to-filf]
     * : 跳过PNG到FILF转换（如果已经转换过）
     *
     * [--skip-upload]
     * : 跳过上传到OSS（仅进行本地转换）
     *
     * [--no-overwrite]
     * : 不覆盖OSS中已存在的文件
     *
     * [--backup-old]
     * : 覆盖前备份OSS中的旧文件
     *
     * [--auto-cleanup]
     * : 自动清理：转换完成后立即删除原始SVG文件，OSS上传完成后立即删除本地FILF文件
     *
     * [--cleanup-svg-after-conversion]
     * : 仅在SVG→PNG转换完成后立即清理原始SVG文件
     *
     * [--cleanup-filf-after-upload]
     * : 仅在OSS上传完成后立即清理本地FILF文件
     *
     * [--verbose]
     * : 显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 完整流程：SVG→PNG→FILF→OSS（推荐）
     *     $ wp ss convert_and_sync_optimized --threads=32 --batch-size=100 --verbose
     *
     *     # 完整流程 + 自动清理（推荐，节省磁盘空间）
     *     $ wp ss convert_and_sync_optimized --auto-cleanup --threads=32 --batch-size=100 --verbose
     *
     *     # 处理指定类目
     *     $ wp ss convert_and_sync_optimized --category=123 --threads=16 --verbose
     *
     *     # 仅转换，不上传
     *     $ wp ss convert_and_sync_optimized --skip-upload --threads=24 --verbose
     *
     *     # 从PNG开始（跳过SVG转换）
     *     $ wp ss convert_and_sync_optimized --skip-svg-to-png --threads=20 --verbose
     *
     *     # 仅清理转换后的SVG文件
     *     $ wp ss convert_and_sync_optimized --cleanup-svg-after-conversion --threads=16 --verbose
     *
     *     # 仅清理上传后的FILF文件
     *     $ wp ss convert_and_sync_optimized --cleanup-filf-after-upload --threads=16 --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function convert_and_sync_optimized($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);
        $threads = isset($assoc_args['threads']) ? intval($assoc_args['threads']) : 16;
        $batch_size = isset($assoc_args['batch-size']) ? intval($assoc_args['batch-size']) : 50;
        $skip_svg_to_png = isset($assoc_args['skip-svg-to-png']);
        $skip_png_to_filf = isset($assoc_args['skip-png-to-filf']);
        $skip_upload = isset($assoc_args['skip-upload']);
        $no_overwrite = isset($assoc_args['no-overwrite']);
        $backup_old = isset($assoc_args['backup-old']);

        // 【新增】自动清理参数
        $auto_cleanup = isset($assoc_args['auto-cleanup']);
        $cleanup_svg_after_conversion = isset($assoc_args['cleanup-svg-after-conversion']) || $auto_cleanup;
        $cleanup_filf_after_upload = isset($assoc_args['cleanup-filf-after-upload']) || $auto_cleanup;

        // 验证参数
        $threads = max(1, min(48, $threads)); // 限制在1-48之间
        $batch_size = max(10, min(200, $batch_size)); // 限制在10-200之间

        WP_CLI::log('🚀 开始优化转换和同步流程...');
        WP_CLI::log("配置: 线程数={$threads}, 批次大小={$batch_size}");

        // 显示清理配置
        if ($auto_cleanup) {
            WP_CLI::log("🧹 自动清理: 启用（转换后清理SVG，上传后清理FILF）");
        } else {
            $cleanup_info = array();
            if ($cleanup_svg_after_conversion) {
                $cleanup_info[] = "转换后清理SVG";
            }
            if ($cleanup_filf_after_upload) {
                $cleanup_info[] = "上传后清理FILF";
            }
            if (!empty($cleanup_info)) {
                WP_CLI::log("🧹 清理配置: " . implode(", ", $cleanup_info));
            }
        }

        // 检查必要工具
        $this->check_conversion_tools();

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $categories_to_process = $this->get_categories_to_process($assoc_args);

        if (empty($categories_to_process)) {
            WP_CLI::error("没有找到要处理的类目");
            return;
        }

        // 创建进度条
        $total_steps = count($categories_to_process) * (($skip_svg_to_png ? 0 : 1) + ($skip_png_to_filf ? 0 : 1) + ($skip_upload ? 0 : 1));
        $progress = \WP_CLI\Utils\make_progress_bar('优化转换和同步', $total_steps);

        $success_count = 0;
        $error_count = 0;
        $total_converted_svg = 0;
        $total_converted_png = 0;
        $total_uploaded = 0;
        $total_compression_ratio = 0;

        foreach ($categories_to_process as $category) {
            if ($verbose) {
                WP_CLI::log("处理类目 \"{$category->name}\" (ID: {$category->term_id})...");
            }

            $category_success = true;

            // 步骤1: SVG转PNG
            if (!$skip_svg_to_png) {
                if ($verbose) {
                    WP_CLI::log("  🔄 SVG转PNG...");
                }

                $svg_result = $svg_file_manager->convert_svg_to_png_multithread($category->term_id, $threads, $batch_size);

                if ($svg_result['success']) {
                    $total_converted_svg += $svg_result['converted'];
                    if ($verbose) {
                        WP_CLI::log("  ✅ " . $svg_result['message']);
                    }

                    // 【新增】转换完成后立即清理原始SVG文件
                    if ($cleanup_svg_after_conversion && $svg_result['converted'] > 0) {
                        if ($verbose) {
                            WP_CLI::log("  🧹 清理转换后的原始SVG文件...");
                        }

                        $cleanup_result = $this->cleanup_converted_svg_files($category->term_id, $verbose);
                        if ($cleanup_result['success']) {
                            if ($verbose) {
                                WP_CLI::log("  ✅ 清理完成: 删除 {$cleanup_result['deleted_count']} 个SVG文件，释放 " . $this->format_bytes($cleanup_result['freed_space']));
                            }
                        } else {
                            WP_CLI::warning("  ⚠️  SVG文件清理失败: " . $cleanup_result['message']);
                        }
                    }
                } else {
                    $category_success = false;
                    WP_CLI::warning("  ❌ SVG转PNG失败: " . $svg_result['message']);
                }

                $progress->tick();
            }

            // 步骤2: PNG转FILF
            if (!$skip_png_to_filf && $category_success) {
                if ($verbose) {
                    WP_CLI::log("  🗜️  PNG转FILF...");
                }

                $filf_result = $svg_file_manager->convert_png_to_filf_multithread($category->term_id, $threads, $batch_size);

                if ($filf_result['success']) {
                    $total_converted_png += $filf_result['converted'];
                    $total_compression_ratio += $filf_result['compression_ratio'];
                    if ($verbose) {
                        WP_CLI::log("  ✅ " . $filf_result['message']);
                    }
                } else {
                    $category_success = false;
                    WP_CLI::warning("  ❌ PNG转FILF失败: " . $filf_result['message']);
                }

                $progress->tick();
            }

            // 步骤3: 上传到OSS
            if (!$skip_upload && $category_success) {
                if ($verbose) {
                    WP_CLI::log("  ☁️  上传到OSS...");
                }

                $upload_result = $this->upload_filf_to_oss($category->term_id, $verbose, !$no_overwrite, $backup_old);

                if ($upload_result['success']) {
                    $total_uploaded += $upload_result['uploaded_count'];
                    if ($verbose) {
                        WP_CLI::log("  ✅ " . $upload_result['message']);
                    }

                    // 【新增】OSS上传完成后立即清理本地FILF文件
                    if ($cleanup_filf_after_upload && $upload_result['uploaded_count'] > 0) {
                        if ($verbose) {
                            WP_CLI::log("  🧹 清理上传后的本地FILF文件...");
                        }

                        $filf_cleanup_result = $svg_file_manager->cleanup_local_filf_files($category->term_id, $verbose);
                        if ($filf_cleanup_result['success']) {
                            if ($verbose) {
                                WP_CLI::log("  ✅ FILF清理完成: 删除 {$filf_cleanup_result['deleted_count']} 个文件，释放 " . $this->format_bytes($filf_cleanup_result['freed_space']));
                            }
                        } else {
                            WP_CLI::warning("  ⚠️  FILF文件清理失败: " . $filf_cleanup_result['message']);
                        }
                    }
                } else {
                    $category_success = false;
                    WP_CLI::warning("  ❌ OSS上传失败: " . $upload_result['message']);
                }

                $progress->tick();
            }

            if ($category_success) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        $progress->finish();

        // 显示统计信息
        WP_CLI::success("优化转换和同步完成！");
        WP_CLI::log("📊 处理统计:");
        WP_CLI::log("  ✅ 成功类目: {$success_count}");
        WP_CLI::log("  ❌ 失败类目: {$error_count}");

        if (!$skip_svg_to_png) {
            WP_CLI::log("  🔄 SVG转PNG: {$total_converted_svg} 个文件");
        }

        if (!$skip_png_to_filf) {
            $avg_compression = $success_count > 0 ? round($total_compression_ratio / $success_count, 2) : 0;
            WP_CLI::log("  🗜️  PNG转FILF: {$total_converted_png} 个文件，平均压缩率: {$avg_compression}%");
        }

        if (!$skip_upload) {
            WP_CLI::log("  ☁️  OSS上传: {$total_uploaded} 个文件");
        }
    }

    /**
     * 清理PNG产品数据
     *
     * ## OPTIONS
     *
     * [--template_id=<id>]
     * : 指定要清理的模板产品ID（支持逗号分隔多个ID）
     *
     * [--all]
     * : 清理所有PNG产品
     *
     * [--dry-run]
     * : 预览模式，不实际删除数据
     *
     * [--force]
     * : 强制删除，跳过确认提示
     *
     * [--max_parallel=<number>]
     * : 最大并行进程数（默认：8，最大：32）
     *
     * [--batch_size=<number>]
     * : 每批处理的产品数量（默认：50，最大：200）
     *
     * [--include-json-records]
     * : 同时清理JSON记录文件（默认：是）
     *
     * [--verbose]
     * : 显示详细清理过程
     *
     * ## EXAMPLES
     *
     *     # 清理指定模板的PNG产品
     *     $ wp ss clean_png_products --template_id=2025 --verbose
     *
     *     # 清理多个模板的PNG产品
     *     $ wp ss clean_png_products --template_id=2025,2026,2027 --verbose
     *
     *     # 预览模式（推荐先执行）
     *     $ wp ss clean_png_products --template_id=2025 --dry-run --verbose
     *
     *     # 并行清理（高性能）
     *     $ wp ss clean_png_products --template_id=2025 --max_parallel=16 --batch_size=100 --verbose
     *
     *     # 强制清理所有PNG产品
     *     $ wp ss clean_png_products --all --force --verbose
     *
     *     # 保留JSON记录文件
     *     $ wp ss clean_png_products --template_id=2025 --include-json-records=false --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function clean_png_products($args, $assoc_args) {
        // 检查CLI环境
        if (!defined('WP_CLI') || !WP_CLI) {
            WP_CLI::error('此命令只能在WP CLI环境中运行');
        }

        // 解析参数
        $template_ids_input = isset($assoc_args['template_id']) ? $assoc_args['template_id'] : '';
        $all_templates = isset($assoc_args['all']);
        $dry_run = isset($assoc_args['dry-run']);
        $force = isset($assoc_args['force']);
        $verbose = isset($assoc_args['verbose']);
        $max_parallel = isset($assoc_args['max_parallel']) ? intval($assoc_args['max_parallel']) : 8;
        $batch_size = isset($assoc_args['batch_size']) ? intval($assoc_args['batch_size']) : 50;

        // JSON记录清理控制参数（默认为true）
        $include_json_records = true;
        if (isset($assoc_args['include-json-records'])) {
            $include_json_records = filter_var($assoc_args['include-json-records'], FILTER_VALIDATE_BOOLEAN);
        }

        // 参数验证（移除最大限制，充分发挥服务器性能）
        $max_parallel = max(1, $max_parallel); // 移除32的限制
        $batch_size = max(10, $batch_size);    // 移除200的限制

        // 设置详细日志
        if ($verbose) {
            $this->verbose = true;
        }

        // 确定要处理的模板ID
        $template_ids = array();
        if ($all_templates) {
            // 获取所有PNG产品的模板ID
            $template_ids = $this->get_all_png_template_ids();
            if (empty($template_ids)) {
                WP_CLI::success('没有找到PNG产品，无需清理');
                return;
            }
        } elseif (!empty($template_ids_input)) {
            $template_ids = array_map('intval', array_filter(explode(',', $template_ids_input)));
        } else {
            WP_CLI::error('请指定要清理的模板ID（--template_id）或使用 --all 清理所有PNG产品');
        }

        // 验证模板ID
        $valid_template_ids = array();
        foreach ($template_ids as $template_id) {
            $template_product = wc_get_product($template_id);
            if ($template_product) {
                $valid_template_ids[] = $template_id;
            } else {
                WP_CLI::warning("模板产品不存在，跳过: {$template_id}");
            }
        }

        if (empty($valid_template_ids)) {
            WP_CLI::error('没有找到有效的模板产品');
        }

        // 显示清理配置
        WP_CLI::log('');
        WP_CLI::log('🗑️  PNG产品清理任务配置');
        WP_CLI::log('模板数量: ' . count($valid_template_ids));
        WP_CLI::log('模板ID: ' . implode(', ', $valid_template_ids));
        WP_CLI::log('并发配置: 最大并行=' . $max_parallel . ', 批次大小=' . $batch_size);
        WP_CLI::log('JSON记录清理: ' . ($include_json_records ? '是' : '否'));
        if ($dry_run) {
            WP_CLI::log('🔍 预览模式：不会实际删除数据');
        }
        WP_CLI::log('');

        // 获取要清理的产品统计
        $cleanup_stats = $this->get_png_cleanup_stats($valid_template_ids);

        if ($cleanup_stats['total_products'] == 0) {
            WP_CLI::success('没有找到要清理的PNG产品');
            return;
        }

        // 显示清理统计
        WP_CLI::log('📊 清理统计预览:');
        WP_CLI::log('  待清理产品: ' . $cleanup_stats['total_products'] . ' 个');
        WP_CLI::log('  预计清理图片: ' . $cleanup_stats['total_images'] . ' 个');
        WP_CLI::log('  涉及类目: ' . $cleanup_stats['categories_count'] . ' 个');
        if ($include_json_records) {
            WP_CLI::log('  JSON记录文件: ' . $cleanup_stats['json_files_count'] . ' 个');
        }
        WP_CLI::log('');

        // 确认操作
        if (!$dry_run && !$force) {
            WP_CLI::confirm('⚠️  此操作将永久删除PNG产品及其关联数据，确定要继续吗？');
        }

        // 执行清理
        $start_time = microtime(true);
        $this->execute_png_cleanup($valid_template_ids, $dry_run, $max_parallel, $batch_size, $include_json_records);
        $total_time = microtime(true) - $start_time;

        // 显示完成信息
        WP_CLI::log('');
        if ($dry_run) {
            WP_CLI::success('🔍 预览完成！总耗时: ' . round($total_time, 2) . ' 秒');
        } else {
            WP_CLI::success('🎉 PNG产品清理完成！总耗时: ' . round($total_time, 2) . ' 秒');
        }
    }

    /**
     * 清理本地FILF文件
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要清理的产品类目ID。如果不指定，将清理所有FILF文件。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--dry-run]
     * : 仅显示将要删除的文件，不实际删除
     *
     * [--verbose]
     * : 显示详细日志
     *
     * [--force]
     * : 强制删除，不询问确认
     *
     * ## EXAMPLES
     *
     *     # 清理所有本地FILF文件
     *     $ wp ss cleanup_local_filf --verbose
     *
     *     # 清理指定类目的FILF文件
     *     $ wp ss cleanup_local_filf --category=123 --verbose
     *
     *     # 预览将要删除的文件（不实际删除）
     *     $ wp ss cleanup_local_filf --dry-run --verbose
     *
     *     # 强制清理所有FILF文件
     *     $ wp ss cleanup_local_filf --force --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function cleanup_local_filf($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);
        $dry_run = isset($assoc_args['dry-run']);
        $force = isset($assoc_args['force']);

        WP_CLI::log('🧹 开始清理本地FILF文件...');

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $category_ids = array();
        if (isset($assoc_args['category'])) {
            $category_input = $assoc_args['category'];
            $category_ids = array_map('intval', explode(',', $category_input));
            $category_ids = array_filter($category_ids);
        }

        // 预览模式：显示将要删除的文件
        if ($dry_run) {
            WP_CLI::log('🔍 预览模式：显示将要删除的文件');
            $this->preview_filf_cleanup($category_ids, $verbose);
            return;
        }

        // 确认删除（除非使用--force）
        if (!$force) {
            if (empty($category_ids)) {
                $confirm_message = '确认要删除所有本地FILF文件吗？这将释放磁盘空间，但文件将无法恢复。';
            } else {
                $confirm_message = '确认要删除指定类目的本地FILF文件吗？文件将无法恢复。';
            }

            WP_CLI::confirm($confirm_message);
        }

        // 执行清理
        if (empty($category_ids)) {
            // 清理所有FILF文件 - 直接调用批量清理，不传递类目ID
            if ($verbose) {
                WP_CLI::log("开始批量清理所有本地FILF文件...");
            }
            $result = $svg_file_manager->batch_cleanup_local_filf_files(array(), $verbose);
        } else {
            // 清理指定类目的FILF文件
            $total_deleted = 0;
            $total_freed_space = 0;
            $processed_categories = 0;

            foreach ($category_ids as $category_id) {
                if ($verbose) {
                    WP_CLI::log("清理类目 ID: {$category_id}");
                }

                $result = $svg_file_manager->cleanup_local_filf_files($category_id, $verbose);

                if ($result['success']) {
                    $total_deleted += $result['deleted_count'];
                    $total_freed_space += $result['freed_space'];
                    $processed_categories++;

                    if ($verbose) {
                        WP_CLI::log("  ✅ " . $result['message']);
                    }
                } else {
                    WP_CLI::warning("  ❌ " . $result['message']);
                }
            }

            $result = array(
                'success' => true,
                'total_deleted' => $total_deleted,
                'total_freed_space' => $total_freed_space,
                'processed_categories' => $processed_categories
            );
        }

        // 显示结果
        if ($result['success']) {
            WP_CLI::success("FILF文件清理完成！");
            WP_CLI::log("📊 清理统计:");
            WP_CLI::log("  🗑️  删除文件: {$result['total_deleted']} 个");
            WP_CLI::log("  💾 释放空间: " . $this->format_bytes($result['total_freed_space']));
            WP_CLI::log("  📁 处理类目: {$result['processed_categories']} 个");
        } else {
            WP_CLI::error("FILF文件清理失败");
        }
    }

    /**
     * 预览FILF文件清理
     */
    private function preview_filf_cleanup($category_ids, $verbose) {
        $upload_dir = wp_upload_dir();
        $base_filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/';

        if (!is_dir($base_filf_dir)) {
            WP_CLI::log('📁 FILF基础目录不存在，无文件可清理');
            return;
        }

        $total_files = 0;
        $total_size = 0;
        $categories_found = 0;

        if (empty($category_ids)) {
            // 预览所有FILF目录
            $filf_dirs = glob($base_filf_dir . '*', GLOB_ONLYDIR);

            foreach ($filf_dirs as $dir) {
                $dir_name = basename($dir);
                $filf_files = glob($dir . '/*.flif');

                if (!empty($filf_files)) {
                    $categories_found++;
                    $dir_size = 0;

                    WP_CLI::log("📁 类目目录: {$dir_name}");

                    foreach ($filf_files as $file) {
                        $file_size = filesize($file);
                        $total_size += $file_size;
                        $dir_size += $file_size;
                        $total_files++;

                        if ($verbose) {
                            WP_CLI::log("  🗑️  " . basename($file) . " (" . $this->format_bytes($file_size) . ")");
                        }
                    }

                    WP_CLI::log("  📊 文件数: " . count($filf_files) . "，大小: " . $this->format_bytes($dir_size));
                }
            }
        } else {
            // 预览指定类目
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("类目 ID {$category_id} 不存在");
                    continue;
                }

                $safe_category_name = sanitize_title($term->name);
                $filf_dir = $base_filf_dir . $safe_category_name . '/';
                $filf_files = glob($filf_dir . '*.flif');

                if (!empty($filf_files)) {
                    $categories_found++;
                    $dir_size = 0;

                    WP_CLI::log("📁 类目: \"{$term->name}\" (ID: {$category_id})");

                    foreach ($filf_files as $file) {
                        $file_size = filesize($file);
                        $total_size += $file_size;
                        $dir_size += $file_size;
                        $total_files++;

                        if ($verbose) {
                            WP_CLI::log("  🗑️  " . basename($file) . " (" . $this->format_bytes($file_size) . ")");
                        }
                    }

                    WP_CLI::log("  📊 文件数: " . count($filf_files) . "，大小: " . $this->format_bytes($dir_size));
                } else {
                    WP_CLI::log("📁 类目: \"{$term->name}\" (ID: {$category_id}) - 无FILF文件");
                }
            }
        }

        WP_CLI::log("\n📊 预览总计:");
        WP_CLI::log("  📁 类目数: {$categories_found}");
        WP_CLI::log("  🗑️  文件数: {$total_files}");
        WP_CLI::log("  💾 总大小: " . $this->format_bytes($total_size));

        if ($total_files > 0) {
            WP_CLI::log("\n💡 使用 --force 参数可跳过确认直接删除");
        }
    }

    /**
     * 【新增】清理转换后的原始SVG文件
     *
     * @param int $category_id 类目ID
     * @param bool $verbose 是否显示详细日志
     * @return array 清理结果
     */
    private function cleanup_converted_svg_files($category_id, $verbose = false) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $upload_dir = wp_upload_dir();

        // 获取原始SVG目录和PNG目录
        $svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/' . $safe_category_name . '/';
        $png_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_png/' . $safe_category_name . '/';

        if (!is_dir($svg_dir)) {
            return array(
                'success' => true,
                'message' => 'SVG目录不存在，无需清理',
                'deleted_count' => 0,
                'freed_space' => 0
            );
        }

        // 获取所有SVG文件
        $svg_files = glob($svg_dir . '*.svg');
        if (empty($svg_files)) {
            return array(
                'success' => true,
                'message' => '没有找到SVG文件，无需清理',
                'deleted_count' => 0,
                'freed_space' => 0
            );
        }

        $deleted_count = 0;
        $freed_space = 0;
        $failed_files = array();

        foreach ($svg_files as $svg_file) {
            $svg_filename = basename($svg_file, '.svg');
            $corresponding_png = $png_dir . $svg_filename . '.png';

            // 只有当对应的PNG文件存在时才删除SVG文件
            if (file_exists($corresponding_png)) {
                $file_size = filesize($svg_file);
                $filename = basename($svg_file);

                if (unlink($svg_file)) {
                    $deleted_count++;
                    $freed_space += $file_size;

                    if ($verbose) {
                        error_log("[SVG清理] 删除已转换的SVG文件: {$filename} ({$this->format_bytes($file_size)})");
                    }
                } else {
                    $failed_files[] = $filename;
                    error_log("[SVG清理] 删除失败: {$filename}");
                }
            } else {
                if ($verbose) {
                    error_log("[SVG清理] 跳过未转换的SVG文件: " . basename($svg_file));
                }
            }
        }

        // 尝试删除空目录
        $remaining_files = glob($svg_dir . '*');
        if (empty($remaining_files)) {
            if (rmdir($svg_dir)) {
                if ($verbose) {
                    error_log("[SVG清理] 删除空目录: {$svg_dir}");
                }
            }
        }

        $message = "转换后SVG文件清理完成：删除 {$deleted_count} 个文件，释放空间 " . $this->format_bytes($freed_space);
        if (!empty($failed_files)) {
            $message .= "，失败 " . count($failed_files) . " 个文件";
        }

        error_log("[SVG清理] " . $message);

        return array(
            'success' => empty($failed_files),
            'message' => $message,
            'deleted_count' => $deleted_count,
            'failed_count' => count($failed_files),
            'freed_space' => $freed_space,
            'failed_files' => $failed_files
        );
    }

    /**
     * 格式化字节大小
     */
    private function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 清理已转移到 OSS 的本地 SVG 文件
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有底层类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--dry-run]
     * : 试运行模式，只显示将要删除的文件，不实际删除
     *
     * [--verbose]
     * : 是否显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 试运行：查看将要清理的文件
     *     $ wp ss cleanup_migrated_svg --dry-run
     *     # 或者使用连字符版本
     *     $ wp ss cleanup-migrated-svg --dry-run
     *
     *     # 实际清理已转移的SVG文件
     *     $ wp ss cleanup_migrated_svg
     *
     *     # 清理指定类目的已转移SVG文件
     *     $ wp ss cleanup_migrated_svg --category=123
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function cleanup_migrated_svg($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);
        $dry_run = isset($assoc_args['dry-run']);

        if ($dry_run) {
            WP_CLI::log('🔍 试运行模式：检查可清理的已转移 SVG 文件...');
        } else {
            WP_CLI::log('🗑️  开始清理已转移到 OSS 的本地 SVG 文件...');

            // 确认操作
            WP_CLI::confirm('⚠️  此操作将永久删除本地的已转移SVG文件，确定要继续吗？');
        }

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $categories_to_process = $this->get_categories_to_process($assoc_args);

        if (empty($categories_to_process)) {
            WP_CLI::error("没有找到要处理的类目");
            return;
        }

        // 创建进度条
        $progress_label = $dry_run ? '检查文件' : '清理文件';
        $progress = \WP_CLI\Utils\make_progress_bar($progress_label, count($categories_to_process));

        $success_count = 0;
        $error_count = 0;
        $total_cleanup = 0;
        $total_files_to_cleanup = 0;

        foreach ($categories_to_process as $category) {
            if ($verbose) {
                $action = $dry_run ? '检查' : '清理';
                WP_CLI::log("正在{$action}类目 \"{$category->name}\" (ID: {$category->term_id}) 的已转移SVG文件...");
            }

            $result = $svg_file_manager->cleanup_migrated_files($category->term_id, $dry_run);

            if ($result['success']) {
                $success_count++;
                if ($dry_run) {
                    $total_files_to_cleanup += count($result['files_to_cleanup']);
                } else {
                    $total_cleanup += $result['cleanup_count'];
                }

                if ($verbose) {
                    WP_CLI::log("✅ " . $result['message']);
                }
            } else {
                $error_count++;
                WP_CLI::warning("❌ 类目 \"{$category->name}\": " . $result['message']);
            }

            $progress->tick();
        }

        $progress->finish();

        if ($dry_run) {
            WP_CLI::success("试运行完成！检查了 {$success_count} 个类目，发现 {$total_files_to_cleanup} 个文件可以清理");
            if ($total_files_to_cleanup > 0) {
                WP_CLI::log("💡 要实际执行清理，请运行：wp ss cleanup_migrated_svg");
            }
        } else {
            WP_CLI::success("清理完成！成功处理 {$success_count} 个类目，失败 {$error_count} 个，总共清理 {$total_cleanup} 个本地文件");
        }
    }

    /**
     * 检查转换工具是否可用
     */
    private function check_conversion_tools() {
        $tools = array(
            'rsvg-convert' => 'rsvg-convert --version',
            'inkscape' => 'inkscape --version',
            'flif' => 'flif --version',
            'rclone' => 'rclone version'
        );

        $missing_tools = array();

        foreach ($tools as $tool => $check_command) {
            $output = shell_exec($check_command . ' 2>/dev/null');
            if (empty($output)) {
                $missing_tools[] = $tool;
            }
        }

        if (!empty($missing_tools)) {
            WP_CLI::error('缺少必要工具: ' . implode(', ', $missing_tools) . "\n请参考 FILF_INSTALLATION_GUIDE.md 安装缺失的工具");
            return;
        }

        WP_CLI::log('✅ 所有转换工具检查通过');
    }

    /**
     * 检查OSS中FILF文件的状态
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要检查的产品类目ID。如果不指定，将检查所有类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--verbose]
     * : 显示详细日志
     *
     * ## EXAMPLES
     *
     *     # 检查所有类目的OSS文件状态
     *     $ wp ss check_oss_filf --verbose
     *
     *     # 检查指定类目的OSS文件状态
     *     $ wp ss check_oss_filf --category=123 --verbose
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function check_oss_filf($args, $assoc_args) {
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('🔍 开始检查OSS中FILF文件状态...');

        $svg_file_manager = SS_SVG_File_Manager::get_instance();

        // 获取要处理的类目
        $category_ids = array();
        if (isset($assoc_args['category'])) {
            $category_input = $assoc_args['category'];
            $category_ids = array_map('intval', explode(',', $category_input));
            $category_ids = array_filter($category_ids);
        }

        if (empty($category_ids)) {
            // 获取所有有FILF文件的类目
            $category_ids = $this->get_categories_with_filf_files();
        }

        if (empty($category_ids)) {
            WP_CLI::warning('没有找到包含FILF文件的类目');
            return;
        }

        $total_files = 0;
        $total_existing = 0;
        $total_new = 0;
        $processed_categories = 0;

        foreach ($category_ids as $category_id) {
            $term = get_term($category_id, 'product_cat');
            if (!$term || is_wp_error($term)) {
                WP_CLI::warning("类目 ID {$category_id} 不存在");
                continue;
            }

            if ($verbose) {
                WP_CLI::log("检查类目: \"{$term->name}\" (ID: {$category_id})");
            }

            $result = $svg_file_manager->check_oss_filf_files($category_id, $verbose);

            if ($result['success']) {
                $total_files += $result['total_files'];
                $total_existing += $result['existing_files'];
                $total_new += $result['new_files'];
                $processed_categories++;

                WP_CLI::log("  📊 " . $result['message']);

                if ($result['existing_files'] > 0) {
                    WP_CLI::log("  ⚠️  发现 {$result['existing_files']} 个已存在的文件，上传时将被覆盖");
                }
            } else {
                WP_CLI::warning("  ❌ " . $result['message']);
            }
        }

        // 显示总结
        WP_CLI::success("OSS文件状态检查完成！");
        WP_CLI::log("📊 检查统计:");
        WP_CLI::log("  📁 检查类目: {$processed_categories} 个");
        WP_CLI::log("  📄 总文件数: {$total_files} 个");
        WP_CLI::log("  ✅ 已存在: {$total_existing} 个");
        WP_CLI::log("  🆕 新文件: {$total_new} 个");

        if ($total_existing > 0) {
            WP_CLI::log("");
            WP_CLI::log("💡 建议:");
            WP_CLI::log("  - 使用 --backup-old 参数备份旧文件");
            WP_CLI::log("  - 使用 --no-overwrite 参数跳过已存在的文件");
        }
    }

    /**
     * 获取包含FILF文件的类目ID列表
     */
    private function get_categories_with_filf_files() {
        $upload_dir = wp_upload_dir();
        $base_filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/';

        if (!is_dir($base_filf_dir)) {
            return array();
        }

        $category_ids = array();
        $filf_dirs = glob($base_filf_dir . '*', GLOB_ONLYDIR);

        foreach ($filf_dirs as $dir) {
            $dir_name = basename($dir);
            $filf_files = glob($dir . '/*.flif');

            if (!empty($filf_files)) {
                // 尝试从目录名找到对应的类目
                $terms = get_terms(array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => false,
                    'name' => $dir_name
                ));

                if (!empty($terms)) {
                    $category_ids[] = $terms[0]->term_id;
                } else {
                    // 尝试通过slug查找
                    $term = get_term_by('slug', $dir_name, 'product_cat');
                    if ($term) {
                        $category_ids[] = $term->term_id;
                    }
                }
            }
        }

        return array_unique($category_ids);
    }

    /**
     * 使用OSS Python SDK上传FILF文件到OSS
     */
    private function upload_filf_to_oss($category_id, $verbose = false, $overwrite = true, $backup_old = false) {
        $svg_file_manager = SS_SVG_File_Manager::get_instance();
        return $svg_file_manager->upload_filf_to_oss_python_sdk($category_id, $verbose, $overwrite, $backup_old);
    }

    /**
     * 生成前端产品类目SVG预览图
     *
     * ## OPTIONS
     *
     * [--category=<category_ids>]
     * : 指定要处理的产品类目ID。如果不指定，将处理所有适合的类目。
     * 可以使用逗号分隔多个类目ID，例如：--category=123,456,789
     *
     * [--all]
     * : 处理所有适合的产品类目（包括已有预设颜色的类目）
     *
     * [--force]
     * : 强制重新生成，即使已存在预览图
     *
     * [--batch-size=<number>]
     * : 每批处理的SVG文件数量（默认：8）
     *
     * [--max-concurrent=<number>]
     * : 最大并发进程数（默认：根据CPU核心数自动计算）
     *
     * [--verbose]
     * : 显示详细日志
     *
     * [--dry-run]
     * : 预览模式，不实际生成预览图
     *
     * [--skip-color-extraction]
     * : 跳过颜色提取，仅处理已有预设颜色的类目
     *
     * ## EXAMPLES
     *
     *     # 为所有适合的类目生成SVG预览图
     *     $ wp ss generate_frontend_previews --verbose --allow_root
     *
     *     # 为指定类目生成SVG预览图
     *     $ wp ss generate_frontend_previews --category=123,456 --verbose --allow_root
     *
     *     # 强制重新生成所有预览图
     *     $ wp ss generate_frontend_previews --all --force --verbose --allow_root
     *
     *     # 预览模式，查看将要处理的类目
     *     $ wp ss generate_frontend_previews --dry-run --verbose --allow_root
     *
     *     # 高性能模式处理
     *     $ wp ss generate_frontend_previews --batch-size=16 --max-concurrent=12 --verbose --allow_root
     *
     * @param array $args
     * @param array $assoc_args
     */
    public function generate_frontend_previews($args, $assoc_args) {
        $start_time = microtime(true);

        // 解析参数
        $verbose = isset($assoc_args['verbose']);
        $dry_run = isset($assoc_args['dry-run']);
        $force = isset($assoc_args['force']);
        $all_categories = isset($assoc_args['all']);
        $skip_color_extraction = isset($assoc_args['skip-color-extraction']);
        $batch_size = isset($assoc_args['batch-size']) ? intval($assoc_args['batch-size']) : 8;
        $max_concurrent = isset($assoc_args['max-concurrent']) ? intval($assoc_args['max-concurrent']) : 0;

        if ($verbose) {
            WP_CLI::log('🚀 开始前端SVG预览图生成任务');
            WP_CLI::log('参数配置:');
            WP_CLI::log('  - 批次大小: ' . $batch_size);
            WP_CLI::log('  - 最大并发: ' . ($max_concurrent > 0 ? $max_concurrent : '自动'));
            WP_CLI::log('  - 强制重新生成: ' . ($force ? '是' : '否'));
            WP_CLI::log('  - 预览模式: ' . ($dry_run ? '是' : '否'));
            WP_CLI::log('  - 跳过颜色提取: ' . ($skip_color_extraction ? '是' : '否'));
        }

        // 获取要处理的类目
        $categories = $this->get_frontend_preview_categories($assoc_args, $all_categories, $skip_color_extraction, $verbose);

        if (empty($categories)) {
            WP_CLI::warning('没有找到适合生成SVG预览图的产品类目');
            return;
        }

        WP_CLI::log(sprintf('找到 %d 个适合的产品类目', count($categories)));

        if ($dry_run) {
            WP_CLI::log('🔍 预览模式 - 将要处理的类目:');
            foreach ($categories as $category) {
                $preset_colors = get_term_meta($category->term_id, 'ss_category_colors', true);
                $thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
                $color_info = !empty($preset_colors) ? '有预设颜色' : (!empty($thumbnail_id) ? '有类目图片' : '需要颜色提取');
                WP_CLI::log(sprintf('  - ID: %d, 名称: %s, 颜色状态: %s',
                    $category->term_id, $category->name, $color_info));
            }
            WP_CLI::success('预览完成，使用 --verbose 参数查看详细信息');
            return;
        }

        // 初始化前端管理器
        if (!class_exists('SS_Frontend_Manager')) {
            require_once plugin_dir_path(__FILE__) . 'class-ss-frontend-manager.php';
        }
        $frontend_manager = new SS_Frontend_Manager();

        // 创建进度条
        $progress = \WP_CLI\Utils\make_progress_bar('生成SVG预览图', count($categories));

        $stats = [
            'total_categories' => count($categories),
            'processed_categories' => 0,
            'skipped_categories' => 0,
            'successful_categories' => 0,
            'failed_categories' => 0,
            'total_previews_generated' => 0,
            'errors' => []
        ];

        // 处理每个类目
        foreach ($categories as $category) {
            $category_start_time = microtime(true);

            try {
                $result = $this->process_category_frontend_previews(
                    $category,
                    $frontend_manager,
                    $force,
                    $batch_size,
                    $max_concurrent,
                    $verbose
                );

                if ($result['success']) {
                    $stats['successful_categories']++;
                    $stats['total_previews_generated'] += $result['previews_generated'];

                    if ($verbose) {
                        $duration = microtime(true) - $category_start_time;
                        WP_CLI::log(sprintf('✅ 类目 "%s" 处理完成，生成 %d 个预览图，耗时 %.2f 秒',
                            $category->name, $result['previews_generated'], $duration));
                    }
                } else {
                    $stats['failed_categories']++;
                    $stats['errors'][] = sprintf('类目 "%s" (ID: %d): %s',
                        $category->name, $category->term_id, $result['error']);

                    if ($verbose) {
                        WP_CLI::warning(sprintf('❌ 类目 "%s" 处理失败: %s',
                            $category->name, $result['error']));
                    }
                }

                $stats['processed_categories']++;

            } catch (Exception $e) {
                $stats['failed_categories']++;
                $stats['processed_categories']++;
                $error_msg = sprintf('类目 "%s" (ID: %d) 处理异常: %s',
                    $category->name, $category->term_id, $e->getMessage());
                $stats['errors'][] = $error_msg;

                if ($verbose) {
                    WP_CLI::warning('❌ ' . $error_msg);
                }
            }

            $progress->tick();
        }

        $progress->finish();

        // 显示统计结果
        $total_time = microtime(true) - $start_time;
        WP_CLI::log('');
        WP_CLI::log('📊 处理完成统计:');
        WP_CLI::log(sprintf('  总类目数: %d', $stats['total_categories']));
        WP_CLI::log(sprintf('  成功处理: %d', $stats['successful_categories']));
        WP_CLI::log(sprintf('  处理失败: %d', $stats['failed_categories']));
        WP_CLI::log(sprintf('  跳过类目: %d', $stats['skipped_categories']));
        WP_CLI::log(sprintf('  生成预览图: %d', $stats['total_previews_generated']));
        WP_CLI::log(sprintf('  总耗时: %.2f 秒', $total_time));

        if (!empty($stats['errors'])) {
            WP_CLI::log('');
            WP_CLI::log('❌ 错误详情:');
            foreach ($stats['errors'] as $error) {
                WP_CLI::log('  - ' . $error);
            }
        }

        if ($stats['successful_categories'] > 0) {
            WP_CLI::success(sprintf('成功为 %d 个类目生成了 %d 个SVG预览图',
                $stats['successful_categories'], $stats['total_previews_generated']));
        } else {
            WP_CLI::error('没有成功处理任何类目');
        }
    }

    /**
     * 获取要处理的类目列表
     *
     * @param array $assoc_args 关联参数
     * @return array 类目数组
     */
    private function get_categories_to_process($assoc_args) {
        if (isset($assoc_args['category'])) {
            // 解析指定的类目ID
            $category_ids = explode(',', $assoc_args['category']);
            $category_ids = array_map('trim', $category_ids);
            $category_ids = array_map('intval', $category_ids);
            $category_ids = array_filter($category_ids);

            $categories = array();
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if ($term && !is_wp_error($term)) {
                    $categories[] = $term;
                } else {
                    WP_CLI::warning("无法找到ID为 {$category_id} 的产品类目");
                }
            }

            return $categories;
        } else {
            // 获取所有底层类目
            return $this->get_leaf_categories();
        }
    }

    /**
     * 获取适合生成前端预览图的类目列表
     */
    private function get_frontend_preview_categories($assoc_args, $all_categories, $skip_color_extraction, $verbose) {
        $categories = [];

        if (isset($assoc_args['category'])) {
            // 处理指定的类目ID
            $category_ids = explode(',', $assoc_args['category']);
            $category_ids = array_map('trim', $category_ids);
            $category_ids = array_map('intval', $category_ids);
            $category_ids = array_filter($category_ids);

            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if ($term && !is_wp_error($term)) {
                    $categories[] = $term;
                } else {
                    WP_CLI::warning("无法找到ID为 {$category_id} 的产品类目");
                }
            }
        } else {
            // 获取所有产品类目
            $all_terms = get_terms([
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'number' => 0
            ]);

            if ($verbose) {
                WP_CLI::log(sprintf('扫描到 %d 个产品类目', count($all_terms)));
            }

            foreach ($all_terms as $term) {
                // 检查是否适合生成前端预览图
                if ($this->is_category_suitable_for_frontend_preview($term, $all_categories, $skip_color_extraction, $verbose)) {
                    $categories[] = $term;
                }
            }
        }

        return $categories;
    }

    /**
     * 检查类目是否适合生成前端预览图
     */
    private function is_category_suitable_for_frontend_preview($term, $all_categories, $skip_color_extraction, $verbose) {
        // 检查是否已通过WP CLI生成过产品
        $wp_cli_generated = get_term_meta($term->term_id, 'ss_wp_cli_products_generated', true);
        if (!empty($wp_cli_generated)) {
            if ($verbose) {
                WP_CLI::log(sprintf('跳过类目 "%s" (ID: %d) - 已通过WP CLI生成产品', $term->name, $term->term_id));
            }
            return false;
        }

        // 检查是否为自定义匹配类目
        $is_custom_matching = get_term_meta($term->term_id, '_is_custom_matching_category', true);
        if ($is_custom_matching) {
            if ($verbose) {
                WP_CLI::log(sprintf('跳过类目 "%s" (ID: %d) - 自定义匹配类目', $term->name, $term->term_id));
            }
            return false;
        }

        // 检查颜色配置
        $preset_colors = get_term_meta($term->term_id, 'ss_category_colors', true);
        $thumbnail_id = get_term_meta($term->term_id, 'thumbnail_id', true);

        if (!empty($preset_colors)) {
            // 有预设颜色，检查是否有类目图片
            if (empty($thumbnail_id)) {
                if ($verbose) {
                    WP_CLI::log(sprintf('跳过类目 "%s" (ID: %d) - 有预设颜色但没有类目图片', $term->name, $term->term_id));
                }
                return false;
            }
            return true; // 有预设颜色且有类目图片
        }

        if ($skip_color_extraction) {
            if ($verbose) {
                WP_CLI::log(sprintf('跳过类目 "%s" (ID: %d) - 没有预设颜色且跳过颜色提取', $term->name, $term->term_id));
            }
            return false;
        }

        // 检查是否有类目图片用于颜色提取
        if (empty($thumbnail_id)) {
            if ($verbose) {
                WP_CLI::log(sprintf('跳过类目 "%s" (ID: %d) - 没有类目图片用于颜色提取', $term->name, $term->term_id));
            }
            return false;
        }

        return true; // 有类目图片，可以进行颜色提取
    }

    /**
     * 处理单个类目的前端预览图生成
     */
    private function process_category_frontend_previews($category, $frontend_manager, $force, $batch_size, $max_concurrent, $verbose) {
        $result = [
            'success' => false,
            'previews_generated' => 0,
            'error' => ''
        ];

        try {
            // 检查是否已生成过预览图
            $frontend_preview_generated = get_term_meta($category->term_id, 'ss_frontend_preview_generated', true);
            if (!$force && !empty($frontend_preview_generated)) {
                $result['error'] = '已生成过前端预览图，使用 --force 强制重新生成';
                return $result;
            }

            // 获取SVG文件列表
            $svg_files = $frontend_manager->get_svg_files();
            if (empty($svg_files)) {
                $result['error'] = '没有找到SVG文件';
                return $result;
            }

            // 获取或提取颜色
            $preset_colors = get_term_meta($category->term_id, 'ss_category_colors', true);
            if (empty($preset_colors)) {
                $preset_colors = $frontend_manager->extract_colors_from_category_image($category->term_id);
                if (empty($preset_colors)) {
                    $result['error'] = '无法获取或提取类目颜色';
                    return $result;
                }
            }

            if ($verbose) {
                WP_CLI::log(sprintf('开始处理类目 "%s" (ID: %d)，SVG文件数: %d，颜色: %s',
                    $category->name, $category->term_id, count($svg_files), $preset_colors[0]));
            }

            // 准备类目数据
            $category_data = [
                'term_id' => $category->term_id,
                'name' => $frontend_manager->get_safe_category_name($category->name),
                'colors' => $preset_colors
            ];

            // 检查现有预览图
            $upload_dir = wp_upload_dir();
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $category_data['name'] . '/previews/';

            $files_to_process = [];
            foreach ($svg_files as $index => $svg_file) {
                $file_name = basename($svg_file);
                $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
                $preview_path = $preview_dir . $safe_name . '_preview.webp';

                if ($force || !file_exists($preview_path)) {
                    $files_to_process[] = [
                        'file' => $svg_file,
                        'index' => $index
                    ];
                }
            }

            if (empty($files_to_process)) {
                $result['error'] = '所有预览图已存在，使用 --force 强制重新生成';
                return $result;
            }

            if ($verbose) {
                WP_CLI::log(sprintf('需要生成 %d 个预览图', count($files_to_process)));
            }

            // 创建完整的类目目录结构并设置权限
            $this->create_category_directory_structure($category_data['name']);

            // 批量处理预览图生成
            $generated_count = $this->generate_previews_batch($files_to_process, $category_data, $frontend_manager, $batch_size, $max_concurrent, $verbose);

            if ($generated_count > 0) {
                // 标记类目已生成前端预览图
                update_term_meta($category->term_id, 'ss_frontend_preview_generated', current_time('timestamp'));

                $result['success'] = true;
                $result['previews_generated'] = $generated_count;
            } else {
                $result['error'] = '没有成功生成任何预览图';
            }

        } catch (Exception $e) {
            $result['error'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 批量生成预览图
     */
    private function generate_previews_batch($files_to_process, $category_data, $frontend_manager, $batch_size, $max_concurrent, $verbose) {
        $generated_count = 0;
        $total_files = count($files_to_process);

        // 自动计算最优并发数
        if ($max_concurrent <= 0) {
            $cpu_cores = $this->detect_cpu_cores();
            $max_concurrent = min($cpu_cores * 2, 16); // CPU核心数的2倍，最大16
        }

        if ($verbose) {
            WP_CLI::log(sprintf('使用批次大小: %d，最大并发: %d', $batch_size, $max_concurrent));
        }

        // 分批处理
        $batches = array_chunk($files_to_process, $batch_size);

        foreach ($batches as $batch_index => $batch) {
            if ($verbose) {
                WP_CLI::log(sprintf('处理批次 %d/%d，包含 %d 个文件',
                    $batch_index + 1, count($batches), count($batch)));
            }

            $batch_generated = $this->process_preview_batch($batch, $category_data, $frontend_manager, $max_concurrent, $verbose);
            $generated_count += $batch_generated;

            // 批次间短暂休息，避免系统过载
            if ($batch_index < count($batches) - 1) {
                usleep(100000); // 0.1秒
            }
        }

        return $generated_count;
    }

    /**
     * 处理单个预览图批次
     */
    private function process_preview_batch($batch, $category_data, $frontend_manager, $max_concurrent, $verbose) {
        $generated_count = 0;
        $active_processes = [];

        foreach ($batch as $file_data) {
            // 等待进程槽位
            while (count($active_processes) >= $max_concurrent) {
                $this->wait_for_preview_process_completion($active_processes, $generated_count, $verbose);
            }

            // 启动新的预览生成进程
            $process = $this->start_preview_generation_process($file_data, $category_data, $frontend_manager);
            if ($process) {
                $active_processes[] = $process;
            }
        }

        // 等待所有进程完成
        while (!empty($active_processes)) {
            $this->wait_for_preview_process_completion($active_processes, $generated_count, $verbose);
        }

        return $generated_count;
    }

    /**
     * 启动预览生成进程
     */
    private function start_preview_generation_process($file_data, $category_data, $frontend_manager) {
        try {
            // 直接调用前端管理器的预览生成方法
            $result = $frontend_manager->generate_svg_preview_file(
                $file_data['file'],
                $category_data['name'],
                $category_data['colors'][0],
                true,
                $category_data['term_id']
            );

            return [
                'file' => $file_data['file'],
                'success' => $result !== false,
                'completed' => true
            ];

        } catch (Exception $e) {
            return [
                'file' => $file_data['file'],
                'success' => false,
                'completed' => true,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 等待预览进程完成
     */
    private function wait_for_preview_process_completion(&$active_processes, &$generated_count, $verbose) {
        foreach ($active_processes as $key => $process) {
            if ($process['completed']) {
                if ($process['success']) {
                    $generated_count++;
                    if ($verbose) {
                        WP_CLI::log('✅ 预览图生成成功: ' . basename($process['file']));
                    }
                } else {
                    if ($verbose) {
                        $error = isset($process['error']) ? $process['error'] : '未知错误';
                        WP_CLI::log('❌ 预览图生成失败: ' . basename($process['file']) . ' - ' . $error);
                    }
                }
                unset($active_processes[$key]);
            }
        }

        if (!empty($active_processes)) {
            usleep(50000); // 0.05秒
        }
    }

    /**
     * 检测CPU核心数
     */
    private function detect_cpu_cores() {
        $cores = 4; // 默认值

        if (function_exists('shell_exec')) {
            // Linux/Unix
            $output = shell_exec('nproc 2>/dev/null');
            if ($output) {
                $cores = intval(trim($output));
            } else {
                // macOS
                $output = shell_exec('sysctl -n hw.ncpu 2>/dev/null');
                if ($output) {
                    $cores = intval(trim($output));
                }
            }
        }

        return max(1, $cores);
    }

    /**
     * 修复现有目录的权限设置
     *
     * ## OPTIONS
     *
     * [--category-ids=<ids>]
     * : 指定要修复的类目ID，多个ID用逗号分隔
     *
     * [--all-categories]
     * : 修复所有类目的权限
     *
     * [--path=<path>]
     * : 指定要修复的具体路径
     *
     * [--verbose]
     * : 显示详细信息
     *
     * ## EXAMPLES
     *
     *     wp ss fix_permissions --category-ids=123,456 --verbose
     *     wp ss fix_permissions --all-categories
     *     wp ss fix_permissions --path=/www/wwwroot/matchbeast.com/wp-content/uploads/shoe-svg-generator/frontend-gallery/adidas-adi2000-chalk-white-black
     */
    public function fix_permissions($args, $assoc_args) {
        $category_ids = isset($assoc_args['category-ids']) ? explode(',', $assoc_args['category-ids']) : [];
        $all_categories = isset($assoc_args['all-categories']);
        $specific_path = isset($assoc_args['path']) ? $assoc_args['path'] : '';
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('🔧 开始修复目录权限...');

        if (!empty($specific_path)) {
            // 修复指定路径
            if (!file_exists($specific_path)) {
                WP_CLI::error("指定路径不存在: {$specific_path}");
                return;
            }

            WP_CLI::log("修复指定路径权限: {$specific_path}");
            $this->fix_directory_tree_permissions($specific_path);
            WP_CLI::success("指定路径权限修复完成");
            return;
        }

        // 获取要处理的类目
        $categories = [];
        if ($all_categories) {
            $categories = get_terms([
                'taxonomy' => 'product_cat',
                'hide_empty' => false,
                'meta_query' => [
                    [
                        'key' => 'ss_category_colors',
                        'compare' => 'EXISTS'
                    ]
                ]
            ]);
        } else {
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if (!$term || is_wp_error($term)) {
                    WP_CLI::warning("类目ID {$category_id} 不存在，跳过");
                    continue;
                }
                $categories[] = $term;
            }
        }

        if (empty($categories)) {
            WP_CLI::warning('没有找到要处理的类目');
            return;
        }

        WP_CLI::log(sprintf('准备修复 %d 个类目的权限', count($categories)));

        $upload_dir = wp_upload_dir();
        $fixed_count = 0;
        $error_count = 0;

        foreach ($categories as $category) {
            $safe_category_name = sanitize_title($category->name);
            $category_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/';

            if ($verbose) {
                WP_CLI::log("处理类目: {$category->name} (ID: {$category->term_id})");
                WP_CLI::log("目录路径: {$category_dir}");
            }

            if (file_exists($category_dir)) {
                try {
                    $this->fix_directory_tree_permissions($category_dir);
                    $fixed_count++;
                    
                    if ($verbose) {
                        WP_CLI::log("✅ 类目 {$category->name} 权限修复完成");
                    }
                } catch (Exception $e) {
                    $error_count++;
                    WP_CLI::warning("❌ 类目 {$category->name} 权限修复失败: " . $e->getMessage());
                }
            } else {
                if ($verbose) {
                    WP_CLI::log("⚠️ 类目 {$category->name} 目录不存在，跳过");
                }
            }
        }

        WP_CLI::log('');
        WP_CLI::log('权限修复完成统计:');
        WP_CLI::log("✅ 成功修复: {$fixed_count} 个类目");
        if ($error_count > 0) {
            WP_CLI::log("❌ 修复失败: {$error_count} 个类目");
        }

        WP_CLI::success('权限修复任务完成！');
    }

    /**
     * 从PNG文件生成产品
     *
     * 直接使用PNG文件生成产品，跳过SVG处理流程
     *
     * ## OPTIONS
     *
     * [--template_id=<id>]
     * : 模板产品ID
     *
     * [--max_parallel=<num>]
     * : 最大并行进程数（默认8，高性能服务器可设置32-64）
     *
     * [--cpu_threshold=<percent>]
     * : CPU使用率阈值（默认90%，高性能服务器可设置95%）
     *
     * [--batch_size=<num>]
     * : 每批处理的文件数量（默认10，高性能服务器可设置20-50）
     *
     * [--memory_threshold=<percent>]
     * : 内存使用率阈值（默认85%，高性能服务器可设置90%）
     *
     * [--dry_run]
     * : 预览模式，不实际生成产品
     *
     * [--verbose]
     * : 显示详细日志
     *
     * [--white]
     * : 设置第一张画廊图背景为白色
     *
     * [--black]
     * : 设置第一张画廊图背景为黑色
     *
     * ## EXAMPLES
     *
     *     # 基本使用
     *     wp ss generate_products_from_png --template_id=2025 --verbose --allow_root
     *
     *     # 高性能服务器配置（CPU 96核，内存182GB）
     *     wp ss generate_products_from_png --template_id=2025 --max_parallel=32 --cpu_threshold=95 --batch_size=20 --memory_threshold=90 --verbose --allow_root
     *
     *     # 预览模式
     *     wp ss generate_products_from_png --template_id=2025 --dry_run --verbose --allow_root
     *
     *     # 设置白色背景
     *     wp ss generate_products_from_png --template_id=2025 --white --verbose --allow_root
     *
     *     # 设置黑色背景
     *     wp ss generate_products_from_png --template_id=2025 --black --verbose --allow_root
     *
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function generate_products_from_png($args, $assoc_args) {
        // 【修复】设置环境变量避免第三方插件警告
        if (!isset($_SERVER['SERVER_PORT'])) {
            $_SERVER['SERVER_PORT'] = '80';
        }
        if (!isset($_SERVER['HTTP_HOST'])) {
            $_SERVER['HTTP_HOST'] = 'localhost';
        }

        $start_time = microtime(true);

        // 解析参数
        $template_id = isset($assoc_args['template_id']) ? intval($assoc_args['template_id']) : 0;
        $max_parallel = isset($assoc_args['max_parallel']) ? intval($assoc_args['max_parallel']) : 8;
        $cpu_threshold = isset($assoc_args['cpu_threshold']) ? intval($assoc_args['cpu_threshold']) : 90;
        $batch_size = isset($assoc_args['batch_size']) ? intval($assoc_args['batch_size']) : 10;
        $memory_threshold = isset($assoc_args['memory_threshold']) ? intval($assoc_args['memory_threshold']) : 85;
        $dry_run = isset($assoc_args['dry_run']);
        $verbose = isset($assoc_args['verbose']);

        // 背景色参数处理
        $background_color = 'transparent'; // 默认透明
        if (isset($assoc_args['white'])) {
            $background_color = 'white';
            error_log('[PNG产品生成][CLI] 检测到--white参数，设置背景色为: white');
        } elseif (isset($assoc_args['black'])) {
            $background_color = 'black';
            error_log('[PNG产品生成][CLI] 检测到--black参数，设置背景色为: black');
        } else {
            error_log('[PNG产品生成][CLI] 未检测到背景色参数，使用默认透明背景');
        }

        error_log('[PNG产品生成][CLI] 最终背景色设置: ' . $background_color);

        // 参数验证
        if (!$template_id) {
            WP_CLI::error('必须指定模板产品ID (--template_id)');
        }

        // 验证模板产品是否存在
        $template_product = wc_get_product($template_id);
        if (!$template_product) {
            WP_CLI::error('模板产品不存在: ' . $template_id);
        }

        if ($verbose) {
            WP_CLI::log('');
            WP_CLI::log('🚀 PNG产品生成任务开始');
            WP_CLI::log('模板产品: ' . $template_product->get_name() . ' (ID: ' . $template_id . ')');
            WP_CLI::log('并发配置: 最大并行=' . $max_parallel . ', CPU阈值=' . $cpu_threshold . '%, 内存阈值=' . $memory_threshold . '%');
            WP_CLI::log('批次大小: ' . $batch_size);
            if ($dry_run) {
                WP_CLI::log('🔍 预览模式：不会实际生成产品');
            }
            WP_CLI::log('');
        }

        // 获取PNG文件管理器实例
        $png_manager = SS_PNG_File_Manager::get_instance();

        // 扫描PNG目录结构
        $directory_structure = $png_manager->scan_png_directory_structure();

        if (empty($directory_structure)) {
            WP_CLI::error('未找到PNG文件或目录结构为空');
        }

        if ($verbose) {
            WP_CLI::log('📁 发现 ' . count($directory_structure) . ' 个文件夹:');
            foreach ($directory_structure as $folder_name => $folder_info) {
                WP_CLI::log('  - ' . $folder_name . ': ' . $folder_info['file_count'] . ' 个PNG文件');
            }
            WP_CLI::log('');
        }

        $total_folders = count($directory_structure);
        $total_files = array_sum(array_column($directory_structure, 'file_count'));
        $processed_folders = 0;
        $processed_files = 0;
        $created_products = 0;
        $skipped_files = 0;

        if ($verbose) {
            WP_CLI::log('📊 统计信息:');
            WP_CLI::log('  模板产品ID: ' . $template_id);
            WP_CLI::log('  画廊图背景色: ' . $background_color);
            WP_CLI::log('  总文件夹数: ' . $total_folders);
            WP_CLI::log('  总文件数: ' . $total_files);
            WP_CLI::log('');
        }

        // 处理每个文件夹
        foreach ($directory_structure as $folder_name => $folder_info) {
            $folder_start_time = microtime(true);

            if ($verbose) {
                WP_CLI::log('📂 处理文件夹: ' . $folder_name . ' (' . $folder_info['file_count'] . ' 个文件)');
            }

            // 创建或获取产品类目
            $category_id = $png_manager->get_or_create_category($folder_name);
            if (!$category_id) {
                WP_CLI::warning('无法创建类目: ' . $folder_name . '，跳过此文件夹');
                continue;
            }

            $category_name = get_term($category_id, 'product_cat')->name;

            if ($verbose) {
                WP_CLI::log('  ✅ 类目: ' . $category_name . ' (ID: ' . $category_id . ')');
            }

            // 【修复】确保JSON记录存在，如果不存在则生成
            $json_record = $png_manager->get_folder_json_record($folder_name, $category_id);
            if (!$json_record) {
                if (!$dry_run) {
                    if ($verbose) {
                        WP_CLI::log('  📝 生成JSON记录文件...');
                    }
                    $result = $png_manager->generate_folder_json_record($folder_name, $category_id);
                    if (!$result['success']) {
                        WP_CLI::warning('无法生成JSON记录: ' . $folder_name . ' - ' . $result['message']);
                        continue;
                    }
                    $json_record = $png_manager->get_folder_json_record($folder_name, $category_id);
                    if ($verbose) {
                        WP_CLI::log('  ✅ JSON记录生成成功，包含 ' . $result['files_count'] . ' 个文件');
                    }
                } else {
                    // 预览模式下，直接扫描文件夹获取文件列表
                    $folder_path = '/www/wwwroot/printeeque.com/wp-content/uploads/png/' . $folder_name;
                    $png_files = glob($folder_path . '/*.png');
                    $unprocessed_files = array();
                    foreach ($png_files as $file_path) {
                        $unprocessed_files[] = array(
                            'filename' => basename($file_path),
                            'file_path' => $file_path
                        );
                    }

                    if ($verbose) {
                        WP_CLI::log('  🔍 预览模式 - 将要处理的文件:');
                        foreach ($unprocessed_files as $file_info) {
                            WP_CLI::log('    - ' . $file_info['filename']);
                        }
                    }
                    $processed_folders++;
                    continue;
                }
            }

            // 【修复】获取未处理的文件（基于模板ID）
            $unprocessed_files = $png_manager->get_unprocessed_files($folder_name, $category_id, $template_id);

            if ($verbose) {
                WP_CLI::log('  🔍 检查未处理文件，模板ID: ' . $template_id);
                WP_CLI::log('  📋 JSON记录中总文件数: ' . (isset($json_record['total_files']) ? $json_record['total_files'] : '未知'));
                WP_CLI::log('  📋 未处理文件数: ' . count($unprocessed_files));

                // 显示未处理文件列表
                if (!empty($unprocessed_files)) {
                    WP_CLI::log('  📋 未处理文件列表:');
                    foreach ($unprocessed_files as $file_info) {
                        WP_CLI::log('    - ' . $file_info['filename']);
                    }
                }
            }

            if (empty($unprocessed_files)) {
                if ($verbose) {
                    WP_CLI::log('  ⏭️  所有文件已处理，跳过');
                }
                $processed_folders++;
                continue;
            }

            if ($verbose) {
                WP_CLI::log('  📋 未处理文件: ' . count($unprocessed_files) . ' 个');
            }

            if ($dry_run) {
                if ($verbose) {
                    WP_CLI::log('  🔍 预览模式 - 将要处理的文件:');
                    foreach ($unprocessed_files as $file_info) {
                        WP_CLI::log('    - ' . $file_info['filename']);
                    }
                }
                $processed_folders++;
                continue;
            }

            // 批量处理文件
            $folder_processed = 0;
            $folder_created = 0;
            $batches = array_chunk($unprocessed_files, $batch_size);

            foreach ($batches as $batch_index => $batch_files) {
                if ($verbose) {
                    WP_CLI::log('  🔄 处理批次 ' . ($batch_index + 1) . '/' . count($batches) . ' (' . count($batch_files) . ' 个文件)');
                }

                // 检查系统资源
                if ($this->should_wait_for_resources($cpu_threshold, $memory_threshold)) {
                    if ($verbose) {
                        WP_CLI::log('  ⏸️  等待系统资源释放...');
                    }
                    $this->wait_for_png_resources($cpu_threshold, $memory_threshold, $verbose);
                }

                // 【修复】优先使用真正的并发处理
                $use_safe_concurrent = $max_parallel > 1 && count($batch_files) > 1 &&
                                      class_exists('SS_Safe_Concurrent_Processor') &&
                                      SS_Safe_Concurrent_Processor::is_supported();

                if ($use_safe_concurrent) {
                    if ($verbose) {
                        WP_CLI::log('    🚀 使用真正的并发处理模式: ' . min($max_parallel, count($batch_files)) . ' 个并发进程');
                    }

                    // 真正的并发处理
                    $concurrent_processor = SS_Safe_Concurrent_Processor::get_instance();
                    $batch_results = $concurrent_processor->process_png_files_safe(
                        $batch_files,
                        $template_id,
                        $category_id,
                        $category_name,
                        $folder_name,
                        min($max_parallel, count($batch_files)),
                        $background_color,
                        $verbose
                    );

                    // 处理并发结果
                    foreach ($batch_results as $result) {
                        if ($result['success']) {
                            // 更新JSON记录（使用完整的相对路径）
                            $png_manager->update_file_processed_status(
                                $folder_relative_path,
                                $category_id,
                                $result['filename'],
                                $template_id,
                                $result['product_id']
                            );

                            $folder_created++;
                            $created_products++;

                            if ($verbose) {
                                WP_CLI::log('    ✅ 并发创建产品成功 (ID: ' . $result['product_id'] . ') - 文件: ' . $result['filename'] . ' - 耗时: ' . round($result['total_duration'], 2) . 's');
                            }
                        } else {
                            $skipped_files++;
                            if ($verbose) {
                                WP_CLI::log('    ❌ 并发处理失败: ' . $result['filename'] . ' - ' . $result['error']);
                            }
                        }
                        $folder_processed++;
                        $processed_files++;
                    }
                } else {
                    // 串行处理回退
                    if ($max_parallel > 1) {
                        if ($verbose) {
                            WP_CLI::log('    ⚠️ 真正的并发处理不可用，使用串行处理');
                        }
                    } else if ($verbose) {
                        WP_CLI::log('    📝 使用串行处理模式');
                    }

                // 优化的串行处理
                foreach ($batch_files as $file_index => $file_info) {
                    $file_start_time = microtime(true);

                    // 【修复】确保文件路径正确
                    $png_filename = $file_info['filename'];
                    $png_file_path = isset($file_info['file_path']) ? $file_info['file_path'] :
                        ('/www/wwwroot/printeeque.com/wp-content/uploads/png/' . $folder_name . '/' . $png_filename);

                    if ($verbose) {
                        WP_CLI::log('    🔧 处理文件 ' . ($file_index + 1) . '/' . count($batch_files) . ': ' . $png_filename);
                        WP_CLI::log('    📁 文件路径: ' . $png_file_path);
                    }

                    // 【修复】验证文件是否存在
                    if (!file_exists($png_file_path)) {
                        if ($verbose) {
                            WP_CLI::log('    ❌ 文件不存在，跳过: ' . $png_file_path);
                        }
                        $skipped_files++;
                        $folder_processed++;
                        $processed_files++;
                        continue;
                    }

                    // 检查内存使用情况
                    $memory_usage = memory_get_usage(true);
                    $memory_limit = $this->get_memory_limit();
                    $memory_percent = ($memory_usage / $memory_limit) * 100;

                    if ($memory_percent > 85) {
                        if ($verbose) {
                            WP_CLI::log('    ⚠️ 内存使用率过高 (' . round($memory_percent, 1) . '%)，清理缓存...');
                        }
                        wp_cache_flush();
                        gc_collect_cycles();
                    }

                    try {
                        // 生成产品
                        $generator = SS_SVG_Generator::get_instance();
                        $product_id = $generator->ss_create_product_from_png(
                            $png_file_path,
                            $template_id,
                            $category_id,
                            $category_name,
                            $background_color
                        );

                        if ($product_id) {
                            // 更新JSON记录（包含模板ID，使用完整的相对路径）
                            $png_manager->update_file_processed_status(
                                $folder_relative_path,
                                $category_id,
                                $png_filename,
                                $template_id,
                                $product_id
                            );

                            $folder_created++;
                            $created_products++;

                            $file_duration = microtime(true) - $file_start_time;
                            if ($verbose) {
                                WP_CLI::log('    ✅ 成功创建产品 (ID: ' . $product_id . ') - 耗时: ' . round($file_duration, 2) . 's');
                            }
                        } else {
                            $skipped_files++;
                            if ($verbose) {
                                WP_CLI::log('    ❌ 产品创建失败或跳过');
                            }
                        }
                    } catch (Exception $e) {
                        $skipped_files++;
                        if ($verbose) {
                            WP_CLI::log('    ❌ 处理异常: ' . $e->getMessage());
                        }
                        error_log('[PNG生成] 处理文件异常: ' . $png_filename . ' - ' . $e->getMessage());
                    }

                    $folder_processed++;
                    $processed_files++;

                    // 短暂休息，避免系统过载
                    if (count($batch_files) > 5) {
                        usleep(100000); // 0.1秒
                    }
                }
                }

                // 批次间短暂休息
                if ($batch_index < count($batches) - 1) {
                    usleep(500000); // 0.5秒
                }
            }

            $folder_duration = microtime(true) - $folder_start_time;
            if ($verbose) {
                WP_CLI::log('  📊 文件夹处理完成: 处理 ' . $folder_processed . ' 个文件，创建 ' . $folder_created . ' 个产品 - 耗时: ' . round($folder_duration, 2) . 's');
                WP_CLI::log('');
            }

            $processed_folders++;
        }

        $total_duration = microtime(true) - $start_time;

        WP_CLI::log('');
        WP_CLI::log('🎉 PNG产品生成任务完成！');
        WP_CLI::log('');
        WP_CLI::log('📊 最终统计:');
        WP_CLI::log('  处理文件夹: ' . $processed_folders . '/' . $total_folders);
        WP_CLI::log('  处理文件: ' . $processed_files . '/' . $total_files);
        WP_CLI::log('  创建产品: ' . $created_products);
        WP_CLI::log('  跳过文件: ' . $skipped_files);
        WP_CLI::log('  总耗时: ' . round($total_duration, 2) . ' 秒');

        if ($created_products > 0) {
            $avg_time = $total_duration / $created_products;
            WP_CLI::log('  平均每个产品: ' . round($avg_time, 2) . ' 秒');
        }

        WP_CLI::success('PNG产品生成任务成功完成！');
    }

    /**
     * 多模板PNG产品生成命令
     *
     * ## OPTIONS
     *
     * [--template_ids=<ids>]
     * : 模板产品ID列表，逗号分隔（如果不指定，将自动查找所有模板产品）
     *
     * [--max_parallel=<number>]
     * : 最大并发进程数 (默认: 16)
     *
     * [--batch_size=<number>]
     * : 每批处理文件数 (默认: 20)
     *
     * [--auto_adjust]
     * : 自动调整并发数基于系统性能
     *
     * [--dry_run]
     * : 预览模式，不实际生成产品
     *
     * [--verbose]
     * : 显示详细信息
     *
     * ## EXAMPLES
     *
     *     # 自动处理所有模板产品
     *     wp ss png_multi_template --auto_adjust --verbose --allow-root
     *
     *     # 指定特定模板产品
     *     wp ss png_multi_template --template_ids=2025,2026 --max_parallel=32 --verbose --allow-root
     *
     *     # 高性能服务器配置（192 vCPU 384 GiB）
     *     wp ss png_multi_template --max_parallel=80 --batch_size=30 --auto_adjust --verbose --allow-root
     *
     * @when after_wp_load
     */
    public function png_multi_template($args, $assoc_args) {
        // 【修复】设置环境变量避免第三方插件警告
        if (!isset($_SERVER['SERVER_PORT'])) {
            $_SERVER['SERVER_PORT'] = '80';
        }
        if (!isset($_SERVER['HTTP_HOST'])) {
            $_SERVER['HTTP_HOST'] = 'localhost';
        }

        $start_time = microtime(true);

        // 解析参数
        $template_ids_input = isset($assoc_args['template_ids']) ? $assoc_args['template_ids'] : '';
        $max_parallel = isset($assoc_args['max_parallel']) ? intval($assoc_args['max_parallel']) : 16;
        $batch_size = isset($assoc_args['batch_size']) ? intval($assoc_args['batch_size']) : 20;
        $auto_adjust = isset($assoc_args['auto_adjust']);
        $dry_run = isset($assoc_args['dry_run']);
        $verbose = isset($assoc_args['verbose']);

        WP_CLI::log('🚀 多模板PNG产品生成任务开始');
        WP_CLI::log('并发配置: 最大并行=' . $max_parallel . ', 批次大小=' . $batch_size);
        WP_CLI::log('自动调整: ' . ($auto_adjust ? '启用' : '禁用'));

        if ($dry_run) {
            WP_CLI::log('🔍 预览模式 - 不会实际生成产品');
        }

        // 获取模板产品列表
        $template_ids = array();
        if (!empty($template_ids_input)) {
            // 解析指定的模板ID
            $ids_array = explode(',', $template_ids_input);
            foreach ($ids_array as $id) {
                $id = trim($id);
                if (is_numeric($id) && intval($id) > 0) {
                    $template_ids[] = intval($id);
                }
            }
        } else {
            // 自动查找所有模板产品
            $template_ids = $this->find_all_template_products();
        }

        if (empty($template_ids)) {
            WP_CLI::error('未找到任何模板产品');
        }

        WP_CLI::log('📋 发现 ' . count($template_ids) . ' 个模板产品');

        // 验证模板产品
        $valid_templates = array();
        foreach ($template_ids as $template_id) {
            $template_product = wc_get_product($template_id);
            if ($template_product) {
                $valid_templates[] = array(
                    'id' => $template_id,
                    'name' => $template_product->get_name(),
                    'product' => $template_product
                );
                WP_CLI::log('  ✅ 模板: ' . $template_product->get_name() . ' (ID: ' . $template_id . ')');
            } else {
                WP_CLI::warning('  ❌ 模板产品不存在: ' . $template_id);
            }
        }

        if (empty($valid_templates)) {
            WP_CLI::error('没有有效的模板产品');
        }

        // 获取PNG文件管理器实例
        $png_manager = SS_PNG_File_Manager::get_instance();

        // 扫描PNG目录结构
        $directory_structure = $png_manager->scan_png_directory_structure();

        if (empty($directory_structure)) {
            WP_CLI::error('未找到PNG文件或目录结构为空');
        }

        WP_CLI::log('📁 发现 ' . count($directory_structure) . ' 个文件夹');

        $total_files = 0;
        $total_processed = 0;
        $total_created = 0;
        $total_skipped = 0;

        // 处理每个模板
        foreach ($valid_templates as $template_info) {
            $template_id = $template_info['id'];
            $template_name = $template_info['name'];

            WP_CLI::log('');
            WP_CLI::log('🎯 开始处理模板: ' . $template_name . ' (ID: ' . $template_id . ')');

            // 使用现有的png_concurrent命令逻辑
            $template_stats = $this->process_template_png_files(
                $template_id,
                $directory_structure,
                $png_manager,
                $max_parallel,
                $batch_size,
                $auto_adjust,
                $dry_run,
                'transparent', // 默认透明背景，png_multi_template命令暂不支持背景色参数
                $verbose
            );

            $total_files += $template_stats['files'];
            $total_processed += $template_stats['processed'];
            $total_created += $template_stats['created'];
            $total_skipped += $template_stats['skipped'];

            WP_CLI::log('  📊 模板 ' . $template_name . ' 统计:');
            WP_CLI::log('    处理文件: ' . $template_stats['processed']);
            WP_CLI::log('    创建产品: ' . $template_stats['created']);
            WP_CLI::log('    跳过文件: ' . $template_stats['skipped']);
        }

        $total_duration = microtime(true) - $start_time;

        WP_CLI::log('');
        WP_CLI::log('🎉 多模板PNG产品生成完成！');
        WP_CLI::log('');
        WP_CLI::log('📊 最终统计:');
        WP_CLI::log('  处理模板: ' . count($valid_templates));
        WP_CLI::log('  总文件数: ' . $total_files);
        WP_CLI::log('  处理文件: ' . $total_processed);
        WP_CLI::log('  创建产品: ' . $total_created);
        WP_CLI::log('  跳过文件: ' . $total_skipped);
        WP_CLI::log('  总耗时: ' . round($total_duration, 2) . ' 秒');

        if ($total_created > 0) {
            $avg_time = $total_duration / $total_created;
            WP_CLI::log('  平均耗时: ' . round($avg_time, 2) . ' 秒/产品');
        }

        WP_CLI::success('多模板PNG产品生成任务成功完成！');
    }

    /**
     * 查找所有模板产品
     *
     * @return array 模板产品ID数组
     */
    private function find_all_template_products() {
        $template_products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_ss_is_template',
                    'value' => '1',
                ),
            ),
            'fields' => 'ids',
        ));

        return $template_products;
    }

    /**
     * 处理单个模板的PNG文件
     *
     * @param int $template_id 模板ID
     * @param array $directory_structure 目录结构
     * @param object $png_manager PNG管理器实例
     * @param int $max_parallel 最大并发数
     * @param int $batch_size 批次大小
     * @param bool $auto_adjust 自动调整
     * @param bool $dry_run 预览模式
     * @param string $background_color 画廊图背景色
     * @param bool $verbose 详细模式
     * @return array 处理统计
     */
    private function process_template_png_files($template_id, $directory_structure, $png_manager, $max_parallel, $batch_size, $auto_adjust, $dry_run, $background_color, $verbose) {
        $stats = array(
            'files' => 0,
            'processed' => 0,
            'created' => 0,
            'skipped' => 0
        );

        // 检查并发处理支持
        if (!SS_Safe_Concurrent_Processor::is_supported()) {
            WP_CLI::warning('系统不支持安全并发处理，使用串行模式');
            $max_parallel = 1;
        }

        $concurrent_processor = SS_Safe_Concurrent_Processor::get_instance();

        foreach ($directory_structure as $folder_path => $folder_info) {
            if ($verbose) {
                WP_CLI::log('    📂 处理文件夹: ' . $folder_path . ' (' . $folder_info['file_count'] . ' 个文件)');
            }

            // 获取或创建类目（支持层级结构）
            $category_id = $png_manager->get_or_create_category($folder_path);
            if (!$category_id) {
                WP_CLI::warning('    无法创建类目: ' . $folder_path);
                continue;
            }

            $category_term = get_term($category_id, 'product_cat');
            $category_name = $category_term->name;

            if ($verbose) {
                $category_hierarchy = $this->get_category_hierarchy_string($category_term);
                WP_CLI::log('      ✅ 类目: ' . $category_hierarchy . ' (ID: ' . $category_id . ')');
            }

            // 生成或获取JSON记录（使用完整的相对路径）
            $folder_relative_path = $folder_info['relative_path']; // 使用完整的相对路径
            $folder_name = $folder_info['folder_name']; // 保留文件夹名称用于显示

            $json_record = $png_manager->get_folder_json_record($folder_relative_path, $category_id);
            if (!$json_record && !$dry_run) {
                $result = $png_manager->generate_folder_json_record($folder_relative_path, $category_id);
                if (!$result['success']) {
                    WP_CLI::warning('    无法生成JSON记录: ' . $folder_relative_path . ' - ' . $result['message']);
                    continue;
                }
                $json_record = $png_manager->get_folder_json_record($folder_relative_path, $category_id);
            }

            // 获取未处理文件（使用完整的相对路径）
            $unprocessed_files = $png_manager->get_unprocessed_files($folder_relative_path, $category_id, $template_id);

            if (empty($unprocessed_files)) {
                if ($verbose) {
                    WP_CLI::log('      ✅ 所有文件已处理完成');
                }
                continue;
            }

            if ($verbose) {
                WP_CLI::log('      📋 未处理文件: ' . count($unprocessed_files) . ' 个');
            }

            $stats['files'] += count($unprocessed_files);

            if ($dry_run) {
                if ($verbose) {
                    WP_CLI::log('      🔍 预览模式 - 将要处理的文件:');
                    foreach (array_slice($unprocessed_files, 0, 5) as $file_info) {
                        WP_CLI::log('        - ' . $file_info['filename']);
                    }
                    if (count($unprocessed_files) > 5) {
                        WP_CLI::log('        ... 还有 ' . (count($unprocessed_files) - 5) . ' 个文件');
                    }
                }
                continue;
            }

            // 分批处理
            $batches = array_chunk($unprocessed_files, $batch_size);

            foreach ($batches as $batch_index => $batch_files) {
                if ($verbose) {
                    WP_CLI::log('      🔄 处理批次 ' . ($batch_index + 1) . '/' . count($batches) . ' (' . count($batch_files) . ' 个文件)');
                }

                // 性能监控和自动调整
                if ($auto_adjust) {
                    $performance = $concurrent_processor->get_system_performance();
                    $adjusted_parallel = $concurrent_processor->adjust_concurrent_count($max_parallel, $performance);

                    if ($adjusted_parallel != $max_parallel && $verbose) {
                        WP_CLI::log('        🎛️ 自动调整并发数: ' . $max_parallel . ' → ' . $adjusted_parallel);
                        $max_parallel = $adjusted_parallel;
                    }
                }

                // 并发处理批次（传递完整的相对路径）
                $batch_results = $concurrent_processor->process_png_files_safe(
                    $batch_files,
                    $template_id,
                    $category_id,
                    $category_name,
                    $folder_relative_path, // 使用完整的相对路径
                    $max_parallel,
                    $background_color,
                    $verbose
                );

                // 【修复】处理并发处理器返回的结果数组，生成统计信息
                $batch_stats = array(
                    'processed' => 0,
                    'created' => 0,
                    'skipped' => 0
                );

                if (is_array($batch_results)) {
                    foreach ($batch_results as $result) {
                        $batch_stats['processed']++;
                        if (isset($result['success']) && $result['success'] && isset($result['product_id']) && $result['product_id']) {
                            $batch_stats['created']++;
                        } else {
                            $batch_stats['skipped']++;
                        }
                    }
                }

                $stats['processed'] += $batch_stats['processed'];
                $stats['created'] += $batch_stats['created'];
                $stats['skipped'] += $batch_stats['skipped'];

                if ($verbose) {
                    WP_CLI::log('        📊 批次结果: 处理=' . $batch_stats['processed'] . ', 创建=' . $batch_stats['created'] . ', 跳过=' . $batch_stats['skipped']);
                }
            }
        }

        return $stats;
    }

    /**
     * 获取类目层级显示字符串
     *
     * @param object $term 类目对象
     * @return string 层级字符串
     */
    private function get_category_hierarchy_string($term) {
        $hierarchy = array();
        $current_term = $term;

        // 向上遍历获取完整层级
        while ($current_term) {
            array_unshift($hierarchy, $current_term->name);

            if ($current_term->parent > 0) {
                $current_term = get_term($current_term->parent, 'product_cat');
                if (is_wp_error($current_term)) {
                    break;
                }
            } else {
                break;
            }
        }

        return implode(' > ', $hierarchy);
    }

    /**
     * 检查是否需要等待系统资源
     */
    private function should_wait_for_resources($cpu_threshold, $memory_threshold) {
        $cpu_usage = $this->get_cpu_usage();
        $memory_usage = $this->get_memory_usage();

        return ($cpu_usage > $cpu_threshold || $memory_usage > $memory_threshold);
    }

    /**
     * 等待系统资源释放（PNG专用）
     */
    private function wait_for_png_resources($cpu_threshold, $memory_threshold, $verbose = false) {
        $wait_count = 0;
        $max_wait = 60; // 最大等待60次（约5分钟）

        while ($this->should_wait_for_resources($cpu_threshold, $memory_threshold) && $wait_count < $max_wait) {
            if ($verbose && $wait_count % 10 == 0) {
                $cpu = $this->get_cpu_usage();
                $memory = $this->get_memory_usage();
                WP_CLI::log("    ⏳ 等待资源释放... CPU: {$cpu}%, 内存: {$memory}%");
            }

            sleep(5); // 等待5秒
            $wait_count++;
        }
    }

    /**
     * 获取CPU使用率
     */
    private function get_cpu_usage() {
        $load = sys_getloadavg();
        if ($load === false) {
            return 0;
        }

        // 获取CPU核心数
        $cpu_cores = $this->get_png_cpu_cores();
        if ($cpu_cores <= 0) {
            $cpu_cores = 1;
        }

        // 计算CPU使用率百分比
        $cpu_usage = ($load[0] / $cpu_cores) * 100;
        return min(100, max(0, $cpu_usage));
    }

    /**
     * 获取内存使用率
     */
    private function get_memory_usage() {
        $memory_usage = memory_get_usage(true);
        $memory_limit = $this->get_memory_limit();

        if ($memory_limit <= 0) {
            return 0;
        }

        return ($memory_usage / $memory_limit) * 100;
    }

    /**
     * 获取CPU核心数（PNG专用）
     */
    private function get_png_cpu_cores() {
        if (function_exists('shell_exec')) {
            $cores = shell_exec('nproc');
            if ($cores !== null) {
                return intval(trim($cores));
            }
        }

        // 备用方法
        if (is_file('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            preg_match_all('/^processor/m', $cpuinfo, $matches);
            return count($matches[0]);
        }

        return 1; // 默认值
    }

    /**
     * 获取内存限制（字节）
     */
    private function get_memory_limit() {
        $memory_limit = ini_get('memory_limit');

        if ($memory_limit == -1) {
            // 无限制，使用系统内存
            if (function_exists('shell_exec')) {
                $mem_info = shell_exec('cat /proc/meminfo | grep MemTotal');
                if ($mem_info) {
                    preg_match('/MemTotal:\s+(\d+)\s+kB/', $mem_info, $matches);
                    if (isset($matches[1])) {
                        return intval($matches[1]) * 1024; // 转换为字节
                    }
                }
            }
            return 8 * 1024 * 1024 * 1024; // 默认8GB
        }

        // 解析内存限制
        $unit = strtolower(substr($memory_limit, -1));
        $value = intval($memory_limit);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return $value;
        }
    }

    /**
     * 并发处理PNG文件批次
     *
     * @param array $batch_files 批次文件列表
     * @param int $template_id 模板产品ID
     * @param int $category_id 类目ID
     * @param string $category_name 类目名称
     * @param string $folder_name 文件夹名称
     * @param SS_PNG_File_Manager $png_manager PNG文件管理器
     * @param int $max_concurrent 最大并发数
     * @param string $background_color 画廊图背景色
     * @param bool $verbose 是否显示详细信息
     * @return array 处理结果数组
     */
    private function process_png_batch_concurrent($batch_files, $template_id, $category_id, $category_name, $folder_name, $png_manager, $max_concurrent, $background_color, $verbose = false) {
        $results = array();
        $futures = array();
        $active_tasks = 0;
        $completed_tasks = 0;
        $total_tasks = count($batch_files);

        if ($verbose) {
            WP_CLI::log("    🚀 启动并发处理: {$total_tasks} 个文件，最大并发: {$max_concurrent}");
        }

        // 创建任务队列
        $task_queue = $batch_files;
        $task_index = 0;

        while ($completed_tasks < $total_tasks) {
            // 启动新任务（填满并发槽位）
            while ($active_tasks < $max_concurrent && !empty($task_queue)) {
                $file_info = array_shift($task_queue);
                $current_task_index = $task_index++;

                // 准备文件路径
                $png_filename = $file_info['filename'];
                $png_file_path = isset($file_info['file_path']) ? $file_info['file_path'] :
                    ('/www/wwwroot/printeeque.com/wp-content/uploads/png/' . $folder_name . '/' . $png_filename);

                // 验证文件存在
                if (!file_exists($png_file_path)) {
                    $results[] = array(
                        'success' => false,
                        'filename' => $png_filename,
                        'error' => '文件不存在',
                        'duration' => 0
                    );
                    $completed_tasks++;
                    continue;
                }

                try {
                    // 创建并发任务
                    $runtime = new \parallel\Runtime();
                    $future = $runtime->run(function($png_file_path, $template_id, $category_id, $category_name, $png_filename) {
                        $start_time = microtime(true);

                        try {
                            // 在并发环境中重新初始化WordPress
                            if (!defined('ABSPATH')) {
                                require_once '/www/wwwroot/printeeque.com/wp-load.php';
                            }

                            // 确保插件文件已加载
                            if (!class_exists('SS_SVG_Generator')) {
                                $plugin_files = array(
                                    '/www/wwwroot/printeeque.com/wp-content/plugins/shoe-svg-generator/shoe-svg-generator.php',
                                    '/www/wwwroot/printeeque.com/wp-content/plugins/shoe-svg-generator/class-ss-svg-generator.php'
                                );

                                foreach ($plugin_files as $plugin_file) {
                                    if (file_exists($plugin_file)) {
                                        require_once $plugin_file;
                                    }
                                }
                            }

                            // 再次检查类是否存在
                            if (class_exists('SS_SVG_Generator')) {
                                $generator = SS_SVG_Generator::get_instance();
                                $product_id = $generator->ss_create_product_from_png(
                                    $png_file_path,
                                    $template_id,
                                    $category_id,
                                    $category_name,
                                    $background_color
                                );

                                $duration = microtime(true) - $start_time;

                                return array(
                                    'success' => $product_id !== false,
                                    'product_id' => $product_id,
                                    'filename' => $png_filename,
                                    'duration' => $duration,
                                    'error' => $product_id ? null : '产品创建失败'
                                );
                            } else {
                                return array(
                                    'success' => false,
                                    'product_id' => null,
                                    'filename' => $png_filename,
                                    'duration' => microtime(true) - $start_time,
                                    'error' => 'SS_SVG_Generator类加载失败，请检查插件文件'
                                );
                            }
                        } catch (Exception $e) {
                            return array(
                                'success' => false,
                                'product_id' => null,
                                'filename' => $png_filename,
                                'duration' => microtime(true) - $start_time,
                                'error' => '并发任务异常: ' . $e->getMessage()
                            );
                        }
                    }, array($png_file_path, $template_id, $category_id, $category_name, $png_filename));

                    $futures[$current_task_index] = array(
                        'future' => $future,
                        'filename' => $png_filename,
                        'start_time' => microtime(true)
                    );

                    $active_tasks++;

                    if ($verbose) {
                        WP_CLI::log("    ⚡ 启动并发任务 #{$current_task_index}: {$png_filename} (活跃: {$active_tasks}/{$max_concurrent})");
                    }

                } catch (Exception $e) {
                    $results[] = array(
                        'success' => false,
                        'filename' => $png_filename,
                        'error' => '并发任务启动失败: ' . $e->getMessage(),
                        'duration' => 0
                    );
                    $completed_tasks++;
                }
            }

            // 检查已完成的任务
            foreach ($futures as $task_index => $task_data) {
                $future = $task_data['future'];

                if ($future->done()) {
                    try {
                        $result = $future->value();
                        $total_duration = microtime(true) - $task_data['start_time'];

                        // 如果成功，更新JSON记录（使用完整的相对路径）
                        if ($result['success'] && $result['product_id']) {
                            $png_manager->update_file_processed_status(
                                $folder_relative_path,
                                $category_id,
                                $result['filename'],
                                $template_id,
                                $result['product_id']
                            );
                        }

                        $result['total_duration'] = $total_duration;
                        $results[] = $result;

                        if ($verbose) {
                            WP_CLI::log("    ✅ 并发任务 #{$task_index} 完成: {$task_data['filename']} - " .
                                       ($result['success'] ? "成功 (ID: {$result['product_id']})" : "失败: {$result['error']}") .
                                       " - 耗时: " . round($total_duration, 2) . "s");
                        }

                    } catch (Exception $e) {
                        $results[] = array(
                            'success' => false,
                            'filename' => $task_data['filename'],
                            'error' => '并发任务执行失败: ' . $e->getMessage(),
                            'duration' => microtime(true) - $task_data['start_time']
                        );

                        if ($verbose) {
                            WP_CLI::log("    ❌ 并发任务 #{$task_index} 失败: {$task_data['filename']} - {$e->getMessage()}");
                        }
                    }

                    unset($futures[$task_index]);
                    $active_tasks--;
                    $completed_tasks++;
                }
            }

            // 短暂休息避免CPU占用过高
            if ($active_tasks > 0) {
                usleep(100000); // 0.1秒
            }
        }

        if ($verbose) {
            WP_CLI::log("    🎉 并发批次处理完成: {$completed_tasks}/{$total_tasks} 个任务");
        }

        return $results;
    }

    /**
     * 获取所有PNG产品的模板ID
     */
    private function get_all_png_template_ids() {
        global $wpdb;

        error_log('[PNG清理] 开始获取所有PNG产品的模板ID');

        // 查询所有包含模板ID元数据的产品（PNG产品使用_ss_template_product_id）
        $template_ids = $wpdb->get_col("
            SELECT DISTINCT meta_value
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_ss_template_product_id'
            AND p.post_type = 'product'
            AND p.post_status = 'publish'
            AND meta_value != ''
            AND meta_value != '0'
        ");

        $template_ids = array_map('intval', array_filter($template_ids));

        error_log('[PNG清理] 找到模板ID: ' . implode(', ', $template_ids));

        return $template_ids;
    }

    /**
     * 获取PNG清理统计信息
     */
    private function get_png_cleanup_stats($template_ids) {
        global $wpdb;

        error_log('[PNG清理] 开始获取清理统计信息，模板ID: ' . implode(', ', $template_ids));

        $stats = array(
            'total_products' => 0,
            'total_images' => 0,
            'categories_count' => 0,
            'json_files_count' => 0
        );

        if (empty($template_ids)) {
            return $stats;
        }

        $template_ids_str = implode(',', $template_ids);

        // 获取产品数量
        $products_query = "
            SELECT COUNT(DISTINCT p.ID) as product_count,
                   COUNT(DISTINCT pm_image.meta_value) as image_count,
                   COUNT(DISTINCT pm_gallery.meta_value) as gallery_count
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm_template ON p.ID = pm_template.post_id
            LEFT JOIN {$wpdb->postmeta} pm_image ON p.ID = pm_image.post_id AND pm_image.meta_key = '_thumbnail_id'
            LEFT JOIN {$wpdb->postmeta} pm_gallery ON p.ID = pm_gallery.post_id AND pm_gallery.meta_key = '_product_image_gallery'
            WHERE pm_template.meta_key = '_ss_template_product_id'
            AND pm_template.meta_value IN ({$template_ids_str})
            AND p.post_type = 'product'
            AND p.post_status = 'publish'
        ";

        $product_stats = $wpdb->get_row($products_query);

        if ($product_stats) {
            $stats['total_products'] = intval($product_stats->product_count);
            $stats['total_images'] = intval($product_stats->image_count) + intval($product_stats->gallery_count);
        }

        // 获取涉及的类目数量
        $categories_query = "
            SELECT COUNT(DISTINCT tr.term_taxonomy_id) as category_count
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            INNER JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
            INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
            WHERE pm.meta_key = '_ss_template_product_id'
            AND pm.meta_value IN ({$template_ids_str})
            AND p.post_type = 'product'
            AND p.post_status = 'publish'
            AND tt.taxonomy = 'product_cat'
        ";

        $category_stats = $wpdb->get_var($categories_query);
        $stats['categories_count'] = intval($category_stats);

        // 获取JSON记录文件数量
        $upload_dir = wp_upload_dir();
        $json_records_dir = $upload_dir['basedir'] . '/shoe-svg-generator/png-records/';

        if (is_dir($json_records_dir)) {
            $json_files = glob($json_records_dir . '*.json');
            $stats['json_files_count'] = count($json_files);
        }

        error_log('[PNG清理] 统计结果: ' . json_encode($stats));

        return $stats;
    }

    /**
     * 执行PNG产品清理
     */
    private function execute_png_cleanup($template_ids, $dry_run, $max_parallel, $batch_size, $include_json_records) {
        error_log('[PNG清理] 开始执行清理，模板ID: ' . implode(', ', $template_ids));

        $total_stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0,
            'json_files_deleted' => 0,
            'categories_processed' => 0
        );

        // 获取所有要清理的产品
        $products_to_clean = $this->get_png_products_by_templates($template_ids);

        if (empty($products_to_clean)) {
            WP_CLI::log('没有找到要清理的PNG产品');
            return $total_stats;
        }

        WP_CLI::log('开始清理 ' . count($products_to_clean) . ' 个PNG产品...');

        // 获取受保护的图片ID（模板产品的图片）
        $protected_image_ids = $this->get_template_product_image_ids($template_ids);

        // 分批处理
        $batches = array_chunk($products_to_clean, $batch_size);
        $progress = \WP_CLI\Utils\make_progress_bar('清理PNG产品', count($products_to_clean));

        foreach ($batches as $batch_index => $batch) {
            if ($this->verbose) {
                WP_CLI::log("处理批次 " . ($batch_index + 1) . "/" . count($batches) . " (" . count($batch) . " 个产品)");
            }

            // 并行处理批次
            if ($max_parallel > 1) {
                $batch_stats = $this->process_png_cleanup_batch_parallel($batch, $protected_image_ids, $dry_run, $max_parallel);
            } else {
                $batch_stats = $this->process_png_cleanup_batch_sequential($batch, $protected_image_ids, $dry_run);
            }

            // 累计统计
            $total_stats['products_deleted'] += $batch_stats['products_deleted'];
            $total_stats['images_deleted'] += $batch_stats['images_deleted'];
            $total_stats['files_deleted'] += $batch_stats['files_deleted'];

            $progress->tick(count($batch));
        }

        $progress->finish();

        // 清理JSON记录文件
        if ($include_json_records) {
            $json_stats = $this->cleanup_png_json_records($template_ids, $dry_run);
            $total_stats['json_files_deleted'] = $json_stats['files_deleted'];
        }

        // 显示清理结果
        WP_CLI::log('');
        WP_CLI::log('📊 清理结果统计:');
        WP_CLI::log('  删除产品: ' . $total_stats['products_deleted'] . ' 个');
        WP_CLI::log('  删除图片: ' . $total_stats['images_deleted'] . ' 个');
        WP_CLI::log('  删除文件: ' . $total_stats['files_deleted'] . ' 个');
        if ($include_json_records) {
            WP_CLI::log('  删除JSON记录: ' . $total_stats['json_files_deleted'] . ' 个');
        }

        return $total_stats;
    }

    /**
     * 根据模板ID获取PNG产品
     */
    private function get_png_products_by_templates($template_ids) {
        global $wpdb;

        if (empty($template_ids)) {
            return array();
        }

        $template_ids_str = implode(',', $template_ids);

        $products = $wpdb->get_results("
            SELECT p.ID, p.post_title, pm.meta_value as template_id
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE pm.meta_key = '_ss_template_product_id'
            AND pm.meta_value IN ({$template_ids_str})
            AND p.post_type = 'product'
            AND p.post_status = 'publish'
            ORDER BY p.ID
        ");

        error_log('[PNG清理] 找到 ' . count($products) . ' 个PNG产品');

        return $products;
    }

    /**
     * 获取模板产品的受保护图片ID
     */
    private function get_template_product_image_ids($template_ids) {
        $protected_image_ids = array();

        foreach ($template_ids as $template_id) {
            $template_product = wc_get_product($template_id);
            if (!$template_product) {
                continue;
            }

            // 主图
            $image_id = $template_product->get_image_id();
            if ($image_id) {
                $protected_image_ids[] = $image_id;
            }

            // 画廊图片
            $gallery_ids = $template_product->get_gallery_image_ids();
            $protected_image_ids = array_merge($protected_image_ids, $gallery_ids);
        }

        $protected_image_ids = array_unique($protected_image_ids);

        error_log('[PNG清理] 受保护的图片ID: ' . implode(', ', $protected_image_ids));

        return $protected_image_ids;
    }

    /**
     * 顺序处理PNG清理批次
     */
    private function process_png_cleanup_batch_sequential($products, $protected_image_ids, $dry_run) {
        $batch_stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0
        );

        foreach ($products as $product) {
            $product_stats = $this->delete_png_product($product->ID, $protected_image_ids, $dry_run);

            $batch_stats['products_deleted'] += $product_stats['products_deleted'];
            $batch_stats['images_deleted'] += $product_stats['images_deleted'];
            $batch_stats['files_deleted'] += $product_stats['files_deleted'];

            if ($this->verbose) {
                $status = $dry_run ? '预览' : '删除';
                WP_CLI::log("  {$status}产品: {$product->post_title} (ID: {$product->ID})");
            }
        }

        return $batch_stats;
    }

    /**
     * 并行处理PNG清理批次
     */
    private function process_png_cleanup_batch_parallel($products, $protected_image_ids, $dry_run, $max_parallel) {
        $batch_stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0
        );

        if (empty($products)) {
            return $batch_stats;
        }

        // 优化子批次分配，支持高并发处理
        $products_per_process = max(1, intval(count($products) / $max_parallel));
        $sub_batches = array_chunk($products, $products_per_process);

        // 如果子批次数量少于max_parallel，调整分配策略
        if (count($sub_batches) < $max_parallel && count($products) > $max_parallel) {
            $sub_batches = array_chunk($products, max(1, intval(count($products) / $max_parallel)));
        }

        $processes = array();
        $temp_files = array();

        error_log('[PNG清理] 高并发处理 ' . count($products) . ' 个产品，分成 ' . count($sub_batches) . ' 个子批次，最大并行: ' . $max_parallel);

        // 创建临时目录
        $temp_dir = sys_get_temp_dir() . '/png_cleanup_' . time() . '/';
        if (!is_dir($temp_dir)) {
            mkdir($temp_dir, 0755, true);
        }

        // 启动并行进程
        foreach ($sub_batches as $batch_index => $sub_batch) {
            $task_data = array(
                'products' => $sub_batch,
                'protected_image_ids' => $protected_image_ids,
                'dry_run' => $dry_run,
                'batch_index' => $batch_index,
                'verbose' => $this->verbose
            );

            // 创建任务文件
            $task_file = $temp_dir . 'task_' . $batch_index . '.json';
            file_put_contents($task_file, json_encode($task_data));
            $temp_files[] = $task_file;

            // 创建结果文件路径
            $result_file = $temp_dir . 'result_' . $batch_index . '.json';

            // 构建PHP命令
            $php_script = $this->create_png_cleanup_worker_script($temp_dir);
            $cmd = "php " . escapeshellarg($php_script) . " " . escapeshellarg($task_file) . " " . escapeshellarg($result_file) . " 2>&1";

            // 启动异步进程
            $descriptors = array(
                0 => array("pipe", "r"),
                1 => array("pipe", "w"),
                2 => array("pipe", "w")
            );

            $process = proc_open($cmd, $descriptors, $pipes);
            if (is_resource($process)) {
                $processes[$batch_index] = array(
                    'process' => $process,
                    'pipes' => $pipes,
                    'result_file' => $result_file,
                    'start_time' => microtime(true)
                );

                if ($this->verbose) {
                    WP_CLI::log("  启动并行清理进程 #{$batch_index} (" . count($sub_batch) . " 个产品)");
                }
            } else {
                error_log('[PNG清理] 无法启动进程 #' . $batch_index);
                // 回退到顺序处理
                $sub_stats = $this->process_png_cleanup_batch_sequential($sub_batch, $protected_image_ids, $dry_run);
                $batch_stats['products_deleted'] += $sub_stats['products_deleted'];
                $batch_stats['images_deleted'] += $sub_stats['images_deleted'];
                $batch_stats['files_deleted'] += $sub_stats['files_deleted'];
            }
        }

        // 优化进程管理，支持高并发处理
        $completed = 0;
        $total_processes = count($processes);
        $last_progress_update = time();

        if ($this->verbose) {
            WP_CLI::log("  🚀 启动了 {$total_processes} 个并行进程，开始监控完成状态...");
        }

        while ($completed < $total_processes) {
            $active_processes = 0;
            $completed_in_cycle = 0;

            foreach ($processes as $batch_index => $process_info) {
                $status = proc_get_status($process_info['process']);

                if (!$status['running']) {
                    // 进程已完成
                    $duration = microtime(true) - $process_info['start_time'];

                    // 关闭管道
                    foreach ($process_info['pipes'] as $pipe) {
                        if (is_resource($pipe)) {
                            fclose($pipe);
                        }
                    }
                    proc_close($process_info['process']);

                    // 读取结果
                    if (file_exists($process_info['result_file'])) {
                        $result_data = json_decode(file_get_contents($process_info['result_file']), true);
                        if ($result_data && isset($result_data['success']) && $result_data['success']) {
                            $batch_stats['products_deleted'] += $result_data['products_deleted'];
                            $batch_stats['images_deleted'] += $result_data['images_deleted'];
                            $batch_stats['files_deleted'] += $result_data['files_deleted'];

                            if ($this->verbose) {
                                WP_CLI::log("  ✅ 进程 #{$batch_index} 完成 - 删除产品: {$result_data['products_deleted']}, 删除图片: {$result_data['images_deleted']}, 耗时: " . round($duration, 2) . "s");
                            }
                        } else {
                            $error_msg = isset($result_data['error']) ? $result_data['error'] : '未知错误';
                            error_log('[PNG清理] 进程 #' . $batch_index . ' 执行失败: ' . $error_msg);
                            if ($this->verbose) {
                                WP_CLI::log("  ❌ 进程 #{$batch_index} 失败: {$error_msg}");
                            }
                        }
                        unlink($process_info['result_file']);
                    } else {
                        error_log('[PNG清理] 进程 #' . $batch_index . ' 结果文件不存在');
                    }

                    unset($processes[$batch_index]);
                    $completed++;
                    $completed_in_cycle++;
                } else {
                    $active_processes++;
                }
            }

            // 定期显示进度（每5秒）
            $current_time = time();
            if ($this->verbose && ($current_time - $last_progress_update >= 5 || $completed_in_cycle > 0)) {
                $progress_percent = round(($completed / $total_processes) * 100, 1);
                WP_CLI::log("  📊 进度: {$completed}/{$total_processes} ({$progress_percent}%) - 活跃进程: {$active_processes}");
                $last_progress_update = $current_time;
            }

            if (!empty($processes)) {
                // 高并发时减少轮询间隔
                usleep($max_parallel > 50 ? 50000 : 100000); // 0.05秒或0.1秒
            }
        }

        // 清理临时文件
        foreach ($temp_files as $temp_file) {
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }
        }

        // 清理临时目录
        if (is_dir($temp_dir)) {
            rmdir($temp_dir);
        }

        return $batch_stats;
    }

    /**
     * 删除单个PNG产品
     */
    private function delete_png_product($product_id, $protected_image_ids, $dry_run = false) {
        $stats = array(
            'products_deleted' => 0,
            'images_deleted' => 0,
            'files_deleted' => 0
        );

        $product = wc_get_product($product_id);
        if (!$product) {
            return $stats;
        }

        // 收集产品的图片
        $product_image_ids = array();

        // 主图
        $image_id = $product->get_image_id();
        if ($image_id && !in_array($image_id, $protected_image_ids)) {
            $product_image_ids[] = $image_id;
        }

        // 画廊图片
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ($gallery_ids as $gallery_id) {
            if (!in_array($gallery_id, $protected_image_ids)) {
                $product_image_ids[] = $gallery_id;
            }
        }

        if ($dry_run) {
            $stats['products_deleted'] = 1;
            $stats['images_deleted'] = count($product_image_ids);
            return $stats;
        }

        // 删除图片
        foreach ($product_image_ids as $image_id) {
            if (wp_delete_attachment($image_id, true)) {
                $stats['images_deleted']++;
                error_log('[PNG清理] 删除图片: ' . $image_id);
            }
        }

        // 删除产品
        if (wp_delete_post($product_id, true)) {
            $stats['products_deleted'] = 1;
            error_log('[PNG清理] 删除产品: ' . $product_id);
        }

        return $stats;
    }

    /**
     * 清理PNG JSON记录文件
     */
    private function cleanup_png_json_records($template_ids, $dry_run) {
        $stats = array('files_deleted' => 0);

        $upload_dir = wp_upload_dir();
        $json_records_dir = $upload_dir['basedir'] . '/shoe-svg-generator/png-records/';

        if (!is_dir($json_records_dir)) {
            return $stats;
        }

        // 获取所有JSON文件
        $json_files = glob($json_records_dir . '*.json');

        foreach ($json_files as $json_file) {
            // 读取JSON文件内容，检查是否与指定模板相关
            $json_content = file_get_contents($json_file);
            $json_data = json_decode($json_content, true);

            if (!$json_data || !isset($json_data['files'])) {
                continue;
            }

            // 检查文件中是否包含指定模板的产品
            // 注意：templates数组的键就是template_id
            $should_delete = false;
            foreach ($json_data['files'] as $file_info) {
                if (isset($file_info['templates'])) {
                    foreach ($file_info['templates'] as $template_id_key => $template_info) {
                        if (in_array($template_id_key, $template_ids)) {
                            $should_delete = true;
                            break 2;
                        }
                    }
                }
            }

            if ($should_delete) {
                if (!$dry_run) {
                    if (unlink($json_file)) {
                        $stats['files_deleted']++;
                        error_log('[PNG清理] 删除JSON记录: ' . basename($json_file));
                    }
                } else {
                    $stats['files_deleted']++;
                    if ($this->verbose) {
                        WP_CLI::log("  预览删除JSON记录: " . basename($json_file));
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * 创建PNG清理工作脚本
     */
    private function create_png_cleanup_worker_script($temp_dir) {
        $script_path = $temp_dir . 'png_cleanup_worker.php';

        $script_content = '<?php
// PNG产品清理工作脚本
if ($argc < 3) {
    exit(1);
}

$task_file = $argv[1];
$result_file = $argv[2];

// 加载WordPress
require_once "/www/wwwroot/printeeque.com/wp-load.php";

try {
    // 读取任务数据
    $task_data = json_decode(file_get_contents($task_file), true);
    if (!$task_data) {
        throw new Exception("无法读取任务数据");
    }

    $products = $task_data["products"];
    $protected_image_ids = $task_data["protected_image_ids"];
    $dry_run = $task_data["dry_run"];
    $batch_index = $task_data["batch_index"];
    $verbose = $task_data["verbose"];

    $stats = array(
        "products_deleted" => 0,
        "images_deleted" => 0,
        "files_deleted" => 0
    );

    // 处理每个产品
    foreach ($products as $product_data) {
        // 兼容对象和数组格式
        $product_id = null;
        if (is_object($product_data)) {
            $product_id = isset($product_data->ID) ? $product_data->ID : null;
        } elseif (is_array($product_data)) {
            $product_id = isset($product_data["ID"]) ? $product_data["ID"] : null;
        }

        if (!$product_id) {
            error_log("[PNG清理工作脚本] 无效的产品数据: " . json_encode($product_data));
            continue;
        }

        $product = wc_get_product($product_id);

        if (!$product) {
            error_log("[PNG清理工作脚本] 产品不存在: " . $product_id);
            continue;
        }

        // 收集产品的图片
        $product_image_ids = array();

        // 主图
        $image_id = $product->get_image_id();
        if ($image_id && !in_array($image_id, $protected_image_ids)) {
            $product_image_ids[] = $image_id;
        }

        // 画廊图片
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ($gallery_ids as $gallery_id) {
            if (!in_array($gallery_id, $protected_image_ids)) {
                $product_image_ids[] = $gallery_id;
            }
        }

        if ($dry_run) {
            $stats["products_deleted"]++;
            $stats["images_deleted"] += count($product_image_ids);
        } else {
            // 删除图片
            foreach ($product_image_ids as $image_id) {
                if (wp_delete_attachment($image_id, true)) {
                    $stats["images_deleted"]++;
                }
            }

            // 删除产品
            if (wp_delete_post($product_id, true)) {
                $stats["products_deleted"]++;
            }
        }
    }

    // 保存结果
    $result = array(
        "success" => true,
        "products_deleted" => $stats["products_deleted"],
        "images_deleted" => $stats["images_deleted"],
        "files_deleted" => $stats["files_deleted"],
        "batch_index" => $batch_index
    );

    file_put_contents($result_file, json_encode($result));

} catch (Exception $e) {
    // 保存错误结果
    $error_result = array(
        "success" => false,
        "error" => $e->getMessage(),
        "batch_index" => isset($batch_index) ? $batch_index : -1
    );

    file_put_contents($result_file, json_encode($error_result));
}
?>';

        file_put_contents($script_path, $script_content);
        return $script_path;
    }
}

// 注册WP-CLI命令
if (class_exists('WP_CLI')) {
    WP_CLI::add_command('ss', 'SS_CLI_Commands');

    // 添加命令别名，支持连字符版本
    WP_CLI::add_hook('before_invoke:ss', function() {
        // 将连字符版本的命令映射到下划线版本
        if (isset($_SERVER['argv'])) {
            foreach ($_SERVER['argv'] as $i => $arg) {
                if ($arg === 'generate-products') {
                    $_SERVER['argv'][$i] = 'generate_products';
                } else if ($arg === 'clear-category-flags') {
                    $_SERVER['argv'][$i] = 'clear_category_flags';
                } else if ($arg === 'cleanup-empty-dirs') {
                    $_SERVER['argv'][$i] = 'cleanup_empty_dirs';
                } else if ($arg === 'generate-svg-records') {
                    $_SERVER['argv'][$i] = 'generate_svg_records';
                } else if ($arg === 'migrate-svg-to-oss') {
                    $_SERVER['argv'][$i] = 'migrate_svg_to_oss';
                } else if ($arg === 'cleanup-migrated-svg') {
                    $_SERVER['argv'][$i] = 'cleanup_migrated_svg';
                } else if ($arg === 'sync-svg-to-oss-rclone') {
                    $_SERVER['argv'][$i] = 'sync_svg_to_oss_rclone';
                } else if ($arg === 'convert-and-sync-optimized' || $arg === 'convert_and_sync_optimized') {
                    $_SERVER['argv'][$i] = 'convert_and_sync_optimized';
                } else if ($arg === 'cleanup-local-filf' || $arg === 'cleanup_local_filf') {
                    $_SERVER['argv'][$i] = 'cleanup_local_filf';
                } else if ($arg === 'check-oss-filf' || $arg === 'check_oss_filf') {
                    $_SERVER['argv'][$i] = 'check_oss_filf';
                } else if ($arg === 'clean-custom-categories') {
                    $_SERVER['argv'][$i] = 'clean_custom_categories';
                } else if ($arg === 'generate-and-sync-complete') {
                    $_SERVER['argv'][$i] = 'generate_and_sync_complete';
                } else if ($arg === 'generate-frontend-previews') {
                    $_SERVER['argv'][$i] = 'generate_frontend_previews';
                } else if ($arg === 'fix-permissions') {
                    $_SERVER['argv'][$i] = 'fix_permissions';
                } else if ($arg === 'png-multi-template') {
                    $_SERVER['argv'][$i] = 'png_multi_template';
                } else if ($arg === 'clean-png-products') {
                    $_SERVER['argv'][$i] = 'clean_png_products';
                }
            }
        }
    });
}

    /**
     * 【修复】检查指定类目是否有已生成的产品
     * 用于保护已生成产品的预览图不被误删
     *
     * @param string $category_name 类目名称（安全格式）
     * @return bool 是否有已生成产品
     */
    private function check_category_has_generated_products($category_name) {
        global $wpdb;

        try {
            // 根据类目名称查找类目ID
            $term = get_term_by('slug', $category_name, 'product_cat');
            if (!$term) {
                // 尝试按名称查找
                $term = get_term_by('name', $category_name, 'product_cat');
            }

            if (!$term) {
                error_log("[预览保护][修复] 未找到类目: {$category_name}");
                return false;
            }

            // 查询该类目下的已生成产品（非模板产品）
            $generated_products_count = $wpdb->get_var($wpdb->prepare("
                SELECT COUNT(DISTINCT p.ID)
                FROM {$wpdb->posts} p
                INNER JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                LEFT JOIN {$wpdb->postmeta} pm_template ON p.ID = pm_template.post_id AND pm_template.meta_key = '_ss_is_template'
                LEFT JOIN {$wpdb->postmeta} pm_temp ON p.ID = pm_temp.post_id AND pm_temp.meta_key = '_ss_is_temp_product'
                WHERE p.post_type = 'product'
                AND p.post_status = 'publish'
                AND tt.term_id = %d
                AND tt.taxonomy = 'product_cat'
                AND (pm_template.meta_value IS NULL OR pm_template.meta_value != '1')
                AND (pm_temp.meta_value IS NULL OR pm_temp.meta_value != '1')
            ", $term->term_id));

            $has_products = $generated_products_count > 0;

            error_log("[预览保护][修复] 类目 '{$category_name}' (ID: {$term->term_id}) 有 {$generated_products_count} 个已生成产品");

            return $has_products;

        } catch (Exception $e) {
            error_log("[预览保护][修复] 检查类目产品时出错: " . $e->getMessage());
            return true; // 出错时保守处理，保护文件
        }
    }
}
