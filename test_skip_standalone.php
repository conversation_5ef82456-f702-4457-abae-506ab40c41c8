<?php
/**
 * 独立测试PNG跳过功能
 * 
 * 不需要WordPress环境即可运行的测试脚本
 * 专门测试修复后的PNG跳过逻辑
 */

// 定义基本常量
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

// 模拟WordPress核心函数
define('WP_DEBUG', true);

// 创建便携式测试类，不需要WordPress
class Portable_PNG_File_Manager {
    
    private $png_root_dir;
    private $json_records_dir;
    
    public function __construct($test_dir = null) {
        // 设置测试目录
        if ($test_dir) {
            $this->png_root_dir = $test_dir . '/png/';
            $this->json_records_dir = $test_dir . '/json-records/';
        } else {
            $this->png_root_dir = sys_get_temp_dir() . '/test_png/';
            $this->json_records_dir = sys_get_temp_dir() . '/test_json/';
        }
        
        // 确保目录存在
        if (!is_dir($this->png_root_dir)) {
            mkdir($this->png_root_dir, 0755, true);
        }
        if (!is_dir($this->json_records_dir)) {
            mkdir($this->json_records_dir, 0755, true);
        }
    }
    
    public function simulate_generate_json_record($folder_name, $files_data) {
        $json_data = array(
            'folder_name' => $folder_name,
            'folder_path' => $folder_name,
            'category_id' => 123,
            'category_name' => '测试类目',
            'total_files' => count($files_data),
            'generated_time' => date('Y-m-d H:i:s'),
            'last_updated' => date('Y-m-d H:i:s'),
            'files' => $files_data
        );
        
        $json_filename = "png_folder_{$folder_name}_category_123.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        file_put_contents($json_filepath, json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        return $json_filepath;
    }
    
    public function get_json_record($folder_name) {
        $json_filename = "png_folder_{$folder_name}_category_123.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        if (file_exists($json_filepath)) {
            return json_decode(file_get_contents($json_filepath), true);
        }
        return false;
    }
    
    public function get_unprocessed_files($folder_name, $template_id = null) {
        $json_record = $this->get_json_record($folder_name);
        
        if (!$json_record) {
            return array();
        }
        
        $unprocessed = array();
        
        foreach ($json_record['files'] as $file_info) {
            // 检查文件是否存在
            $full_path = $this->png_root_dir . $folder_name . '/' . $file_info['filename'];
            if (!file_exists($full_path)) {
                continue; // 文件不存在，跳过
            }
            
            // 检查是否已处理
            $is_processed = false;
            if ($template_id !== null) {
                $is_processed = isset($file_info['templates']) && isset($file_info['templates'][$template_id]);
            } else {
                $is_processed = isset($file_info['templates']) && !empty($file_info['templates']);
            }
            
            if (!$is_processed) {
                $unprocessed[] = $file_info;
            }
        }
        
        return $unprocessed;
    }
    
    public function update_file_status($folder_name, $filename, $template_id, $product_id) {
        $json_record = $this->get_json_record($folder_name);
        
        if (!$json_record) {
            return false;
        }
        
        foreach ($json_record['files'] as &$file_info) {
            if ($file_info['filename'] === $filename) {
                if (!isset($file_info['templates'])) {
                    $file_info['templates'] = array();
                }
                
                $file_info['templates'][$template_id] = array(
                    'product_id' => $product_id,
                    'processed_time' => date('Y-m-d H:i:s'),
                    'template_name' => '测试模板'
                );
                
                $json_filename = "png_folder_{$folder_name}_category_123.json";
                $json_filepath = $this->json_records_dir . $json_filename;
                file_put_contents($json_filepath, json_encode($json_record, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                
                return true;
            }
        }
        
        return false;
    }
    
    public function cleanup() {
        // 清理测试文件
        $files = glob($this->json_records_dir . '*.json');
        foreach ($files as $file) {
            unlink($file);
        }
        
        // 清理PNG测试文件夹
        $this->removeDirectory($this->png_root_dir);
        $this->removeDirectory($this->json_records_dir);
    }
    
    private function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}

function run_standalone_tests() {
    echo "=== 独立PNG跳过功能测试 ===\n\n";
    
    // 创建便携式测试实例
    $test_dir = sys_get_temp_dir() . '/png_test_' . time();
    $png_manager = new Portable_PNG_File_Manager($test_dir);
    
    // 测试1：基本功能测试
    echo "1. 测试基本功能\n";
    
    // 创建测试文件夹和文件
    $test_folder = 'test_basic';
    $folder_path = $test_dir . '/png/' . $test_folder;
    mkdir($folder_path, 0755, true);
    
    // 创建测试PNG文件
    file_put_contents($folder_path . '/file1.png', 'test content 1');
    file_put_contents($folder_path . '/file2.png', 'test content 2');
    
    $files_data = array(
        array(
            'filename' => 'file1.png',
            'file_path' => $folder_path . '/file1.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array()
        ),
        array(
            'filename' => 'file2.png',
            'file_path' => $folder_path . '/file2.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array()
        )
    );
    
    $png_manager->simulate_generate_json_record($test_folder, $files_data);
    
    $unprocessed = $png_manager->get_unprocessed_files($test_folder);
    echo "   首次检查未处理文件数: " . count($unprocessed) . " ✅\n";
    
    // 标记一个文件为已处理
    $png_manager->update_file_status($test_folder, 'file1.png', 123, 999);
    
    $unprocessed_after = $png_manager->get_unprocessed_files($test_folder);
    echo "   处理后未处理文件数: " . count($unprocessed_after) . " ✅\n";
    
    // 测试2：缺失文件处理
    echo "\n2. 测试缺失文件处理\n";
    
    // 删除一个源文件
    unlink($folder_path . '/file1.png');
    
    $unprocessed_missing = $png_manager->get_unprocessed_files($test_folder);
    echo "   文件缺失后未处理文件数: " . count($unprocessed_missing) . " ✅\n";
    
    // 测试3：已处理文件夹行为
    echo "\n3. 测试文件夹处理完成行为\n";
    
    $test_folder_complete = 'test_complete';
    $folder_path_complete = $test_dir . '/png/' . $test_folder_complete;
    mkdir($folder_path_complete, 0755, true);
    
    // 创建并处理所有文件
    file_put_contents($folder_path_complete . '/complete1.png', 'complete test');
    file_put_contents($folder_path_complete . '/complete2.png', 'complete test');
    
    $files_complete = array(
        array(
            'filename' => 'complete1.png',
            'file_path' => $folder_path_complete . '/complete1.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array(123 => array('product_id' => 1001, 'processed_time' => date('Y-m-d H:i:s')))
        ),
        array(
            'filename' => 'complete2.png',
            'file_path' => $folder_path_complete . '/complete2.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array(123 => array('product_id' => 1002, 'processed_time' => date('Y-m-d H:i:s')))
        )
    );
    
    $png_manager->simulate_generate_json_record($test_folder_complete, $files_complete);
    
    $unprocessed_complete = $png_manager->get_unprocessed_files($test_folder_complete, 123);
    echo "   完全处理文件夹的未处理文件数: " . count($unprocessed_complete) . " ✅\n";
    
    // 测试4：不同模板的处理
    echo "\n4. 测试不同模板的处理\n";
    
    $new_template_unprocessed = $png_manager->get_unprocessed_files($test_folder_complete, 456);
    echo "   新模板下的未处理文件数: " . count($new_template_unprocessed) . " ✅\n";
    
    // 测试5：文件夹有部分新文件
    echo "\n5. 测试文件夹有新文件的情况\n";
    
    $test_folder_partial = 'test_partial';
    $folder_path_partial = $test_dir . '/png/' . $test_folder_partial;
    mkdir($folder_path_partial, 0755, true);
    
    // 创建文件
    file_put_contents($folder_path_partial . '/existing.png', 'existing file');
    file_put_contents($folder_path_partial . '/new.png', 'new file');
    
    $files_partial = array(
        array(
            'filename' => 'existing.png',
            'file_path' => $folder_path_partial . '/existing.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array(123 => array('product_id' => 1001, 'processed_time' => date('Y-m-d H:i:s')))
        ),
        array(
            'filename' => 'new.png',
            'file_path' => $folder_path_partial . '/new.png',
            'file_size' => 1024,
            'created_time' => time(),
            'templates' => array()
        )
    );
    
    $png_manager->simulate_generate_json_record($test_folder_partial, $files_partial);
    
    $unprocessed_partial = $png_manager->get_unprocessed_files($test_folder_partial, 123);
    echo "   部分处理文件夹的未处理文件数: " . count($unprocessed_partial) . " ✅\n";
    
    // 清理
    $png_manager->cleanup();
    
    echo "\n=== 所有测试完成 ✅ ===\n";
}

// 运行测试
run_standalone_tests();
?>