<?php
/**
 * Template Display Manager Class
 *
 * 管理产品模板的展示设置
 */

if (!defined('ABSPATH')) {
    exit;
}

class SS_Template_Display_Manager {
    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
            // error_log('[SS_Template_Display_Manager] New instance created');
        }
        return self::$instance;
    }

    public function __construct() {
        // error_log('[SS_Template_Display_Manager] Constructor called');

        // 添加保存模板展示设置的AJAX处理
        add_action('wp_ajax_ss_save_template_display', array($this, 'save_template_display'));

        // 添加媒体上传所需的脚本
        add_action('admin_enqueue_scripts', array($this, 'enqueue_media_scripts'));

        // error_log('[SS_Template_Display_Manager] Actions registered');
    }

    /**
     * 渲染模板展示设置页面
     */
    public function render_template_display_page() {
        // 检查权限
        if (!current_user_can('manage_options')) {
            wp_die(__('您没有足够的权限访问此页面。', 'shoe-svg-generator'));
        }

        // 获取所有模板产品
        $template_products = $this->get_template_products();
        ?>
        <div class="wrap">
            <h1><?php _e('自定义模板展示', 'shoe-svg-generator'); ?></h1>

            <style>
            .ss-template-display-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }
            .ss-template-display-item {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                background: #fff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .ss-template-info h3 {
                margin: 0 0 10px 0;
                color: #333;
                font-size: 16px;
            }
            .ss-template-info .description {
                margin: 0 0 20px 0;
                color: #666;
                font-style: italic;
            }
            .ss-template-display-settings > div {
                margin-bottom: 15px;
            }
            .ss-template-display-settings label {
                display: block;
                font-weight: 600;
                margin-bottom: 5px;
                color: #333;
            }
            .image-preview-wrapper {
                margin: 10px 0;
                min-height: 100px;
                border: 2px dashed #ddd;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f9f9f9;
            }
            .image-preview {
                max-width: 100%;
                max-height: 150px;
                border-radius: 4px;
            }
            .ss-template-display-priority input {
                width: 80px;
            }
            .ss-template-display-priority .description {
                margin-top: 5px;
                font-size: 12px;
                color: #666;
                line-height: 1.4;
            }
            .ss-template-actions {
                border-top: 1px solid #eee;
                padding-top: 15px;
                margin-top: 20px;
            }
            .save-status .notice {
                margin: 10px 0 0 0;
                padding: 8px 12px;
            }
            </style>

            <?php if (empty($template_products)): ?>
                <div class="notice notice-warning">
                    <p><?php _e('未找到任何产品模板。请先创建产品模板。', 'shoe-svg-generator'); ?></p>
                </div>
            <?php else: ?>
                <div class="ss-template-display-grid">
                    <?php foreach ($template_products as $product):
                        $display_image_id = get_post_meta($product->ID, '_ss_template_display_image', true);
                        $display_title = get_post_meta($product->ID, '_ss_template_display_title', true);
                        $display_image_url = $display_image_id ? wp_get_attachment_image_url($display_image_id, 'medium') : '';
                    ?>
                        <div class="ss-template-display-item" data-product-id="<?php echo esc_attr($product->ID); ?>">
                            <div class="ss-template-info">
                                <h3><?php echo esc_html($product->post_title); ?></h3>
                                <p class="description"><?php _e('原始模板标题（仅用于生成产品）', 'shoe-svg-generator'); ?></p>
                            </div>

                            <div class="ss-template-display-settings">
                                <div class="ss-template-display-image">
                                    <label><?php _e('展示图片', 'shoe-svg-generator'); ?></label>
                                    <div class="image-preview-wrapper">
                                        <?php if ($display_image_url): ?>
                                            <img src="<?php echo esc_url($display_image_url); ?>" class="image-preview">
                                        <?php endif; ?>
                                    </div>
                                    <input type="hidden" name="template_display_image" value="<?php echo esc_attr($display_image_id); ?>" class="image-id">
                                    <button type="button" class="button upload-image-button">
                                        <?php _e('选择图片', 'shoe-svg-generator'); ?>
                                    </button>
                                    <button type="button" class="button remove-image-button" <?php echo !$display_image_url ? 'style="display:none;"' : ''; ?>>
                                        <?php _e('移除图片', 'shoe-svg-generator'); ?>
                                    </button>
                                </div>

                                <div class="ss-template-display-title">
                                    <label><?php _e('展示标题', 'shoe-svg-generator'); ?></label>
                                    <input type="text" name="template_display_title"
                                           value="<?php echo esc_attr($display_title); ?>"
                                           class="regular-text"
                                           placeholder="<?php _e('输入在模板选择弹窗中显示的标题', 'shoe-svg-generator'); ?>">
                                </div>

                                <div class="ss-template-display-priority">
                                    <?php
                                    $display_priority = get_post_meta($product->ID, '_ss_template_display_priority', true);
                                    $display_priority = $display_priority ? intval($display_priority) : 0;
                                    ?>
                                    <label><?php _e('显示优先级', 'shoe-svg-generator'); ?></label>
                                    <input type="number" name="template_display_priority"
                                           value="<?php echo esc_attr($display_priority); ?>"
                                           class="small-text"
                                           min="0" max="999"
                                           placeholder="0">
                                    <p class="description">
                                        <?php _e('数值越小优先级越高，0为最高优先级。在前端模板选择弹窗中，优先级高的模板会排在前面。', 'shoe-svg-generator'); ?>
                                    </p>
                                </div>

                                <div class="ss-template-actions">
                                    <button type="button" class="button button-primary save-display-settings">
                                        <?php _e('保存设置', 'shoe-svg-generator'); ?>
                                    </button>
                                    <span class="spinner"></span>
                                    <div class="save-status"></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <script type="text/javascript">
                jQuery(document).ready(function($) {
                    // 保存设置
                    $('.save-display-settings').click(function(e) {
                        e.preventDefault();
                        var button = $(this);
                        var item = button.closest('.ss-template-display-item');
                        var spinner = item.find('.spinner');
                        var statusDiv = item.find('.save-status');

                        var data = {
                            action: 'ss_save_template_display',
                            nonce: '<?php echo wp_create_nonce('ss_template_display_nonce'); ?>',
                            product_id: item.data('product-id'),
                            display_image: item.find('.image-id').val(),
                            display_title: item.find('input[name="template_display_title"]').val(),
                            display_priority: item.find('input[name="template_display_priority"]').val()
                        };

                        spinner.addClass('is-active');
                        statusDiv.empty();

                        $.post(ajaxurl, data, function(response) {
                            spinner.removeClass('is-active');
                            if (response.success) {
                                statusDiv.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                            } else {
                                statusDiv.html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                            }

                            setTimeout(function() {
                                statusDiv.empty();
                            }, 3000);
                        });
                    });
                });
                </script>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * 获取所有模板产品
     */
    private function get_template_products() {
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_ss_is_template',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );

        return get_posts($args);
    }

    /**
     * 保存模板展示设置
     */
    public function save_template_display() {
        check_ajax_referer('ss_template_display_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('权限不足', 'shoe-svg-generator')));
        }

        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        $display_image = isset($_POST['display_image']) ? intval($_POST['display_image']) : '';
        $display_title = isset($_POST['display_title']) ? sanitize_text_field($_POST['display_title']) : '';
        $display_priority = isset($_POST['display_priority']) ? intval($_POST['display_priority']) : 0;

        if (!$product_id) {
            wp_send_json_error(array('message' => __('无效的产品ID', 'shoe-svg-generator')));
        }

        // 验证优先级范围
        if ($display_priority < 0) {
            $display_priority = 0;
        } elseif ($display_priority > 999) {
            $display_priority = 999;
        }

        // 更新展示图片
        update_post_meta($product_id, '_ss_template_display_image', $display_image);

        // 更新展示标题
        update_post_meta($product_id, '_ss_template_display_title', $display_title);

        // 更新显示优先级
        update_post_meta($product_id, '_ss_template_display_priority', $display_priority);

        // 【修复】在更新模板配置前，先保护已生成产品的画廊图片顺序
        if (class_exists('SS_SVG_Generator')) {
            $generator = SS_SVG_Generator::get_instance();
            $protected_count = $generator->protect_generated_products_gallery_order();
            error_log("[模板显示管理][修复] 已保护 {$protected_count} 个产品的画廊图片顺序");
        }

        // 清除模板缓存以确保新的优先级设置生效
        if (class_exists('SS_Frontend_Manager')) {
            $frontend_manager = SS_Frontend_Manager::get_instance();
            $frontend_manager->clear_template_cache();
            $frontend_manager->update_template_configs();
        }

        wp_send_json_success(array('message' => __('设置已保存，模板缓存已更新', 'shoe-svg-generator')));
    }

    /**
     * 加载媒体上传脚本
     */
    public function enqueue_media_scripts($hook) {
        // error_log('[SS_Template_Display_Manager] enqueue_media_scripts called with hook: ' . $hook);

        // 修改hook检查逻辑，使用strpos来检查页面标识符
        if (strpos($hook, '_page_ss-template-display') === false && strpos($hook, 'toplevel_page_ss-svg-library') === false) {
            // error_log('[SS_Template_Display_Manager] Hook not matched, scripts not loaded');
            return;
        }

        wp_enqueue_media();
        wp_enqueue_style('wp-jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-dialog');
        wp_enqueue_script('jquery-ui-button');

        // error_log('[SS_Template_Display_Manager] All scripts enqueued');

        // 在管理员页面脚本部分添加一些自定义脚本来确保媒体上传按钮工作
        add_action('admin_footer', function() {
            // error_log('[SS_Template_Display_Manager] admin_footer action triggered');
            ?>
            <script type="text/javascript">
            (function($) {
                $(document).ready(function() {
                    console.log('[SS_Template_Display_Manager] Document ready triggered');

                    // 检查jQuery和wp.media是否可用
                    if (typeof $ === 'undefined') {
                        console.log('[SS_Template_Display_Manager] jQuery is not defined');
                    }
                    if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                        console.log('[SS_Template_Display_Manager] wp.media is not defined');
                    }

                    // 检查按钮是否存在
                    var uploadButtons = $('.upload-image-button');
                    console.log('[SS_Template_Display_Manager] Found ' + uploadButtons.length + ' upload buttons');

                    // 重新绑定图片上传按钮事件
                    $('.upload-image-button').off('click').on('click', function(e) {
                        console.log('[SS_Template_Display_Manager] Upload button clicked');
                        e.preventDefault();

                        var button = $(this);
                        var imagePreviewWrapper = button.siblings('.image-preview-wrapper');
                        var imageIdInput = button.siblings('.image-id');
                        var removeButton = button.siblings('.remove-image-button');

                        var frame = wp.media({
                            title: '选择模板展示图片',
                            multiple: false,
                            library: {
                                type: 'image'
                            }
                        });

                        frame.on('select', function() {
                            console.log('[SS_Template_Display_Manager] Media frame selection made');
                            var attachment = frame.state().get('selection').first().toJSON();
                            imagePreviewWrapper.html('<img src="' + attachment.url + '" class="image-preview">');
                            imageIdInput.val(attachment.id);
                            removeButton.show();
                        });

                        frame.open();
                        console.log('[SS_Template_Display_Manager] Media frame opened');
                    });

                    // 移除图片
                    $('.remove-image-button').off('click').on('click', function(e) {
                        console.log('[SS_Template_Display_Manager] Remove button clicked');
                        e.preventDefault();
                        var button = $(this);
                        var imagePreviewWrapper = button.siblings('.image-preview-wrapper');
                        var imageIdInput = button.siblings('.image-id');

                        imagePreviewWrapper.empty();
                        imageIdInput.val('');
                        button.hide();
                    });
                });
            })(jQuery);
            </script>
            <?php
            // error_log('[SS_Template_Display_Manager] Custom script output completed');
        });
    }
}

// 初始化类
SS_Template_Display_Manager::get_instance();
