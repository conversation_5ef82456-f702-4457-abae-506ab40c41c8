<?php
/**
 * 安全的并发处理器类
 * 
 * 使用进程池替代PHP parallel扩展，避免Segmentation fault
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SS_Safe_Concurrent_Processor {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 最大并发进程数
     */
    private $max_processes = 4;
    
    /**
     * 临时文件目录
     */
    private $temp_dir;
    
    /**
     * 构造函数
     */
    private function __construct() {
        $upload_dir = wp_upload_dir();
        $this->temp_dir = $upload_dir['basedir'] . '/shoe-svg-generator/temp/';
        
        // 确保临时目录存在
        if (!file_exists($this->temp_dir)) {
            wp_mkdir_p($this->temp_dir);
        }
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 安全的并发处理PNG文件
     *
     * @param array $files 文件列表
     * @param int $template_id 模板ID
     * @param int $category_id 类目ID
     * @param string $category_name 类目名称
     * @param string $folder_name 文件夹名称
     * @param int $max_concurrent 最大并发数
     * @param string $background_color 画廊图背景色
     * @param bool $verbose 详细输出
     * @return array 处理结果
     */
    public function process_png_files_safe($files, $template_id, $category_id, $category_name, $folder_name, $max_concurrent = 4, $background_color = 'transparent', $verbose = false) {
        $this->max_processes = min($max_concurrent, count($files));
        $results = array();
        
        if ($verbose) {
            error_log('[安全并发处理器] 开始处理 ' . count($files) . ' 个文件，最大并发: ' . $this->max_processes . '，背景色: ' . $background_color);
        }

        // 创建任务文件
        $task_files = array();
        foreach ($files as $index => $file_info) {
            $task_data = array(
                'file_info' => $file_info,
                'template_id' => $template_id,
                'category_id' => $category_id,
                'category_name' => $category_name,
                'folder_name' => $folder_name,
                'background_color' => $background_color,
                'index' => $index
            );
            
            $task_file = $this->temp_dir . 'task_' . $index . '_' . time() . '.json';
            file_put_contents($task_file, json_encode($task_data));
            $task_files[] = $task_file;
        }
        
        // 分批处理
        $batches = array_chunk($task_files, $this->max_processes);
        
        foreach ($batches as $batch_index => $batch_tasks) {
            if ($verbose) {
                error_log('[安全并发处理器] 处理批次 ' . ($batch_index + 1) . '/' . count($batches) . ' (' . count($batch_tasks) . ' 个任务)');
            }
            
            $batch_results = $this->process_batch_with_processes($batch_tasks, $verbose);
            $results = array_merge($results, $batch_results);
        }
        
        // 清理临时文件
        foreach ($task_files as $task_file) {
            if (file_exists($task_file)) {
                unlink($task_file);
            }
        }
        
        return $results;
    }
    
    /**
     * 使用真正的并发进程池处理批次
     *
     * @param array $task_files 任务文件列表
     * @param bool $verbose 详细输出
     * @return array 处理结果
     */
    private function process_batch_with_processes($task_files, $verbose = false) {
        $processes = array();
        $results = array();
        $total_processes = count($task_files);

        if ($verbose) {
            error_log('[安全并发处理器] 启动真正的并发处理: ' . $total_processes . ' 个任务，最大并发: ' . $this->max_processes);
        }

        // 创建处理脚本文件
        $worker_script = $this->create_worker_script();

        // 【关键修复】使用proc_open创建真正的异步进程
        $descriptors = array(
            0 => array("pipe", "r"),  // stdin
            1 => array("pipe", "w"),  // stdout
            2 => array("pipe", "w")   // stderr
        );

        $active_processes = 0;
        $completed_processes = 0;
        $task_index = 0;

        while ($completed_processes < $total_processes) {
            // 启动新进程（填满进程池）
            while ($active_processes < $this->max_processes && $task_index < $total_processes) {
                $task_file = $task_files[$task_index];
                $result_file = $this->temp_dir . 'result_' . $task_index . '_' . time() . '.json';

                $cmd = "php '$worker_script' '$task_file' '$result_file'";

                // 使用proc_open创建异步进程
                $process = proc_open($cmd, $descriptors, $pipes);

                if (is_resource($process)) {
                    // 设置非阻塞模式
                    stream_set_blocking($pipes[1], false);
                    stream_set_blocking($pipes[2], false);

                    $processes[$task_index] = array(
                        'process' => $process,
                        'pipes' => $pipes,
                        'task_file' => $task_file,
                        'result_file' => $result_file,
                        'start_time' => microtime(true),
                        'completed' => false
                    );

                    $active_processes++;

                    if ($verbose) {
                        error_log('[安全并发处理器] 启动异步进程 #' . ($task_index + 1) . ' (活跃: ' . $active_processes . '/' . $this->max_processes . ')');
                    }
                }

                $task_index++;

                // 短暂延迟避免系统过载
                usleep(10000); // 0.01秒
            }

            // 检查已完成的进程（非阻塞）
            foreach ($processes as $proc_index => $proc_data) {
                if (!$proc_data['completed']) {
                    $status = proc_get_status($proc_data['process']);

                    // 检查进程是否完成
                    if (!$status['running']) {
                        // 进程已完成，读取结果
                        if (file_exists($proc_data['result_file'])) {
                            $result_content = file_get_contents($proc_data['result_file']);
                            if ($result_content) {
                                $result = json_decode($result_content, true);
                                if ($result) {
                                    $result['total_duration'] = microtime(true) - $proc_data['start_time'];
                                    $results[] = $result;

                                    if ($verbose) {
                                        error_log('[安全并发处理器] 异步进程 #' . ($proc_index + 1) . ' 完成: ' . $result['filename'] . ' - ' .
                                                 ($result['success'] ? '成功 (ID: ' . $result['product_id'] . ')' : '失败: ' . $result['error']) .
                                                 ' - 耗时: ' . round($result['total_duration'], 2) . 's');
                                    }
                                }
                            }

                            // 清理结果文件
                            unlink($proc_data['result_file']);
                        }

                        // 清理任务文件
                        if (file_exists($proc_data['task_file'])) {
                            unlink($proc_data['task_file']);
                        }

                        // 关闭进程和管道
                        fclose($proc_data['pipes'][0]);
                        fclose($proc_data['pipes'][1]);
                        fclose($proc_data['pipes'][2]);
                        proc_close($proc_data['process']);

                        $processes[$proc_index]['completed'] = true;
                        $active_processes--;
                        $completed_processes++;
                    }
                }
            }

            // 短暂休息避免CPU占用过高
            if ($active_processes > 0) {
                usleep(50000); // 0.05秒
            }
        }

        // 清理工作脚本
        if (file_exists($worker_script)) {
            unlink($worker_script);
        }

        if ($verbose) {
            error_log('[安全并发处理器] 真正的并发处理完成: ' . $completed_processes . '/' . $total_processes . ' 个任务');
        }

        return $results;
    }

    /**
     * 创建工作进程脚本
     *
     * @return string 脚本文件路径
     */
    private function create_worker_script() {
        $script_content = <<<'SCRIPT'
<?php
// PNG产品生成工作进程
if ($argc < 3) {
    exit(1);
}

$task_file = $argv[1];
$result_file = $argv[2];

try {
    // 加载WordPress环境
    require_once '/www/wwwroot/printeeque.com/wp-load.php';

    // 读取任务数据
    if (!file_exists($task_file)) {
        throw new Exception('任务文件不存在');
    }

    $task_data = json_decode(file_get_contents($task_file), true);
    if (!$task_data) {
        throw new Exception('任务数据解析失败');
    }

    $file_info = $task_data['file_info'];
    $template_id = $task_data['template_id'];
    $category_id = $task_data['category_id'];
    $category_name = $task_data['category_name'];
    $folder_name = $task_data['folder_name'];

    $start_time = microtime(true);
    $png_filename = $file_info['filename'];
    $png_file_path = isset($file_info['file_path']) ? $file_info['file_path'] :
        ('/www/wwwroot/printeeque.com/wp-content/uploads/png/' . $folder_name . '/' . $png_filename);

    // 记录处理参数
    error_log('[并发工作进程] 开始处理 - 文件: ' . $png_filename . ', 路径: ' . $png_file_path . ', 模板: ' . $template_id . ', 类目: ' . $category_id . ' (' . $category_name . ')');

    // 验证文件存在
    if (!file_exists($png_file_path)) {
        throw new Exception('PNG文件不存在: ' . $png_file_path);
    }

    // 验证参数完整性
    if (!$template_id || !$category_id || !$category_name) {
        throw new Exception('参数不完整 - 模板ID: ' . $template_id . ', 类目ID: ' . $category_id . ', 类目名: ' . $category_name);
    }

    // 确保类已加载
    if (!class_exists('SS_SVG_Generator')) {
        throw new Exception('SS_SVG_Generator类未加载');
    }

    // 获取背景色参数
    $background_color = isset($task_data['background_color']) ? $task_data['background_color'] : 'transparent';
    error_log('[并发工作进程] 背景色参数: ' . $background_color);

    // 使用正确的PNG产品生成方法
    $generator = SS_SVG_Generator::get_instance();
    $product_id = $generator->ss_create_product_from_png(
        $png_file_path,
        $template_id,
        $category_id,
        $category_name,
        $background_color
    );

    // 记录处理结果
    error_log('[并发工作进程] PNG产品生成 - 文件: ' . $png_filename . ', 模板: ' . $template_id . ', 类目: ' . $category_id . ', 结果: ' . ($product_id ? $product_id : '失败'));

    $duration = microtime(true) - $start_time;

    $result = array(
        'success' => $product_id !== false,
        'product_id' => $product_id,
        'filename' => $png_filename,
        'duration' => $duration,
        'error' => $product_id ? null : '产品创建失败',
        'worker_pid' => getmypid()
    );

} catch (Exception $e) {
    $result = array(
        'success' => false,
        'product_id' => null,
        'filename' => isset($png_filename) ? $png_filename : '未知',
        'duration' => isset($start_time) ? (microtime(true) - $start_time) : 0,
        'error' => '工作进程异常: ' . $e->getMessage(),
        'worker_pid' => getmypid()
    );
}

// 保存结果
file_put_contents($result_file, json_encode($result));
exit(0);
?>
SCRIPT;

        $script_file = $this->temp_dir . 'worker_' . time() . '_' . getmypid() . '.php';
        file_put_contents($script_file, $script_content);

        return $script_file;
    }
    
    /**
     * 检查是否支持安全并发处理
     *
     * @return bool
     */
    public static function is_supported() {
        // 检查是否可以执行PHP命令
        return function_exists('popen') && function_exists('pclose') && function_exists('exec');
    }

    /**
     * 获取系统性能信息
     *
     * @return array 性能信息
     */
    public function get_system_performance() {
        $performance = array();

        // CPU负载
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $performance['cpu_load'] = $load[0]; // 1分钟平均负载
        }

        // 内存使用
        $performance['memory_usage'] = memory_get_usage(true);
        $performance['memory_peak'] = memory_get_peak_usage(true);

        // 可用内存
        if (function_exists('shell_exec')) {
            $free_memory = shell_exec('free -b | grep Mem | awk \'{print $7}\'');
            if ($free_memory) {
                $performance['free_memory'] = intval(trim($free_memory));
            }
        }

        return $performance;
    }

    /**
     * 动态调整并发数
     *
     * @param int $current_concurrent 当前并发数
     * @param array $performance 性能信息
     * @return int 调整后的并发数
     */
    public function adjust_concurrent_count($current_concurrent, $performance) {
        $adjusted = $current_concurrent;

        // 基于CPU负载调整
        if (isset($performance['cpu_load'])) {
            $cpu_cores = $this->get_cpu_cores();
            $cpu_usage_percent = ($performance['cpu_load'] / $cpu_cores) * 100;

            if ($cpu_usage_percent > 90) {
                $adjusted = max(1, $adjusted - 2);
            } elseif ($cpu_usage_percent > 70) {
                $adjusted = max(1, $adjusted - 1);
            } elseif ($cpu_usage_percent < 50 && $adjusted < $this->max_processes) {
                $adjusted = min($this->max_processes, $adjusted + 1);
            }
        }

        // 基于内存使用调整
        if (isset($performance['free_memory']) && $performance['free_memory'] < 1024 * 1024 * 1024) { // 小于1GB
            $adjusted = max(1, $adjusted - 1);
        }

        return $adjusted;
    }

    /**
     * 获取CPU核心数
     *
     * @return int CPU核心数
     */
    private function get_cpu_cores() {
        $cores = 1;

        if (function_exists('shell_exec')) {
            $cores_output = shell_exec('nproc');
            if ($cores_output) {
                $cores = intval(trim($cores_output));
            }
        }

        return max(1, $cores);
    }

    /**
     * 清理临时文件
     */
    public function cleanup_temp_files() {
        if (is_dir($this->temp_dir)) {
            $files = glob($this->temp_dir . '*');
            $cleaned = 0;

            foreach ($files as $file) {
                if (is_file($file) && (time() - filemtime($file)) > 3600) { // 1小时前的文件
                    unlink($file);
                    $cleaned++;
                }
            }

            if ($cleaned > 0) {
                error_log('[安全并发处理器] 清理了 ' . $cleaned . ' 个临时文件');
            }
        }
    }
}
