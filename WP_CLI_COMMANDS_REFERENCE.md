# 🚀 Shoe SVG Generator WP CLI 命令参考手册

## 📋 命令概览

本插件提供了完整的WP CLI命令集，用于SVG文件处理、转换、压缩和OSS同步。所有命令都使用下划线（_）连接，而非连字符（-）。

### 命令分类

| 分类 | 命令数量 | 主要功能 |
|------|----------|----------|
| **核心功能** | 6个 | 一键式流程、SVG产品生成、PNG产品生成、完整转换流程和前端预览图生成 |
| **文件管理** | 4个 | 本地文件清理和管理（包含重要的clean命令） |
| **OSS操作** | 2个 | OSS文件检查和转移（仅FILF文件） |
| **数据管理** | 2个 | JSON记录生成和管理 |
| **系统维护** | 3个 | 清理、统计和维护 |
| **自定义类目** | 1个 | 自定义匹配类目专用清理 |

**总计：18个核心命令**（新增PNG产品生成相关命令）

---

## 🎯 核心功能命令（最重要）

### 1. generate_and_sync_complete
**【新增】一键式产品生成和OSS同步完整流程（推荐使用）**

```bash
wp ss generate_and_sync_complete [选项]
```

#### 基本参数
- `--category=<ids>` : 指定产品类目ID（逗号分隔）
- `--verbose` : 显示详细日志
- `--allow_root` : 允许root用户执行

#### 产品生成参数
- `--max-parallel=<num>` : 最大并行进程数（默认4）
- `--batch-size=<num>` : 每批处理的类目数量（默认5）
- `--cpu-threshold=<percent>` : CPU使用率阈值（默认80%）
- `--memory-threshold=<percent>` : 内存使用率阈值（默认85%）

#### 转换参数
- `--threads=<num>` : 转换线程数（默认16）
- `--conversion-batch-size=<num>` : 转换批次大小（默认50）

#### 流程控制参数
- `--skip-product-generation` : 跳过产品生成步骤
- `--skip-svg-to-png` : 跳过SVG到PNG转换
- `--skip-png-to-filf` : 跳过PNG到FILF转换
- `--skip-oss-upload` : 跳过OSS上传
- `--skip-cleanup` : 跳过本地文件清理
- `--auto-cleanup` : 自动清理（转换后立即删除原始SVG，上传后立即删除本地FILF）

#### 覆盖策略参数
- `--no-overwrite` : 不覆盖OSS中已存在的文件
- `--backup-old` : 覆盖前备份OSS中的旧文件

#### 使用示例
```bash
# 完整一键式流程（推荐）
wp ss generate_and_sync_complete --max-parallel=8 --threads=16 --verbose --allow-root

# 完整流程 + 自动清理（推荐，节省磁盘空间）
wp ss generate_and_sync_complete --auto-cleanup --max-parallel=8 --threads=16 --verbose --allow-root

# 处理指定类目
wp ss generate_and_sync_complete --category=123,456 --verbose --allow-root

# 高性能模式 + 自动清理
wp ss generate_and_sync_complete --auto-cleanup --max-parallel=12 --cpu-threshold=90 --threads=24 --verbose --allow-root

# 仅产品生成和记录更新
wp ss generate_and_sync_complete --skip-svg-to-png --skip-png-to-filf --skip-oss-upload --verbose --allow-root

# 跳过清理步骤
wp ss generate_and_sync_complete --skip-cleanup --verbose --allow-root
```

#### 流程说明
此命令整合了以下完整流程：
1. **产品生成**: 执行SVG填色和产品生成
2. **JSON记录更新**: 自动更新svg-records目录下的记录
3. **格式转换和OSS同步**: SVG→PNG→FILF转换并上传到OSS
4. **本地清理**: 清理本地临时文件

**注意**: 只有转换后的FILF文件会上传到OSS，原始SVG文件保留在本地

### 2. generate_products
**产品生成过程（现已包含JSON记录自动生成功能）**

```bash
wp ss generate_products [选项]
```

#### 基本参数
- `--category=<ids>` : 指定产品类目ID（逗号分隔）
- `--verbose` : 显示详细日志
- `--allow_root` : 允许root用户执行

#### 性能参数
- `--max_parallel=<num>` : 最大并行进程数（默认4）
- `--batch_size=<num>` : 每批处理的类目数量（默认5）
- `--cpu_threshold=<percent>` : CPU使用率阈值（默认80%）
- `--memory_threshold=<percent>` : 内存使用率阈值（默认85%）

#### 高级参数
- `--performance_mode` : 启用高性能模式
- `--adaptive_delay` : 启用自适应延迟
- `--max_wait_time=<seconds>` : 资源等待最大时间（默认120秒）
- `--skip-json-records` : 跳过JSON记录生成（默认：否，会自动生成）

#### 使用示例
```bash
# 为所有底层类目生成产品（最常用）
wp ss generate_products --max_parallel=16 --cpu_threshold=85 --batch_size=16 --verbose --allow_root

# 为指定类目生成产品
wp ss generate_products --category=123,456 --max_parallel=8 --verbose --allow_root

# 高性能模式生成
wp ss generate_products --performance_mode --max_parallel=12 --cpu_threshold=95 --verbose --allow_root

# 自适应延迟模式
wp ss generate_products --adaptive_delay --max_wait_time=60 --verbose --allow_root

# 生成产品但跳过JSON记录生成
wp ss generate_products --skip-json-records --verbose --allow_root
```

### 3. convert_and_sync_optimized
**完整的SVG转换和OSS同步流程（单独使用）**

```bash
wp ss convert_and_sync_optimized [选项]
```

#### 基本参数
- `--category=<ids>` : 指定产品类目ID（逗号分隔）
- `--threads=<num>` : 并发线程数（默认16）
- `--batch_size=<num>` : 批处理大小（默认50）
- `--verbose` : 显示详细日志
- `--allow_root` : 允许root用户执行

#### 流程控制参数
- `--skip_svg_to_png` : 跳过SVG转PNG步骤
- `--skip_png_to_filf` : 跳过PNG转FILF步骤
- `--skip_upload` : 跳过OSS上传步骤

#### 覆盖策略参数
- `--no_overwrite` : 不覆盖OSS中已存在的文件
- `--backup_old` : 覆盖前备份OSS中的旧文件

#### 自动清理参数
- `--auto-cleanup` : 自动清理（转换后立即删除原始SVG，上传后立即删除本地FILF）
- `--cleanup-svg-after-conversion` : 仅在SVG→PNG转换完成后立即清理原始SVG文件
- `--cleanup-filf-after-upload` : 仅在OSS上传完成后立即清理本地FILF文件

#### 使用示例
```bash
# 完整流程（推荐）
wp ss convert_and_sync_optimized --threads=16 --batch_size=50 --verbose --allow_root

# 完整流程 + 自动清理（推荐，节省磁盘空间）
wp ss convert_and_sync_optimized --auto-cleanup --threads=16 --batch_size=50 --verbose --allow_root

# 指定类目处理
wp ss convert_and_sync_optimized --category=123,456 --threads=8 --verbose --allow_root

# 备份模式（安全）
wp ss convert_and_sync_optimized --backup_old --threads=16 --verbose --allow_root

# 增量模式（不覆盖）
wp ss convert_and_sync_optimized --no_overwrite --threads=16 --verbose --allow_root

# 仅本地转换
wp ss convert_and_sync_optimized --skip_upload --threads=16 --verbose --allow_root

# 仅清理转换后的SVG文件
wp ss convert_and_sync_optimized --cleanup-svg-after-conversion --threads=16 --verbose --allow_root

# 仅清理上传后的FILF文件
wp ss convert_and_sync_optimized --cleanup-filf-after-upload --threads=16 --verbose --allow_root
```

### 4. generate_frontend_previews
**【新增】前端产品类目SVG预览图生成**

```bash
wp ss generate_frontend_previews [选项]
```

#### 基本参数
- `--category=<ids>` : 指定产品类目ID（逗号分隔）
- `--all` : 处理所有适合的产品类目
- `--force` : 强制重新生成，即使已存在预览图
- `--batch-size=<number>` : 每批处理的SVG文件数量（默认：8）
- `--max-concurrent=<number>` : 最大并发进程数（默认：自动计算）
- `--verbose` : 显示详细日志
- `--dry-run` : 预览模式，不实际生成
- `--skip-color-extraction` : 跳过颜色提取，仅处理已有预设颜色的类目

#### 使用示例
```bash
# 为所有适合的类目生成SVG预览图
wp ss generate_frontend_previews --verbose --allow_root

# 为指定类目生成SVG预览图
wp ss generate_frontend_previews --category=123,456 --verbose --allow_root

# 强制重新生成所有预览图
wp ss generate_frontend_previews --all --force --verbose --allow_root

# 预览模式，查看将要处理的类目
wp ss generate_frontend_previews --dry-run --verbose --allow_root

# 高性能模式处理
wp ss generate_frontend_previews --batch-size=16 --max-concurrent=12 --verbose --allow_root

# 仅处理已有预设颜色的类目
wp ss generate_frontend_previews --skip-color-extraction --verbose --allow_root
```

#### 功能说明
此命令专门用于前端产品类目的SVG预览图生成：
1. **智能类目筛选**: 自动识别适合生成预览图的类目
2. **颜色自动处理**: 支持预设颜色和自动颜色提取
3. **批量并发处理**: 高效的批量生成和并发处理
4. **冲突检测**: 避免与WP CLI产品生成冲突
5. **预览模式**: 支持dry-run预览将要处理的类目

**注意**: 此命令仅处理前端预览图，不会生成实际产品

### 5. generate_products_from_png
**【PNG产品生成】从PNG文件直接生成产品**

```bash
wp ss generate_products_from_png [选项]
```

#### 基本参数
- `--template_id=<id>` : 模板产品ID（必需）
- `--max_parallel=<num>` : 最大并行进程数（默认8，高性能服务器可设置32-64）
- `--cpu_threshold=<percent>` : CPU使用率阈值（默认90%，高性能服务器可设置95%）
- `--batch_size=<num>` : 每批处理的文件数量（默认10，高性能服务器可设置20-50）
- `--memory_threshold=<percent>` : 内存使用率阈值（默认85%，高性能服务器可设置90%）
- `--dry_run` : 预览模式，不实际生成产品
- `--verbose` : 显示详细日志
- `--white` : 设置第一张画廊图背景为白色
- `--black` : 设置第一张画廊图背景为黑色

#### 使用示例
```bash
# 基本使用
wp ss generate_products_from_png --template_id=2025 --verbose --allow_root

# 高性能服务器配置（CPU 96核，内存182GB）
wp ss generate_products_from_png --template_id=2025 --max_parallel=32 --cpu_threshold=95 --batch_size=20 --memory_threshold=90 --verbose --allow_root

# 预览模式
wp ss generate_products_from_png --template_id=2025 --dry_run --verbose --allow_root

# 设置白色背景
wp ss generate_products_from_png --template_id=2025 --white --verbose --allow_root

# 设置黑色背景
wp ss generate_products_from_png --template_id=2025 --black --verbose --allow_root

# 中等性能配置
wp ss generate_products_from_png --template_id=2025 --max_parallel=16 --cpu_threshold=85 --batch_size=15 --verbose --allow_root
```

#### 功能说明
此命令专门用于从PNG文件直接生成产品：
1. **目录扫描**: 扫描PNG目录结构，识别所有包含PNG文件的子文件夹
2. **类目创建**: 基于文件夹名称自动创建产品类目
3. **JSON记录生成**: 为每个文件夹生成JSON记录文件，跟踪处理状态
4. **图片处理**: 加载PNG文件并裁剪透明边缘，保持宽高比植入产品模板
5. **产品生成**: 创建产品并设置主图和画廊图片，复制模板产品的所有数据

**注意**: 此命令跳过SVG处理流程，直接使用PNG文件生成产品

### 6. png_concurrent
**【PNG大数据量并发处理】高性能PNG产品生成**

```bash
wp ss png_concurrent [选项]
```

#### 基本参数
- `--template_id=<id>` : 模板产品ID（必需）
- `--max_parallel=<number>` : 最大并发进程数（默认16）
- `--batch_size=<number>` : 每批处理文件数（默认50）
- `--auto_adjust` : 自动调整并发数基于系统性能
- `--folder=<n>` : 指定处理的文件夹名称
- `--dry_run` : 预览模式，不实际生成产品
- `--verbose` : 显示详细信息
- `--white` : 设置第一张画廊图背景为白色
- `--black` : 设置第一张画廊图背景为黑色

#### 使用示例
```bash
# 高并发处理所有PNG文件
wp ss png_concurrent --template_id=324761 --max_parallel=32 --verbose --allow_root

# 自动调整并发数
wp ss png_concurrent --template_id=324761 --auto_adjust --verbose --allow_root

# 处理指定文件夹
wp ss png_concurrent --template_id=324761 --folder=test --max_parallel=16 --allow_root

# 大批次高并发处理
wp ss png_concurrent --template_id=324761 --max_parallel=64 --batch_size=100 --auto_adjust --verbose --allow_root

# 设置白色背景
wp ss png_concurrent --template_id=324761 --white --verbose --allow_root

# 设置黑色背景
wp ss png_concurrent --template_id=324761 --black --verbose --allow_root
```

#### 功能说明
此命令专为大数据量PNG文件处理优化：
1. **智能并发**: 支持自动调整并发数基于系统性能
2. **批量处理**: 高效的批量文件处理机制
3. **性能监控**: 实时监控系统资源使用情况
4. **安全并发**: 使用安全并发处理器避免系统过载
5. **文件夹选择**: 支持指定特定文件夹进行处理

**注意**: 此命令适用于大规模PNG文件处理，需要系统支持并发处理

---

## 🔄 转换和同步命令
**完整的SVG转换和OSS同步流程**

```bash
wp ss convert_and_sync_optimized [选项]
```

#### 基本参数
- `--category=<ids>` : 指定产品类目ID（逗号分隔）
- `--threads=<num>` : 并发线程数（默认16）
- `--batch_size=<num>` : 批处理大小（默认50）
- `--verbose` : 显示详细日志
- `--allow_root` : 允许root用户执行

#### 流程控制参数
- `--skip_svg_to_png` : 跳过SVG转PNG步骤
- `--skip_png_to_filf` : 跳过PNG转FILF步骤
- `--skip_upload` : 跳过OSS上传步骤

#### 覆盖策略参数
- `--no_overwrite` : 不覆盖OSS中已存在的文件
- `--backup_old` : 覆盖前备份OSS中的旧文件

#### 使用示例
```bash
# 完整流程（推荐）
wp ss convert_and_sync_optimized --threads=16 --batch_size=50 --verbose --allow_root

# 指定类目处理
wp ss convert_and_sync_optimized --category=123,456 --threads=8 --verbose --allow_root

# 备份模式（安全）
wp ss convert_and_sync_optimized --backup_old --threads=16 --verbose --allow_root

# 增量模式（不覆盖）
wp ss convert_and_sync_optimized --no_overwrite --threads=16 --verbose --allow_root

# 仅本地转换
wp ss convert_and_sync_optimized --skip_upload --threads=16 --verbose --allow_root
```

### 4. generate_svg_records
**生成SVG文件JSON记录**

```bash
wp ss generate_svg_records [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--force_regenerate` : 强制重新生成
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 生成所有类目记录
wp ss generate_svg_records --verbose --allow_root

# 生成指定类目记录
wp ss generate_svg_records --category=123,456 --verbose --allow_root

# 强制重新生成
wp ss generate_svg_records --force_regenerate --verbose --allow_root
```

### 5. migrate_svg_to_oss
**将SVG文件转移到OSS**

```bash
wp ss migrate_svg_to_oss [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 转移所有类目的SVG文件到OSS
wp ss migrate_svg_to_oss --verbose --allow_root

# 转移指定类目的SVG文件
wp ss migrate_svg_to_oss --category=123 --verbose --allow_root
```

---

---

## 📁 文件管理命令

### 6. cleanup_local_filf
**清理本地FILF文件**

```bash
wp ss cleanup_local_filf [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--dry_run` : 预览模式，不实际删除
- `--force` : 强制删除，跳过确认
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 预览清理（推荐先执行）
wp ss cleanup_local_filf --dry_run --verbose --allow_root

# 清理所有FILF文件
wp ss cleanup_local_filf --force --verbose --allow_root

# 清理指定类目
wp ss cleanup_local_filf --category=123,456 --verbose --allow_root

# 交互式清理
wp ss cleanup_local_filf --verbose --allow_root
```

### 7. clean
**清理指定类目下的生成产品数据（重要命令）**

```bash
wp ss clean --category_id=<id> [选项]
```

#### 基本参数
- `--category_id=<id>` : 指定要清理的产品类目ID（支持逗号分隔多个ID）
- `--all` : 清理所有类目的生成产品
- `--verbose` : 显示详细清理过程
- `--allow_root` : 允许root用户执行

#### 操作模式参数
- `--dry-run` : 预览模式，不实际删除数据
- `--force` : 强制删除，跳过确认提示
- `--parallel=<number>` : 并行清理进程数（默认：1，最大：16）
- `--include-json-records` : 同时清理JSON记录文件（默认：是）

#### 清理内容
此命令会清理以下内容：
- **生成的产品**：删除类目下的生成产品（保留模板产品）
- **生成产品的关联图片**：删除产品图片和缩略图
- **SVG处理文件**：清理SVG相关的处理文件
- **前端Gallery文件**：清理frontend-gallery目录中的产品图和预览图
- **预览图文件**：清理frontend-gallery/{category}/previews/目录中的预览图
- **最终产品文件**：清理final-products目录中的产品文件
- **JSON记录文件**：清理svg-records目录中的类目JSON记录（可选）
- **已处理SVG记录**：清理term meta中的已处理SVG文件记录
- **类目生成标志**：清理类目的产品生成标志

#### 使用示例
```bash
# 清理单个类目（最常用）
wp ss clean --category_id=44581 --allow_root

# 清理多个类目（逗号分隔）
wp ss clean --category_id=123,456,789 --verbose --allow_root

# 预览模式（推荐先执行）
wp ss clean --category_id=44581 --dry-run --verbose --allow_root

# 强制清理，跳过确认
wp ss clean --category_id=44581 --force --verbose --allow_root

# 并行清理多个类目（高效）
wp ss clean --category_id=123,456,789 --parallel=8 --verbose --allow_root

# 清理所有类目的生成产品
wp ss clean --all --force --verbose --allow_root

# 保留JSON记录文件（便于重新生成）
wp ss clean --category_id=44581 --include-json-records=false --verbose --allow_root

# 批量清理，保留JSON记录
wp ss clean --category_id=123,456,789 --include-json-records=false --parallel=8 --verbose --allow_root
```

#### 安全特性
- **模板保护**：自动识别并保护模板产品，避免误删
- **确认提示**：非强制模式下会要求用户确认（除非使用 `--force`）
- **预览模式**：支持 `--dry-run` 预览将要清理的内容
- **详细日志**：提供完整的清理过程记录
- **并行处理**：支持多进程并行清理，提高效率
- **错误处理**：完善的异常处理和错误统计

#### 注意事项
⚠️ **此操作不可逆！** 建议先使用 `--dry-run` 预览清理内容

⚠️ **保护模板产品**：只删除生成的产品，不会删除模板产品

⚠️ **JSON记录清理**：默认会清理JSON记录文件，使用`--include-json-records=false`可保留

✅ **支持批量操作**：可以同时清理多个类目，提高效率

✅ **智能重置**：清理JSON记录后可完全重新开始，保留记录可快速重新生成

---

### 6. clean_png_products
**【PNG产品清理】清理PNG产品数据**

```bash
wp ss clean_png_products [选项]
```

#### 基本参数
- `--template_id=<id>` : 指定要清理的模板产品ID（支持逗号分隔多个ID）
- `--all` : 清理所有PNG产品
- `--dry-run` : 预览模式，不实际删除数据
- `--force` : 强制删除，跳过确认提示
- `--verbose` : 显示详细清理过程

#### 性能参数
- `--max_parallel=<number>` : 最大并行进程数（默认：8，无上限，支持高并发）
- `--batch_size=<number>` : 每批处理的产品数量（默认：50，无上限，支持大批次）

#### 清理控制参数
- `--include-json-records` : 同时清理JSON记录文件（默认：是）

#### 清理内容
此命令会清理以下内容：
- **PNG生成的产品**：删除基于指定模板生成的PNG产品（保留模板产品）
- **产品关联图片**：删除产品主图和画廊图片（模板产品图片除外）
- **JSON记录文件**：清理png-records目录中的相关JSON记录文件（可选）
- **产品元数据**：清理产品的所有元数据和变体信息

#### 使用示例
```bash
# 清理指定模板的PNG产品（最常用）
wp ss clean_png_products --template_id=2025 --verbose --allow_root

# 清理多个模板的PNG产品
wp ss clean_png_products --template_id=2025,2026,2027 --verbose --allow_root

# 预览模式（推荐先执行）
wp ss clean_png_products --template_id=2025 --dry-run --verbose --allow_root

# 高性能并行清理
wp ss clean_png_products --template_id=2025 --max_parallel=16 --batch_size=100 --verbose --allow_root

# 超高性能服务器配置（192核384GB）
wp ss clean_png_products --template_id=2025 --max_parallel=180 --batch_size=600 --verbose --allow_root

# 强制清理所有PNG产品
wp ss clean_png_products --all --force --verbose --allow_root

# 保留JSON记录文件（便于重新生成）
wp ss clean_png_products --template_id=2025 --include-json-records=false --verbose --allow_root

# 使用连字符版本
wp ss clean-png-products --template_id=2025 --dry-run --verbose --allow_root
```

#### 安全特性
- **模板保护**：自动识别并保护模板产品及其图片，避免误删
- **确认提示**：非强制模式下会要求用户确认（除非使用 `--force`）
- **预览模式**：支持 `--dry-run` 预览将要清理的内容
- **详细日志**：提供完整的清理过程记录和统计信息
- **并行处理**：支持多进程并行清理，提高大批量清理效率
- **错误处理**：完善的异常处理和错误统计

#### 注意事项
⚠️ **此操作不可逆！** 建议先使用 `--dry-run` 预览清理内容

⚠️ **专用于PNG产品**：只清理通过PNG生成的产品，不会影响SVG产品

✅ **保护模板产品**：自动保护模板产品及其关联图片

---

### 7. cleanup_migrated_svg
**清理已转移到OSS的本地SVG文件**

```bash
wp ss cleanup_migrated_svg [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--dry_run` : 预览模式，不实际删除
- `--force` : 强制删除，跳过确认
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 清理所有已转移的SVG文件
wp ss cleanup_migrated_svg --verbose --allow_root

# 预览清理操作
wp ss cleanup_migrated_svg --dry_run --verbose --allow_root

# 强制清理指定类目
wp ss cleanup_migrated_svg --category=123 --force --verbose --allow_root
```

### 9. cleanup_empty_dirs
**清理空目录**

```bash
wp ss cleanup_empty_dirs [选项]
```

#### 参数
- `--path=<path>` : 指定清理路径
- `--dry_run` : 预览模式
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 清理空目录
wp ss cleanup_empty_dirs --verbose --allow_root

# 预览清理操作
wp ss cleanup_empty_dirs --dry_run --verbose --allow_root
```

---

## 🎯 自定义类目专用命令

### 15. clean_custom_categories
**清理自定义匹配类目的所有数据和文件**

```bash
wp ss clean_custom_categories [选项]
```

#### 参数
- `--category=<ids>` : 指定要清理的自定义匹配类目ID（逗号分隔）
- `--all` : 清理所有自定义匹配类目
- `--dry-run` : 预览模式，不实际执行删除操作
- `--verbose` : 显示详细的清理过程日志
- `--force` : 强制执行清理，跳过确认提示

#### 清理内容
- **产品数据**: 删除类目下的生成产品及其元数据（保护模板产品）
- **专用文件**: 清理SVG文件、预览图、产品图
- **专用目录**: 清理 `/custom-matching/svg/{slug}/`、`/custom-matching/previews/{slug}/`、`/custom-matching/products/{slug}/` 等
- **类目缩略图**: 删除类目缩略图及自定义上传的图片
- **自定义图片**: 清理存储在 `/custom-matching/images/` 目录中的用户上传图片
- **类目元数据**: 清理所有自定义匹配相关的term meta
- **父类目清理**: 自动检查并清理空的自定义上传产品父类目
- **类目处理**: 如果包含模板产品则转为普通类目，否则删除整个类目

#### 使用示例
```bash
# 预览清理所有自定义匹配类目
wp ss clean_custom_categories --dry-run --verbose --allow_root

# 清理指定的自定义匹配类目
wp ss clean_custom_categories --category=123,456 --verbose --allow_root

# 强制清理所有自定义匹配类目
wp ss clean_custom_categories --all --force --verbose --allow_root

# 使用连字符版本
wp ss clean-custom-categories --dry-run --verbose --allow_root
```

#### 安全特性
- **类目验证**: 只处理标记为自定义匹配的类目（`_is_custom_matching_category = 1`）
- **模板保护**: 自动识别并保护模板产品，避免误删
- **智能处理**: 包含模板产品的类目转为普通类目，不包含的则完全删除
- **确认提示**: 非强制模式下会要求用户确认（除非使用 `--force`）
- **预览模式**: 支持 `--dry-run` 预览将要清理的内容
- **详细日志**: 提供完整的清理过程记录
- **错误处理**: 完善的异常处理和错误统计

#### 注意事项
⚠️ **此操作不可逆！** 建议先使用 `--dry-run` 预览清理内容

⚠️ **专用命令**: 只清理自定义匹配类目，不会影响普通产品类目

✅ **安全保护**: 自动验证类目类型，避免误删普通类目

---

## ☁️ OSS操作命令

### 10. check_oss_filf
**清理已处理的文件**

```bash
wp ss cleanup_processed_files [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID
- `--type=<type>` : 文件类型（svg/png/filf/all）
- `--force` : 强制删除
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 清理所有已处理文件
wp ss cleanup_processed_files --type=all --force --verbose --allow_root

# 清理指定类型文件
wp ss cleanup_processed_files --type=svg --category=123 --verbose --allow_root
```

### 7. get_directory_stats
**获取目录统计信息**

```bash
wp ss get_directory_stats [选项]
```

#### 参数
- `--path=<path>` : 指定目录路径
- `--detailed` : 显示详细统计
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 获取处理目录统计
wp ss get_directory_stats --detailed --verbose --allow_root
```

---

### 10. check_oss_filf
**检查OSS中FILF文件状态**

```bash
wp ss check_oss_filf [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 检查所有类目OSS状态
wp ss check_oss_filf --verbose --allow_root

# 检查指定类目OSS状态
wp ss check_oss_filf --category=123,456 --verbose --allow_root
```

---

## 🔧 系统维护命令

### 11. clear_category_flags
**清理类目标志**

```bash
wp ss clear_category_flags [选项]
```

#### 参数
- `--category=<ids>` : 指定类目ID（逗号分隔）
- `--all` : 清理所有类目标志
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 清理所有类目标志
wp ss clear_category_flags --all --verbose --allow_root

# 清理指定类目标志
wp ss clear_category_flags --category=123,456 --verbose --allow_root
```

### 12. get_directory_stats
**获取目录统计信息**

```bash
wp ss get_directory_stats [选项]
```

#### 参数
- `--path=<path>` : 指定目录路径
- `--detailed` : 显示详细统计
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 获取处理目录统计
wp ss get_directory_stats --detailed --verbose --allow_root

# 获取指定路径统计
wp ss get_directory_stats --path=/path/to/dir --verbose --allow_root
```

### 13. system_status
**系统状态检查**

```bash
wp ss system_status [选项]
```

#### 参数
- `--detailed` : 显示详细状态
- `--check_dependencies` : 检查依赖
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 检查系统状态
wp ss system_status --detailed --check_dependencies --verbose --allow_root
```

### 14. maintenance_mode
**维护模式控制**

```bash
wp ss maintenance_mode --action=<action> [选项]
```

#### 参数
- `--action=<action>` : 操作（enable/disable/status）
- `--message=<msg>` : 维护消息
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 启用维护模式
wp ss maintenance_mode --action=enable --message="系统维护中" --verbose --allow_root

# 禁用维护模式
wp ss maintenance_mode --action=disable --verbose --allow_root

# 检查维护状态
wp ss maintenance_mode --action=status --verbose --allow_root
```

---

## 📊 命令重要性排序

### 🔥 最高优先级（日常必用）
1. **generate_and_sync_complete** - 一键式完整流程（推荐）
2. **generate_products** - SVG产品生成（单独使用）
3. **generate_products_from_png** - PNG产品生成（直接从PNG生成）
4. **convert_and_sync_optimized** - 完整转换流程（单独使用）

### ⭐ 高优先级（常用）
5. **clean** - 清理指定类目的生成产品数据（重要）
6. **clean_png_products** - 清理PNG产品数据（重要）
7. **png_concurrent** - PNG大数据量并发处理
8. **cleanup_local_filf** - 清理本地FILF文件
9. **check_oss_filf** - 检查OSS文件状态
10. **generate_svg_records** - 生成SVG记录
11. **clean_custom_categories** - 清理自定义匹配类目

### 📈 中等优先级（定期使用）
12. **generate_frontend_previews** - 前端预览图生成
13. **cleanup_migrated_svg** - 清理已转移的SVG文件
14. **migrate_svg_to_oss** - SVG文件转移到OSS（仅在需要时使用）

### 🔧 低优先级（维护使用）
14. **clear_category_flags** - 清理类目标志
15. **cleanup_empty_dirs** - 清理空目录
16. **get_directory_stats** - 目录统计
17. **system_status** - 系统状态检查
18. **maintenance_mode** - 维护模式控制
**同步文件到OSS**

```bash
wp ss sync_to_oss --category=<id> [选项]
```

#### 参数
- `--category=<id>` : 产品类目ID（必需）
- `--type=<type>` : 文件类型（filf/png）
- `--overwrite` : 覆盖已存在文件
- `--verbose` : 显示详细日志

#### 使用示例
```bash
# 同步FILF文件到OSS
wp ss sync_to_oss --category=123 --type=filf --verbose --allow_root
```



---

---

## 📈 性能优化建议

### 服务器配置推荐

| 服务器规格 | generate_products参数 | generate_products_from_png参数 | convert_and_sync_optimized参数 | 适用场景 |
|------------|----------------------|-------------------------------|-------------------------------|----------|
| **2核4GB** | `--max_parallel=4 --batch_size=8` | `--max_parallel=4 --batch_size=8` | `--threads=4 --batch_size=20` | 小规模处理 |
| **4核8GB** | `--max_parallel=8 --batch_size=12` | `--max_parallel=8 --batch_size=12` | `--threads=8 --batch_size=30` | 中等规模处理 |
| **8核16GB** | `--max_parallel=12 --batch_size=16` | `--max_parallel=16 --batch_size=15` | `--threads=16 --batch_size=50` | 大规模处理 |
| **16核32GB+** | `--max_parallel=16 --batch_size=20` | `--max_parallel=32 --batch_size=20` | `--threads=32 --batch_size=100` | 超大规模处理 |
| **96核182GB** | `--max_parallel=24 --batch_size=25` | `--max_parallel=64 --batch_size=50` | `--threads=64 --batch_size=200` | 极限性能处理 |

### 常用组合命令

```bash
# 【推荐】一键式完整流程
wp ss generate_and_sync_complete --max-parallel=8 --threads=16 --verbose --allow-root

# 【推荐】一键式流程 + 自动清理（节省磁盘空间）
wp ss generate_and_sync_complete --auto-cleanup --max-parallel=8 --threads=16 --verbose --allow-root

# 【推荐】高性能一键式流程 + 自动清理
wp ss generate_and_sync_complete --auto-cleanup --max-parallel=12 --cpu-threshold=90 --threads=24 --conversion-batch-size=100 --verbose --allow-root

# 【推荐】指定类目一键式流程
wp ss generate_and_sync_complete --category=123,456 --verbose --allow-root

# PNG产品生成流程
# 【推荐】基本PNG产品生成
wp ss generate_products_from_png --template_id=2025 --verbose --allow-root

# 【推荐】高性能PNG产品生成
wp ss generate_products_from_png --template_id=2025 --max_parallel=32 --cpu_threshold=95 --batch_size=20 --verbose --allow-root

# 【推荐】大数据量PNG并发处理
wp ss png_concurrent --template_id=324761 --max_parallel=64 --batch_size=100 --auto_adjust --verbose --allow-root

# 传统分步骤流程（如需精细控制）
wp ss generate_products --max-parallel=16 --cpu-threshold=85 --batch-size=16 --verbose --allow-root
wp ss convert_and_sync_optimized --auto-cleanup --threads=16 --batch-size=50 --verbose --allow-root

# 重新生成指定类目流程
wp ss check_oss_filf --category=123 --verbose --allow-root
wp ss generate_and_sync_complete --category=123 --backup-old --verbose --allow-root

# 清理指定类目数据流程
wp ss clean --category_id=44581 --dry-run --verbose --allow-root  # 先预览
wp ss clean --category_id=44581 --verbose --allow-root  # 确认后执行

# 批量清理多个类目
wp ss clean --category_id=123,456,789 --parallel=8 --verbose --allow-root

# PNG产品清理流程
# 【推荐】清理指定模板PNG产品
wp ss clean_png_products --template_id=2025 --dry-run --verbose --allow-root  # 先预览
wp ss clean_png_products --template_id=2025 --verbose --allow-root  # 确认后执行

# 【推荐】高性能批量清理PNG产品
wp ss clean_png_products --template_id=2025,2026,2027 --max_parallel=16 --batch_size=100 --verbose --allow-root

# 【推荐】超高性能服务器PNG产品清理（192核384GB）
wp ss clean_png_products --all --force --max_parallel=180 --batch_size=600 --verbose --allow-root

# 强制清理所有PNG产品
wp ss clean_png_products --all --force --verbose --allow-root

# 系统维护流程
wp ss system_status --detailed --check_dependencies --verbose --allow-root
wp ss cleanup_migrated_svg --dry_run --verbose --allow-root
wp ss cleanup_empty_dirs --verbose --allow-root
wp ss clear_category_flags --all --verbose --allow-root

# OSS文件管理流程
wp ss check_oss_filf --verbose --allow-root
```

---

## ⚠️ 已弃用命令

### sync_svg_to_oss_rclone
**状态**: 已弃用
**替代命令**: `migrate_svg_to_oss`
**弃用原因**: Rclone 同步方式已不再维护，SDK 同步方式更稳定可靠
**说明**: 此命令仍可使用但会显示弃用警告，建议迁移到新的 SDK 同步方式

---

## ⚠️ 注意事项

1. **权限要求**: 所有命令都需要 `--allow_root` 参数
2. **命令格式**: 使用下划线（_）而非连字符（-）
3. **日志记录**: 建议始终使用 `--verbose` 参数
4. **备份策略**: 重要操作前建议使用 `--backup_old` 参数
5. **预览模式**: 删除操作前建议使用 `--dry_run` 预览

## 📞 技术支持

如有问题，请查看相关日志文件或联系技术支持。

---

## 📝 更新日志

- **2024-12-18**: 初始版本，包含15个核心命令
- **2024-12-18**: 添加详细的参数说明和使用示例
- **2024-12-18**: 完善命令分类和优先级排序
- **2024-12-18**: 新增 `generate_and_sync_complete` 一键式完整流程命令
- **2024-12-18**: 弃用 `sync_svg_to_oss_rclone` 命令，推荐使用 SDK 同步方式
- **2024-12-18**: 修复一键式流程，移除SVG转移步骤，仅同步FILF文件到OSS
- **2024-12-18**: 新增自动清理功能，转换完成后立即清理原始SVG，上传完成后立即清理本地FILF
- **2024-12-18**: 完善 `clean` 命令文档，添加详细参数说明、使用示例和安全特性介绍
- **2024-12-18**: 增强 `clean` 命令功能，添加JSON记录文件清理控制，支持保留或清理JSON记录
- **2025-01-15**: 新增PNG产品生成相关命令：`generate_products_from_png` 和 `png_concurrent`
- **2025-01-15**: 添加PNG产品生成的详细参数说明、使用示例和性能优化建议
- **2025-01-15**: 清理插件垃圾文件，移除所有测试文件、验证文件和不必要的文档文件
- **2025-01-15**: 更新命令分类和优先级排序，总计18个核心命令
- **2025-01-30**: 新增PNG产品清理命令：`clean_png_products`
- **2025-01-30**: 支持指定模板清理和全部PNG产品清理功能
- **2025-01-30**: 添加并行清理支持，提高大批量PNG产品清理效率
- **2025-01-30**: 完善PNG产品清理的安全特性和详细文档说明
