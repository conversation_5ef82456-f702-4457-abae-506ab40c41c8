<?php
/**
 * Color Science颜色识别器类
 *
 * 基于Color Science科学色彩理论的专业颜色识别系统
 * 替换旧的class-ss-color-processor.php，提供更精准的颜色识别和处理
 *
 * @package Shoe_SVG_Generator
 */

if (!defined('ABSPATH')) {
    define('ABSPATH', '/fake/wordpress/path/'); // 测试环境模拟
}

class SS_Color_Science_Recognizer {
    /**
     * 单例实例
     *
     * @var SS_Color_Science_Recognizer
     */
    private static $instance = null;

    /**
     * 默认配置参数
     *
     * @var array
     */
    private $config = array(
        'vivid_enhancement' => true,           // 默认开启高级鲜艳度调节方案
        'print_compensation' => true,          // 默认启用数码直喷打印补偿
        'print_preset' => 'mid_cotton',        // 默认使用mid_cotton布料预设
        'saturation_adjustment' => 0,          // 饱和度调整（默认0，不调整）
        'lightness_adjustment' => 0,           // 亮度调整（默认0，不调整）
        'color_count' => 5,                    // 颜色识别数量默认为5个（参考文档标准）
        'verbose_mode' => false,               // 详细日志模式
        'enable_dual_shuffle' => true          // 【修复】默认开启双色排列
    );

    /**
     * Python脚本路径
     *
     * @var string
     */
    private $python_script_path;

    /**
     * 获取单例实例
     *
     * @return SS_Color_Science_Recognizer
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        $this->python_script_path = __DIR__ . '/python/color_science_recognizer.py';
        $this->init_python_script();
    }

    /**
     * 初始化Python脚本
     */
    private function init_python_script() {
        // 如果Python脚本不存在，创建它
        if (!file_exists($this->python_script_path)) {
            $this->create_python_script();
        }
    }

    /**
     * 从图片中提取颜色（主要接口）
     *
     * @param string $image_path 图片路径
     * @param int $num_colors 要提取的颜色数量（默认使用配置中的值）
     * @param string $caller 调用者标识（用于日志）
     * @param bool $debug_mode 是否启用调试模式
     * @return array 提取的颜色数组
     */
    public function extract_colors_from_image($image_path, $num_colors = null, $caller = '未知', $debug_mode = false) {
        if ($num_colors === null) {
            $num_colors = $this->config['color_count'];
        }

        $this->log("【Color Science-{$caller}】开始从图片提取颜色: {$image_path}");
        $this->log("【Color Science-{$caller}】配置参数: " . json_encode($this->config));
        $this->log("【Color Science-{$caller}】调试模式: " . ($debug_mode ? '开启' : '关闭'));

        // 检查图片文件是否存在
        if (!file_exists($image_path)) {
            $this->log("【Color Science-{$caller}】错误：图片文件不存在: {$image_path}");
            return array(); // 不使用默认颜色，返回空数组
        }

        // 调用Python脚本进行颜色识别
        $result = $this->call_python_recognizer($image_path, $num_colors, $debug_mode);

        if ($result['status'] === 'success' && !empty($result['colors'])) {
            $colors = array_map(function($color) {
                return $color['hex'];
            }, $result['colors']);

            $this->log("【Color Science-{$caller}】成功提取颜色: " . implode(', ', $colors));
            
            // 如果有调试日志，输出详细信息
            if ($debug_mode && !empty($result['debug_log'])) {
                $this->log("【Color Science-{$caller}】详细处理过程:");
                foreach ($result['debug_log'] as $log_line) {
                    $this->log("【Color Science-{$caller}】  " . $log_line);
                }
            }
            
            return $colors;
        } else {
            $this->log("【Color Science-{$caller}】颜色提取失败，跳过处理: " . ($result['message'] ?? '未知错误'));
            return array(); // 不使用默认颜色，返回空数组
        }
    }

    /**
     * 【双色排列方案】生成双色排列的颜色组合
     * 
     * @param array $original_colors 原始颜色数组 [color1, color2, color3, color4, color5]
     * @return array 双色排列的颜色组合 [scheme_A, scheme_B]
     */
    public function generate_dual_color_schemes($original_colors) {
        if (empty($original_colors) || count($original_colors) < 5) {
            return array($original_colors, $original_colors); // 不足5色时返回原色
        }

        // 双色排列方案A: 原始顺序 [颜色1, 颜色2, 颜色3, 颜色4, 颜色5]
        $scheme_a = $original_colors;

        // 双色排列方案B: 重排顺序 [颜色3, 颜色4, 颜色1, 颜色2, 颜色5]
        $scheme_b = array(
            $original_colors[2], // 颜色3 -> 1位
            $original_colors[3], // 颜色4 -> 2位  
            $original_colors[0], // 颜色1 -> 3位
            $original_colors[1], // 颜色2 -> 4位
            $original_colors[4]  // 颜色5 -> 5位（保持不变）
        );

        return array($scheme_a, $scheme_b);
    }

    /**
     * 【双色排列方案】随机选择颜色方案
     * 
     * @param array $scheme_a 方案A的颜色
     * @param array $scheme_b 方案B的颜色
     * @param int $probability_b 选择B方案的概率（默认50）
     * @return array 选中的颜色方案 (A或B)
     */
    public function select_random_color_scheme($scheme_a, $scheme_b, $probability_b = 50) {
        // 使用可配置的概率进行选择
        $selected_scheme = (mt_rand(1, 100) <= $probability_b) ? $scheme_b : $scheme_a;
        
        $scheme_name = ($selected_scheme === $scheme_b) ? "B" : "A";
        $this->log("【双色排列方案】随机选择颜色方案: " . $scheme_name . 
                  " (概率: " . $probability_b . "%)" . 
                  " - 颜色: " . implode(', ', $selected_scheme));
        
        return $selected_scheme;
    }

    /**
     * 调用Python颜色识别器
     *
     * @param string $image_path 图片路径
     * @param int $num_colors 颜色数量
     * @param bool $debug_mode 是否启用调试模式
     * @return array 识别结果
     */
    private function call_python_recognizer($image_path, $num_colors, $debug_mode = false) {
        // 准备参数
        $params = array(
            'image_path' => $image_path,
            'color_count' => $num_colors,
            'vivid_enhancement' => $this->config['vivid_enhancement'],
            'print_compensation' => $this->config['print_compensation'],
            'print_preset' => $this->config['print_preset'],
            'saturation_adjustment' => $this->config['saturation_adjustment'],
            'lightness_adjustment' => $this->config['lightness_adjustment'],
            'verbose_mode' => $debug_mode || $this->config['verbose_mode']
        );

        // 将参数编码为JSON
        $params_json = json_encode($params);
        $temp_params_file = sys_get_temp_dir() . '/color_science_params_' . uniqid() . '.json';
        file_put_contents($temp_params_file, $params_json);

        // 获取Python命令
        $python_cmd = $this->get_python_command();

        // 构建命令
        $command = sprintf(
            '%s %s %s 2>&1',
            escapeshellcmd($python_cmd),
            escapeshellarg($this->python_script_path),
            escapeshellarg($temp_params_file)
        );

        $this->log("【Color Science】执行命令: {$command}");

        // 执行命令
        $output = shell_exec($command);
        
        // 清理临时文件
        if (file_exists($temp_params_file)) {
            unlink($temp_params_file);
        }

        $this->log("【Color Science】Python输出: " . $output);

        // 解析输出
        $result = json_decode($output, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return array(
                'status' => 'error',
                'message' => 'Python脚本输出解析失败: ' . $output
            );
        }

        return $result;
    }

    /**
     * 获取Python命令
     *
     * @return string Python命令
     */
    private function get_python_command() {
        $python_commands = array('python3', 'python');

        foreach ($python_commands as $cmd) {
            $test_output = shell_exec("which {$cmd} 2>/dev/null");
            if (!empty($test_output)) {
                return $cmd;
            }
        }

        return 'python3'; // 默认使用python3
    }

    /**
     * 获取默认颜色
     *
     * @param int $num_colors 颜色数量
     * @return array 默认颜色数组
     */
    public function get_default_colors($num_colors = 8) {
        $default_colors = array(
            '#FF6B6B', // 红色
            '#4ECDC4', // 青色
            '#45B7D1', // 蓝色
            '#96CEB4', // 绿色
            '#FFEAA7', // 黄色
            '#DDA0DD', // 紫色
            '#98D8C8', // 薄荷绿
            '#F7DC6F'  // 金黄色
        );

        return array_slice($default_colors, 0, $num_colors);
    }

    /**
     * 设置配置参数
     *
     * @param array $config 配置数组
     */
    public function set_config($config) {
        $this->config = array_merge($this->config, $config);
    }

    /**
     * 获取配置参数
     *
     * @return array 配置数组
     */
    public function get_config() {
        return $this->config;
    }

    /**
     * 启用调试模式提取颜色
     *
     * @param string $image_path 图片路径
     * @param int $num_colors 要提取的颜色数量
     * @param string $caller 调用者标识
     * @return array 提取的颜色数组
     */
    public function extract_colors_with_debug($image_path, $num_colors = null, $caller = '调试模式') {
        return $this->extract_colors_from_image($image_path, $num_colors, $caller, true);
    }

    /**
     * 检查颜色是否为黑白灰（兼容性方法）
     *
     * @param string $hex_color 十六进制颜色
     * @return bool 是否为黑白灰
     */
    public function is_black_white_or_gray($hex_color) {
        // 转换为RGB
        $rgb = $this->hex_to_rgb($hex_color);
        if (!$rgb) {
            return false;
        }

        list($r, $g, $b) = $rgb;

        // 计算亮度
        $brightness = ($r * 0.299 + $g * 0.587 + $b * 0.114) / 255;

        // 计算饱和度
        $max = max($r, $g, $b) / 255;
        $min = min($r, $g, $b) / 255;
        $saturation = $max == 0 ? 0 : ($max - $min) / $max;

        // 判断黑白灰
        return $brightness < 0.2 || $brightness > 0.9 || $saturation < 0.15;
    }

    /**
     * 十六进制转RGB
     *
     * @param string $hex 十六进制颜色
     * @return array|false RGB数组或false
     */
    private function hex_to_rgb($hex) {
        $hex = ltrim($hex, '#');
        if (strlen($hex) !== 6) {
            return false;
        }

        return array(
            hexdec(substr($hex, 0, 2)),
            hexdec(substr($hex, 2, 2)),
            hexdec(substr($hex, 4, 2))
        );
    }

    /**
     * 创建Python脚本文件
     */
    private function create_python_script() {
        $python_dir = dirname($this->python_script_path);
        if (!is_dir($python_dir)) {
            if (!mkdir($python_dir, 0755, true)) {
                $this->log("无法创建Python目录: {$python_dir}");
                return;
            }
        }

        // 检查是否已存在外部Python文件
        $external_python_file = dirname(__FILE__) . '/python/color_science_recognizer.py';
        if (file_exists($external_python_file)) {
            // 使用外部Python文件
            $this->python_script_path = $external_python_file;
            $this->log("使用外部Python脚本: {$this->python_script_path}");
            return;
        }

        // 如果外部文件不存在，创建内嵌脚本（备用方案）
        $script_content = $this->get_python_script_content();
        if (file_put_contents($this->python_script_path, $script_content) === false) {
            $this->log("无法创建Python脚本: {$this->python_script_path}");
            return;
        }

        chmod($this->python_script_path, 0755);
        $this->log("Python脚本创建成功: {$this->python_script_path}");
    }

    /**
     * 日志记录方法
     *
     * @param string $message 日志消息
     */
    private function log($message) {
        if (function_exists('error_log')) {
            error_log($message);
        } else {
            echo $message . "\n";
        }
    }

    /**
     * 获取Python脚本内容
     *
     * @return string Python脚本内容
     */
    private function get_python_script_content() {
        return '#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Color Science颜色识别器
基于科学色彩理论的专业颜色识别系统
"""

import sys
import json
import math
import os
from datetime import datetime

# 检查并导入必要的库
try:
    import numpy as np
    from PIL import Image
    from sklearn.cluster import KMeans
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    IMPORT_ERROR = str(e)

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print(json.dumps({"status": "error", "message": "参数错误"}))
        return

    params_file = sys.argv[1]
    
    try:
        with open(params_file, "r", encoding="utf-8") as f:
            params = json.load(f)
    except Exception as e:
        print(json.dumps({"status": "error", "message": f"参数文件读取失败: {e}"}))
        return

    # 执行颜色识别
    result = recognize_image_colors(params)
    print(json.dumps(result, ensure_ascii=False))

def recognize_image_colors(params):
    """识别图片颜色"""
    try:
        # 检查依赖库是否可用
        if not DEPENDENCIES_AVAILABLE:
            return {"status": "error", "message": f"缺少必要的库: {IMPORT_ERROR}"}

        image_path = params["image_path"]
        color_count = params.get("color_count", 8)
        
        # 检查图片文件
        if not os.path.exists(image_path):
            return {"status": "error", "message": f"图片文件不存在: {image_path}"}

        # 加载图片
        try:
            image = Image.open(image_path)
            if image.mode != "RGB":
                image = image.convert("RGB")
        except Exception as e:
            return {"status": "error", "message": f"图片加载失败: {e}"}

        # 提取颜色
        colors, counts, total_pixels = extract_colors_kmeans(image, color_count * 2)

        # 创建颜色信息列表
        color_info_list = []
        for i, (color, count) in enumerate(zip(colors, counts)):
            percentage = (count / total_pixels) * 100
            hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"

            # 使用Color Science分类
            label = classify_color_science(tuple(color))

            color_info = {
                "color_code": hex_color,
                "hex": hex_color,
                "rgb": tuple(int(c) for c in color),
                "percentage": percentage,
                "label": label,
                "colour_science": label
            }
            color_info_list.append(color_info)

        # 按占比排序
        color_info_list.sort(key=lambda x: x["percentage"], reverse=True)

        # 应用Color Science处理逻辑
        processed_colors = apply_color_science_processing(color_info_list, color_count, params)

        return {
            "status": "success",
            "image_path": image_path,
            "colors": processed_colors,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        return {"status": "error", "message": f"识别失败: {e}"}

def extract_colors_kmeans(image, n_colors):
    """使用K-means提取颜色"""
    try:
        # 将图片转换为numpy数组
        data = np.array(image)
        data = data.reshape((-1, 3))
        
        # 移除透明像素（如果有的话）
        data = data[~np.all(data == [0, 0, 0], axis=1)]
        
        total_pixels = len(data)
        
        # 使用K-means聚类
        n_colors = min(n_colors, len(data))
        if n_colors <= 0:
            n_colors = 1
            
        kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
        kmeans.fit(data)
        
        # 获取聚类中心（颜色）
        colors = kmeans.cluster_centers_.astype(int)
        
        # 计算每个颜色的像素数量
        labels = kmeans.labels_
        counts = np.bincount(labels)
        
        return colors, counts, total_pixels
        
    except Exception as e:
        # 如果K-means失败，返回基本颜色
        basic_colors = np.array([[128, 128, 128], [255, 255, 255], [0, 0, 0]])
        basic_counts = np.array([100, 100, 100])
        return basic_colors, basic_counts, 300

def classify_color_science(rgb_color):
    """Color Science颜色分类"""
    try:
        r, g, b = rgb_color
        
        # 转换为HSV色彩空间进行分类
        h, s, v = rgb_to_hsv(r, g, b)
        
        # 判断黑白灰
        if v < 0.2:  # 很暗
            return "black"
        elif v > 0.8 and s < 0.1:  # 很亮且饱和度低
            return "white"
        elif s < 0.15:  # 饱和度很低
            return "gray"
        
        # 基于色相分类彩色
        hue_deg = h * 360
        if 0 <= hue_deg < 15 or 345 <= hue_deg <= 360:
            return "red"
        elif 15 <= hue_deg < 45:
            return "orange"
        elif 45 <= hue_deg < 75:
            return "yellow"
        elif 75 <= hue_deg < 165:
            return "green"
        elif 165 <= hue_deg < 195:
            return "cyan"
        elif 195 <= hue_deg < 255:
            return "blue"
        elif 255 <= hue_deg < 285:
            return "purple"
        elif 285 <= hue_deg < 315:
            return "magenta"
        elif 315 <= hue_deg < 345:
            return "pink"
        else:
            return "unknown"
            
    except Exception:
        # 简单的RGB分类作为备选
        r, g, b = rgb_color
        if r > 200 and g < 100 and b < 100:
            return "red"
        elif r < 100 and g > 200 and b < 100:
            return "green"
        elif r < 100 and g < 100 and b > 200:
            return "blue"
        elif r > 200 and g > 200 and b < 100:
            return "yellow"
        elif r < 50 and g < 50 and b < 50:
            return "black"
        elif r > 200 and g > 200 and b > 200:
            return "white"
        else:
            return "gray"

def apply_color_science_processing(color_info_list, target_count, params):
    """应用Color Science颜色处理逻辑"""
    # 分离黑白灰和其他颜色
    bwg_colors = []  # black, white, gray
    non_bwg_colors = []

    for color in color_info_list:
        label = color["label"].lower()
        if label in ["black", "white", "gray", "grey"]:
            bwg_colors.append(color)
        else:
            non_bwg_colors.append(color)

    # 决定使用哪些颜色
    if non_bwg_colors:
        # 存在其他颜色，仅使用非黑白灰颜色
        working_colors = non_bwg_colors
    else:
        # 仅包含黑白灰，使用预设颜色
        dominant_type = determine_dominant_bwg_type(bwg_colors)
        return get_fixed_bwg_colors(dominant_type, target_count)

    # 去重（基于标签）
    seen_labels = set()
    unique_colors = []
    for color in working_colors:
        label = color["label"]
        if label not in seen_labels:
            seen_labels.add(label)
            unique_colors.append(color)

    # 应用颜色增强
    enhanced_colors = []
    for color in unique_colors:
        enhanced_color = apply_color_enhancement(color, params)
        enhanced_colors.append(enhanced_color)

    # 确保有足够的颜色
    final_colors = ensure_enough_colors(enhanced_colors, target_count)

    return final_colors

def apply_color_enhancement(color, params):
    """应用颜色增强"""
    enhanced_color = color.copy()
    
    r, g, b = color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    # 应用鲜艳度调节（如果启用）
    if params.get("vivid_enhancement", True):
        # 亮度调整
        if v < 0.45:
            v = min(v * 1.25, 0.60)
        elif v < 0.65:
            v = min(v * 1.15, 0.75)
        
        # 饱和度调整
        if s < 0.35:
            s = min(s * 1.40, 0.55)
        elif s < 0.70:
            s = min(s * 1.25, 0.85)
        else:
            s = min(s * 1.10, 1.00)
    
    # 应用打印补偿（如果启用）
    if params.get("print_compensation", True):
        preset = params.get("print_preset", "mid_cotton")
        if preset == "mid_cotton":
            # 中磅纯棉预设的补偿
            s = min(s * 1.1, 1.0)
            v = min(v * 1.05, 1.0)
    
    # 应用用户自定义参数
    saturation_adj = params.get("saturation_adjustment", 0) / 100.0
    lightness_adj = params.get("lightness_adjustment", 0) / 100.0
    
    s = max(0, min(1, s + saturation_adj))
    v = max(0, min(1, v + lightness_adj))
    
    # 转换回RGB
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    enhanced_color["rgb"] = (new_r, new_g, new_b)
    enhanced_color["hex"] = new_hex
    enhanced_color["color_code"] = new_hex
    
    return enhanced_color

def determine_dominant_bwg_type(bwg_colors):
    """判定黑白灰主导类型"""
    if not bwg_colors:
        return "gray"
    
    # 按占比排序，取最大的
    bwg_colors.sort(key=lambda x: x["percentage"], reverse=True)
    return bwg_colors[0]["label"].lower()

def get_fixed_bwg_colors(dominant_type, target_count):
    """获取固定的黑白灰预设颜色"""
    presets = {
        "black": ["#404040", "#353535", "#4a4a4a", "#3f3f3f", "#2a2a2a"],
        "white": ["#f4f4f4", "#dfdfdf", "#e9e9e9", "#d4d4d4", "#b7b7b7"],
        "gray": ["#dedede", "#b1b1b1", "#cdcdcd", "#959595", "#747474"]
    }
    
    colors = presets.get(dominant_type, presets["gray"])
    result = []
    
    for i, hex_color in enumerate(colors[:target_count]):
        rgb = hex_to_rgb(hex_color)
        result.append({
            "color_code": hex_color,
            "hex": hex_color,
            "rgb": rgb,
            "percentage": 100.0 / target_count,
            "label": dominant_type,
            "colour_science": dominant_type
        })
    
    return result

def ensure_enough_colors(colors, target_count):
    """确保有足够的颜色"""
    if len(colors) >= target_count:
        return colors[:target_count]
    
    # 如果颜色不够，生成变体
    result = colors.copy()
    
    while len(result) < target_count:
        # 基于现有颜色生成变体
        base_color = result[len(result) % len(colors)]
        variant = generate_color_variant(base_color, len(result))
        result.append(variant)
    
    return result[:target_count]

def generate_color_variant(base_color, variant_index):
    """生成颜色变体"""
    r, g, b = base_color["rgb"]
    h, s, v = rgb_to_hsv(r, g, b)
    
    # 根据变体索引调整颜色
    if variant_index % 4 == 1:
        # 变体1：提高亮度
        v = min(v * 1.1, 1.0)
    elif variant_index % 4 == 2:
        # 变体2：降低饱和度
        s = max(s * 0.8, 0.0)
    elif variant_index % 4 == 3:
        # 变体3：调整色相
        h = (h + 0.1) % 1.0
    
    new_r, new_g, new_b = hsv_to_rgb(h, s, v)
    new_hex = f"#{new_r:02x}{new_g:02x}{new_b:02x}"
    
    variant = base_color.copy()
    variant["rgb"] = (new_r, new_g, new_b)
    variant["hex"] = new_hex
    variant["color_code"] = new_hex
    variant["percentage"] = base_color["percentage"] * 0.5
    
    return variant

def rgb_to_hsv(r, g, b):
    """RGB转HSV"""
    r, g, b = r/255.0, g/255.0, b/255.0
    max_val = max(r, g, b)
    min_val = min(r, g, b)
    diff = max_val - min_val
    
    # 计算色相
    if diff == 0:
        h = 0
    elif max_val == r:
        h = (60 * ((g - b) / diff) + 360) % 360
    elif max_val == g:
        h = (60 * ((b - r) / diff) + 120) % 360
    elif max_val == b:
        h = (60 * ((r - g) / diff) + 240) % 360
    
    h = h / 360.0
    
    # 计算饱和度
    s = 0 if max_val == 0 else diff / max_val
    
    # 计算明度
    v = max_val
    
    return h, s, v

def hsv_to_rgb(h, s, v):
    """HSV转RGB"""
    h = h * 360
    c = v * s
    x = c * (1 - abs((h / 60) % 2 - 1))
    m = v - c
    
    if 0 <= h < 60:
        r, g, b = c, x, 0
    elif 60 <= h < 120:
        r, g, b = x, c, 0
    elif 120 <= h < 180:
        r, g, b = 0, c, x
    elif 180 <= h < 240:
        r, g, b = 0, x, c
    elif 240 <= h < 300:
        r, g, b = x, 0, c
    elif 300 <= h < 360:
        r, g, b = c, 0, x
    else:
        r, g, b = 0, 0, 0
    
    r = int((r + m) * 255)
    g = int((g + m) * 255)
    b = int((b + m) * 255)
    
    return r, g, b

def hex_to_rgb(hex_color):
    """十六进制转RGB"""
    hex_color = hex_color.lstrip("#")
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

if __name__ == "__main__":
    main()
';
    }
}