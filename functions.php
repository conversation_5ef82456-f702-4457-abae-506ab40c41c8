<?php
// Add custom Theme Functions here
add_action('wp_enqueue_scripts', 'printeefree_custom_enqueues',99);
function printeefree_custom_enqueues()
{
    // wp_enqueue_script('pf-font', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/js/all.min.js', array(), '1.1', true);
    wp_enqueue_style('pf-font', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css', array(), '1.1', 'all');
    wp_enqueue_style('pf-custom', get_stylesheet_directory_uri() . '/custom.css');
}
add_action( 'design-editor-header', 'printeefree_custom_enqueues_style',99);
function printeefree_custom_enqueues_style()

{
    wp_enqueue_style('pf-font', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css', array(), '1.1', 'all');
    wp_enqueue_style('pf-custom', get_stylesheet_directory_uri() . '/custom.css');
}

// 移动端产品页短描述位置调整
add_action( 'init', 'reposition_short_description_on_mobile' );
function reposition_short_description_on_mobile() {
    // 只在移动端调整
    if ( wp_is_mobile() ) {
        // 移除默认位置的短描述输出
        remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20 );

        // 在产品摘要下方、产品描述Tab上方重新加入短描述
        add_action( 'woocommerce_after_single_product_summary', 'my_mobile_short_description_wrapper', 9 );
    }
}

// 包裹短描述输出，使其在移动端可控定位和样式
function my_mobile_short_description_wrapper() {
    echo '<div class="mobile-short-description">';
    woocommerce_template_single_excerpt();
    echo '</div>';
}

// 为 Quick View 界面和移动端短描述添加对应样式及隐藏多余元素
add_action('wp_head', 'fix_quickview_description_styles');
function fix_quickview_description_styles() {
    ?>
    <style>
        /* ============ 产品详情页与 Quick View 通用样式 ============ */
        .mobile-short-description {
            background-color: #f8f8f8;
            border-radius: 4px;
            margin-top: 20px;
        }

        /* 桌面端时隐藏移动端的短描述容器 */
        @media screen and (min-width: 850px) {
            .mobile-short-description {
                display: none;
            }
        }

        /* ============ Quick View 弹窗相关样式调整 ============ */
        .product-quick-view-container .mobile-short-description {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            width: 100%;
            clear: both;
        }

        /* 移除 Quick View 中的加载圈 */
        .product-quick-view-container .mobile-short-description .loading-spin,
        .product-quick-view-container .product-short-description .loading-spin {
            display: none !important;
        }

        /* 隐藏 Quick View 中出现的 default.svg 占位符 */
        .product-quick-view-container img[src*="wp-includes/images/media/default.svg"] {
            display: none !important;
        }

        /* 隐藏 Quick View 弹窗中的切换箭头 (原先的UX Swiper) */
        .product-quick-view-container .ux-swiper-nav {
            display: none !important;
        }

        /* 隐藏 Flickity 导航按钮（左右滑动的箭头）*/
        .product-quick-view-container .flickity-prev-next-button {
            display: none !important;
        }

        /* 确保 Quick View 中的短描述与详情页类似 */
        .product-quick-view-container .product-short-description {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            width: 100%;
            clear: both;
        }
    </style>
    <?php
}

// 确保 Quick View 中的 UX Block 正确加载（如您的主题/插件需要）
add_action('wp_enqueue_scripts', 'ensure_quickview_ux_blocks');
function ensure_quickview_ux_blocks() {
    if (!is_admin()) {
        wp_enqueue_script('flatsome-ux-blocks', get_template_directory_uri() . '/assets/js/ux-blocks.js', array('jquery'), null, true);
    }
}

// 添加运输方式过滤函数
add_filter('woocommerce_package_rates', 'custom_shipping_method_filter', 10, 2);
function custom_shipping_method_filter($rates, $package) {
    // 添加调试信息（可选）
    //error_log('可用的运输方式: ' . print_r($rates, true));

    // 获取当前选择的国家
    $customer_country = WC()->customer->get_shipping_country();

    // 定义运输方式ID对应关系
    $shipping_pairs = array(
        // 美国地区 (US)
        'US' => array(
            'free_shipping:5' => 'flat_rate:8',  // Standard Free shipping 对应 Standard Shipping
            'free_shipping:9' => 'flat_rate:6',  // Express Free shipping 对应 Express Shipping
            'free_shipping:10' => 'flat_rate:6'  // Express Free shipping 对应 Express Shipping
        ),
        // 其他国家地区
        'default' => array(
            'free_shipping:4' => 'flat_rate:3',  // Free shipping 对应 Standard Shipping
        )
    );

    // 确定使用哪个配置
    $current_pairs = isset($shipping_pairs[$customer_country])
        ? $shipping_pairs[$customer_country]
        : $shipping_pairs['default'];

    // 如果从美国切换到其他国家，需要特殊处理（移除Express相关方式）
    if (!isset($shipping_pairs[$customer_country])) {
        foreach ($rates as $rate_id => $rate) {
            if (strpos($rate_id, 'flat_rate:6') !== false ||
                strpos($rate_id, 'free_shipping:9') !== false ||
                strpos($rate_id, 'free_shipping:10') !== false) {
                unset($rates[$rate_id]);
            }
        }
    }

    // 应用运输方式过滤
    foreach ($current_pairs as $free_shipping_id => $normal_shipping_id) {
        if (isset($rates[$free_shipping_id])) {
            if (isset($rates[$normal_shipping_id])) {
                unset($rates[$normal_shipping_id]);
            }
        }
    }

    // 确保至少有一个运输方式可用（如果没有可用的运输方式，这里可根据需要添加逻辑）
    if (empty($rates)) {
        // 如果需要，可默认添加标准运输方式。
        // 这里不做操作是因为一般Woo会有默认的处理方式，
        // 或者您可以根据需求添加默认方式。
    }

    return $rates;
}

// 添加AJAX处理程序，用于当地址更新时重新计算运费（主要用于checkout页面）
add_action('wp_ajax_update_checkout_shipping', 'handle_checkout_shipping_update');
add_action('wp_ajax_nopriv_update_checkout_shipping', 'handle_checkout_shipping_update');
function handle_checkout_shipping_update() {
    WC()->cart->calculate_shipping();
    WC()->cart->calculate_totals();
    wp_die();
}

// 在前端添加JS逻辑，同时支持checkout和cart页面
add_action('wp_footer', 'add_frontend_shipping_script');
function add_frontend_shipping_script() {
    if (is_checkout() || is_cart()) {
        ?>
        <script type="text/javascript">
        jQuery(function($) {
            var countrySelectors = '#shipping_country, #billing_country';
            var selectedShippingMethod = '';

            // 当国家变更时，清除当前选择的运输方式，然后强制更新
            $(document.body).on('change', countrySelectors, function() {
                $('input[name^="shipping_method"]').prop('checked', false);
                selectedShippingMethod = ''; // 重置选中的运输方式

                // 如果是结账页面
                if ($('form.checkout').length) {
                    $(document.body).trigger('update_checkout');
                }
                // 如果是购物车页面
                else if ($('form.woocommerce-cart-form').length) {
                    $('button[name="update_cart"]').prop('disabled', false);
                    $('button[name="update_cart"]').trigger('click');
                }
            });

            // 当运输方式变更时的处理
            $(document.body).on('change', 'input[name^="shipping_method"]', function() {
                selectedShippingMethod = $(this).val(); // 保存用户选择的运输方式

                if ($('form.checkout').length) {
                    $(document.body).trigger('update_checkout');
                } else if ($('form.woocommerce-cart-form').length) {
                    // 使用 AJAX 更新购物车，而不是触发更新按钮
                    $.ajax({
                        type: 'POST',
                        url: wc_cart_params.ajax_url,
                        data: {
                            action: 'update_shipping_method',
                            shipping_method: selectedShippingMethod,
                            security: wc_cart_params.update_shipping_method_nonce
                        },
                        success: function(response) {
                            // 触发购物车更新，但保持选择的运输方式
                            $('button[name="update_cart"]').prop('disabled', false);
                            $('button[name="update_cart"]').trigger('click');
                        }
                    });
                }
            });

            // 在购物车更新完成后，恢复之前选择的运输方式
            $(document.body).on('updated_cart_totals', function() {
                if (selectedShippingMethod) {
                    $('input[name^="shipping_method"][value="' + selectedShippingMethod + '"]').prop('checked', true);
                }
            });
        });
        </script>
        <?php
    }
}

// 添加处理运输方式更新的AJAX操作
add_action('wp_ajax_update_shipping_method', 'handle_update_shipping_method');
add_action('wp_ajax_nopriv_update_shipping_method', 'handle_update_shipping_method');
function handle_update_shipping_method() {
    if (!isset($_POST['shipping_method']) || !isset($_POST['security'])) {
        wp_send_json_error('Invalid request');
        return;
    }

    if (!wp_verify_nonce($_POST['security'], 'update-shipping-method')) {
        wp_send_json_error('Invalid security token');
        return;
    }

    $chosen_shipping_methods = WC()->session->get('chosen_shipping_methods');
    $chosen_shipping_methods[0] = wc_clean($_POST['shipping_method']);
    WC()->session->set('chosen_shipping_methods', $chosen_shipping_methods);

    WC()->cart->calculate_shipping();
    WC()->cart->calculate_totals();

    wp_send_json_success();
}

// 确保在checkout时，地址或运费方式更新后强制重新计算
add_action('woocommerce_checkout_update_order_review', 'force_shipping_recalculation');
function force_shipping_recalculation($post_data) {
    WC()->cart->calculate_shipping();
}

//lumise close the allow customize by DEFAULT
add_filter('default_post_metadata', function($value, $post_id, $meta_key, $single) {
    if ($meta_key === 'lumise_customize') {
        return 'no';
    }
    return $value;
}, 10, 4);
//Recaptcha
function add_recaptcha_api() {
    echo '<script src="https://www.google.com/recaptcha/api.js" async defer></script>';
}
add_action('wp_head', 'add_recaptcha_api', 10);

// 添加自定义 CSS 来隐藏 reCAPTCHA 徽章
function hide_recaptcha_badge() {
    echo '<style>
        .grecaptcha-badge {
            visibility: hidden;
        }
    </style>';
}
add_action('wp_head', 'hide_recaptcha_badge');
//隐藏console错误显示：GET https://matchbeast.com/wp-content/themes/flatsome/assets/js/ux-blocks.js net::ERR_ABORTED 404 (Not Found)
//add_action('wp_enqueue_scripts', function() {
//    wp_deregister_script('flatsome-ux-blocks');
//}, 100);

// ==================== 智能产品类目性能优化 ====================
// 优化后台产品管理页面，解决35000+类目导致的加载缓慢问题
// 通过安全的CSS隐藏方式优化，不影响产品数据完整性

// 1. 智能阻止产品列表页面加载类目数据
add_filter('manage_product_posts_columns', 'smart_remove_product_category_column');
function smart_remove_product_category_column($columns) {
    // 移除产品类目列，减少查询负担
    unset($columns['product_cat']);
    error_log('产品类目优化: 已移除产品列表页面的类目列显示');
    return $columns;
}

// 1.1 智能阻止产品列表页面的类目相关操作
add_action('init', 'smart_disable_category_operations_in_admin', 1);
function smart_disable_category_operations_in_admin() {
    global $pagenow;

    // 只在产品列表页面（edit.php）禁用，不影响产品类目管理页面（edit-tags.php）
    if (is_admin() && $pagenow === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        // 【彻底修复】仅通过CSS隐藏产品类目相关元素，不修改任何查询或钩子
        // 这样可以确保产品数据完整性，同时提升页面加载速度
        add_action('admin_head', 'hide_category_elements_css_only');

        error_log('产品类目优化: 已精确禁用产品列表页面的类目操作，保护其他产品数据');
    }
}

// 【新增】仅通过CSS隐藏产品类目相关元素的安全函数
function hide_category_elements_css_only() {
    global $pagenow, $post_type;

    if ($pagenow === 'edit.php' && $post_type === 'product') {
        echo '<style>
            /* 仅隐藏产品类目相关的UI元素，不影响数据查询 */
            .tablenav .actions select[name="product_cat"] { display: none !important; }
            .subsubsub .product_cat { display: none !important; }

            /* 隐藏产品列表中的类目列 */
            .wp-list-table .column-product_cat { display: none !important; }
            .wp-list-table .manage-column.column-product_cat { display: none !important; }

            /* 保留所有其他产品相关的列和过滤器 */
            .wp-list-table .column-product_type { display: table-cell !important; }
            .wp-list-table .column-product_tag { display: table-cell !important; }
            .wp-list-table .column-featured { display: table-cell !important; }
            .wp-list-table .column-product_status { display: table-cell !important; }

            /* 确保产品属性和变体相关元素正常显示 */
            .tablenav .actions select[name="product_type"] { display: inline-block !important; }
            .tablenav .actions select[name="stock_status"] { display: inline-block !important; }
        </style>';

        error_log('产品类目优化: 已通过CSS安全隐藏产品类目元素，保护所有产品数据');
    }
}

// 【暂时禁用】智能优化类目计数查询，避免影响产品数据
function smart_optimize_category_count_only($count, $taxonomy, $args) {
    // 暂时不进行任何优化，确保产品数据完整性
    return $count;
}

// 【彻底修复】完全禁用get_terms过滤器，确保产品数据完整性
function smart_block_category_terms_in_product_list($terms, $taxonomies, $args) {
    // 暂时不进行任何过滤，确保所有产品数据正常显示
    return $terms;
}

// 1.2 智能阻止产品列表页面的类目数据查询
add_action('pre_get_posts', 'smart_disable_category_queries_in_product_list');
function smart_disable_category_queries_in_product_list($query) {
    // 只在后台产品列表页面执行
    if (is_admin() && $query->is_main_query() &&
        isset($_GET['post_type']) && $_GET['post_type'] === 'product') {

        // 【彻底修复】暂时禁用pre_get_posts优化，避免影响产品数据查询
        // 仅通过CSS和UI层面进行优化，不修改查询逻辑
        add_action('admin_head', 'hide_category_filter_only');

        error_log('产品类目优化: 已精确禁用产品列表页面的类目查询，保护其他产品数据');
    }
}

// 1.3 智能移除产品列表页面的类目过滤器
add_action('admin_head', 'smart_remove_product_category_filter');
function smart_remove_product_category_filter() {
    global $pagenow, $post_type;

    if ($pagenow === 'edit.php' && $post_type === 'product') {
        // 移除类目过滤下拉框
        add_filter('disable_months_dropdown', '__return_true');

        // 隐藏类目过滤器
        echo '<style>
            .tablenav .actions select[name="product_cat"] { display: none !important; }
            .subsubsub .product_cat { display: none !important; }
        </style>';

        error_log('产品类目优化: 已隐藏产品列表页面的类目过滤器');
    }
}

// 1.4 智能优化产品列表页面的数据库查询
add_filter('posts_clauses', 'smart_optimize_product_list_queries', 10, 2);
function smart_optimize_product_list_queries($clauses, $query) {
    global $pagenow, $post_type;

    // 只在后台产品列表页面执行
    if (is_admin() && $pagenow === 'edit.php' &&
        isset($_GET['post_type']) && $_GET['post_type'] === 'product' &&
        $query->is_main_query()) {

        // 【彻底修复】暂时禁用JOIN优化，避免影响产品属性和变体数据
        // 仅通过其他方式优化产品类目，不修改数据库查询结构
        error_log('产品类目优化: 保持原始JOIN语句，仅通过其他方式优化产品类目显示');

        error_log('产品类目优化: 优化后的JOIN语句 - ' . $clauses['join']);
    }

    return $clauses;
}

// 1.5 智能禁用WooCommerce产品列表页面的类目相关功能
add_action('admin_init', 'smart_disable_woocommerce_category_features');
function smart_disable_woocommerce_category_features() {
    global $pagenow;

    // 只在产品列表页面（edit.php）禁用，不影响产品类目管理页面（edit-tags.php）
    if (is_admin() && $pagenow === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        // 【修复】仅移除产品类目相关的过滤器，保留其他产品功能
        // 移除WooCommerce的产品类目过滤器
        remove_action('restrict_manage_posts', array('WC_Admin_Post_Types', 'product_filters'));
        remove_action('restrict_manage_posts', 'wc_product_dropdown_categories');

        // 【修复】不禁用整个admin bar，仅针对类目功能进行优化
        // 通过CSS隐藏类目相关元素而不是完全禁用功能

        error_log('产品类目优化: 已精确禁用WooCommerce产品列表页面的类目功能，保护其他产品数据');
    }
}

// 【彻底修复】暂时禁用查询监控，避免影响任何产品数据查询
add_filter('query', 'smart_monitor_large_queries');
function smart_monitor_large_queries($query) {
    if (is_admin() && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        // 【修复】仅记录查询，不进行任何阻止操作，确保产品数据完整性
        if (strpos($query, 'product_cat') !== false) {
            error_log('产品类目优化: 检测到产品类目相关查询 - ' . substr($query, 0, 200) . '...');
            // 不再阻止任何查询，仅记录日志
        }
    }
    return $query; // 始终返回原始查询，不做任何修改
}

// 【新增】仅隐藏类目过滤器的CSS函数
function hide_category_filter_only() {
    global $pagenow, $post_type;

    if ($pagenow === 'edit.php' && $post_type === 'product') {
        echo '<style>
            /* 仅隐藏产品类目过滤器，保留其他过滤器 */
            .tablenav .actions select[name="product_cat"] { display: none !important; }
            .subsubsub .product_cat { display: none !important; }
            /* 保留产品类型、产品状态等其他过滤器的显示 */
        </style>';

        error_log('产品类目优化: 已通过CSS隐藏产品类目过滤器，保留其他产品过滤器');
    }
}

// 【新增】添加调试日志记录关键错误
add_action('wp_loaded', 'log_product_optimization_status');
function log_product_optimization_status() {
    if (is_admin() && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        error_log('产品类目优化: 产品列表页面加载，优化状态检查');
        error_log('产品类目优化: 当前页面 - ' . (isset($_GET['page']) ? $_GET['page'] : 'edit.php'));

        // 检查Redis缓存状态
        if (class_exists('Redis')) {
            try {
                $redis = new Redis();
                $redis->connect('127.0.0.1', 6379);
                $product_cache_keys = $redis->keys('*product*');
                error_log('产品类目优化: Redis中产品相关缓存键数量 - ' . count($product_cache_keys));
                $redis->close();
            } catch (Exception $e) {
                error_log('产品类目优化: Redis连接失败 - ' . $e->getMessage());
            }
        }
    }
}

// 【新增】清理可能被污染的Redis缓存
add_action('admin_init', 'clear_potentially_corrupted_product_cache');
function clear_potentially_corrupted_product_cache() {
    global $pagenow;

    // 只在产品列表页面执行一次清理
    if (is_admin() && $pagenow === 'edit.php' &&
        isset($_GET['post_type']) && $_GET['post_type'] === 'product' &&
        !get_transient('ss_product_cache_cleared')) {

        // 设置24小时的清理标记，避免频繁清理
        set_transient('ss_product_cache_cleared', true, 86400);

        // 清理可能被污染的产品缓存
        if (class_exists('Redis')) {
            try {
                $redis = new Redis();
                $redis->connect('127.0.0.1', 6379);

                // 清理产品相关的缓存键
                $product_keys = $redis->keys('*product*');
                $wc_keys = $redis->keys('*wc_*');
                $all_keys = array_merge($product_keys, $wc_keys);

                if (!empty($all_keys)) {
                    $redis->del($all_keys);
                    error_log('产品类目优化: 已清理 ' . count($all_keys) . ' 个可能被污染的产品缓存键');
                }

                $redis->close();
            } catch (Exception $e) {
                error_log('产品类目优化: 清理Redis缓存失败 - ' . $e->getMessage());
            }
        }

        // 清理WordPress对象缓存
        wp_cache_flush();
        error_log('产品类目优化: 已清理WordPress对象缓存，防止数据污染');
    }
}

// 2. 移除默认的产品类目元框，替换为搜索式加载
add_action('add_meta_boxes', 'remove_default_category_metabox', 999);
function remove_default_category_metabox() {
    // 移除默认的类目元框
    remove_meta_box('product_catdiv', 'product', 'side');
    error_log('产品类目优化: 已移除默认类目元框');
}

// 3. 添加自定义的AJAX搜索类目元框
add_action('add_meta_boxes', 'add_ajax_category_metabox');
function add_ajax_category_metabox() {
    add_meta_box(
        'ajax_product_categories',
        '产品类目 (搜索选择)',
        'ajax_category_metabox_callback',
        'product',
        'side',
        'default'
    );
    error_log('产品类目优化: 已添加AJAX搜索类目元框');
}

// 4. AJAX搜索类目元框的回调函数
function ajax_category_metabox_callback($post) {
    // 获取当前产品的类目
    $current_categories = wp_get_post_terms($post->ID, 'product_cat', array('fields' => 'ids'));
    $current_category_names = array();

    if (!empty($current_categories)) {
        $current_terms = wp_get_post_terms($post->ID, 'product_cat');
        foreach ($current_terms as $term) {
            $current_category_names[] = array(
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug
            );
        }
    }

    // 添加nonce字段
    wp_nonce_field('ajax_category_metabox', 'ajax_category_metabox_nonce');

    ?>
    <div id="ajax-category-search-container">
        <div class="ajax-category-search-wrapper">
            <input type="text"
                   id="category-search-input"
                   placeholder="搜索产品类目..."
                   autocomplete="off"
                   style="width: 100%; margin-bottom: 10px; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">

            <div id="category-search-results"
                 style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; display: none; background: white; position: relative; z-index: 999;">
            </div>

            <div id="selected-categories" style="margin-top: 10px;">
                <h4 style="margin: 10px 0 5px 0; font-size: 13px;">已选择的类目:</h4>
                <div id="selected-categories-list">
                    <?php if (!empty($current_category_names)): ?>
                        <?php foreach ($current_category_names as $category): ?>
                            <div class="selected-category-item" data-category-id="<?php echo esc_attr($category['id']); ?>"
                                 style="display: inline-block; background: #0073aa; color: white; padding: 4px 8px; margin: 2px; border-radius: 3px; font-size: 12px;">
                                <?php echo esc_html($category['name']); ?>
                                <span class="remove-category" style="margin-left: 5px; cursor: pointer; font-weight: bold;">&times;</span>
                                <input type="hidden" name="product_cat[]" value="<?php echo esc_attr($category['id']); ?>">
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p style="color: #666; font-style: italic; font-size: 12px;">暂未选择任何类目</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="margin-top: 10px; padding: 8px; background: #f9f9f9; border-radius: 4px; font-size: 11px; color: #666;">
                <strong>使用说明:</strong><br>
                • 在搜索框中输入类目名称进行搜索<br>
                • 点击搜索结果中的类目进行选择<br>
                • 点击已选类目旁的 × 可以移除<br>
                • 支持选择多个类目
            </div>


        </div>
    </div>

    <style>
        .category-search-result-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .category-search-result-item:hover {
            background-color: #f0f0f0;
        }
        .category-search-result-item:last-child {
            border-bottom: none;
        }
        .selected-category-item {
            position: relative;
        }
        .remove-category:hover {
            background-color: rgba(255,255,255,0.2);
            border-radius: 50%;
        }
        #category-search-results {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
    <?php
}

// 5. AJAX处理函数 - 搜索产品类目
add_action('wp_ajax_search_product_categories', 'ajax_search_product_categories');
function ajax_search_product_categories() {
    // 添加调试信息
    error_log('产品类目优化: 收到AJAX搜索请求');
    error_log('产品类目优化: POST数据 - ' . print_r($_POST, true));

    // 验证nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'category_search_nonce')) {
        error_log('产品类目优化: AJAX搜索请求nonce验证失败');
        wp_send_json_error('安全验证失败');
        return;
    }

    if (!isset($_POST['search_term'])) {
        error_log('产品类目优化: 搜索关键词未提供');
        wp_send_json_error('搜索关键词未提供');
        return;
    }

    $search_term = sanitize_text_field($_POST['search_term']);
    error_log('产品类目优化: 搜索类目关键词 - ' . $search_term);

    if (strlen($search_term) < 2) {
        wp_send_json_error('搜索关键词至少需要2个字符');
        return;
    }

    // 搜索产品类目，限制结果数量以提高性能
    $categories = get_terms(array(
        'taxonomy' => 'product_cat',
        'hide_empty' => false,
        'search' => $search_term,
        'number' => 20, // 限制返回20个结果
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    if (is_wp_error($categories)) {
        error_log('产品类目优化: 搜索类目时发生错误 - ' . $categories->get_error_message());
        wp_send_json_error('搜索时发生错误');
        return;
    }

    $results = array();
    foreach ($categories as $category) {
        $results[] = array(
            'id' => $category->term_id,
            'name' => $category->name,
            'slug' => $category->slug,
            'count' => $category->count
        );
    }

    error_log('产品类目优化: 找到 ' . count($results) . ' 个匹配的类目');
    wp_send_json_success($results);
}

// 6. 保存产品类目
add_action('save_post', 'save_ajax_product_categories');
function save_ajax_product_categories($post_id) {
    // 验证这是产品类型的文章
    if (get_post_type($post_id) !== 'product') {
        return;
    }

    // 【重要保护】检查是否为模板产品，如果是则跳过处理
    $is_template = get_post_meta($post_id, '_ss_is_template', true);
    if ($is_template === '1') {
        error_log('产品类目优化: 检测到模板产品 ID: ' . $post_id . '，跳过类目更新以保护模板产品');
        return;
    }

    // 验证nonce
    if (!isset($_POST['ajax_category_metabox_nonce']) ||
        !wp_verify_nonce($_POST['ajax_category_metabox_nonce'], 'ajax_category_metabox')) {
        return;
    }

    // 检查用户权限
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // 避免自动保存时执行
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // 获取选择的类目ID
    $selected_categories = isset($_POST['product_cat']) ? array_map('intval', $_POST['product_cat']) : array();

    // 设置产品类目
    wp_set_post_terms($post_id, $selected_categories, 'product_cat');

    error_log('产品类目优化: 产品 ' . $post_id . ' 的类目已更新，共 ' . count($selected_categories) . ' 个类目');
}

// 7. 在产品编辑页面加载JavaScript - 简化版本
add_action('admin_footer', 'enqueue_category_search_scripts_simple');
function enqueue_category_search_scripts_simple() {
    global $post_type, $pagenow;

    // 只在产品编辑页面加载
    if (($pagenow == 'post.php' || $pagenow == 'post-new.php') && $post_type == 'product') {
        error_log('产品类目优化: 简化版JavaScript开始加载 - pagenow: ' . $pagenow . ', post_type: ' . $post_type);

        // 直接输出JavaScript，不使用复杂的函数
        ?>
        <script type="text/javascript">
        console.log('产品类目优化: 简化版JavaScript开始执行');

        jQuery(document).ready(function($) {
            console.log('产品类目优化: jQuery ready - 开始查找元素');

            // 等待一下确保DOM完全加载
            setTimeout(function() {
                console.log('产品类目优化: 延迟执行开始');

                var $searchInput = $('#category-search-input');

                console.log('产品类目优化: 搜索输入框找到:', $searchInput.length);

                // 搜索功能
                if ($searchInput.length > 0) {
                    console.log('产品类目优化: 绑定搜索输入事件');

                    var searchTimeout;
                    var $searchResults = $('#category-search-results');
                    var $selectedList = $('#selected-categories-list');

                    $searchInput.off('input keyup paste').on('input keyup paste', function() {
                        console.log('产品类目优化: 搜索输入事件触发，值:', $(this).val());
                        var searchTerm = $(this).val().trim();

                        // 清除之前的定时器
                        clearTimeout(searchTimeout);

                        if (searchTerm.length < 2) {
                            $searchResults.hide().empty();
                            return;
                        }

                        console.log('产品类目优化: 准备搜索:', searchTerm);

                        // 延迟搜索，避免频繁请求
                        searchTimeout = setTimeout(function() {
                            performSearch(searchTerm);
                        }, 300);
                    });

                    // 执行搜索函数
                    function performSearch(searchTerm) {
                        console.log('产品类目优化: 开始搜索类目 - ' + searchTerm);

                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'search_product_categories',
                                search_term: searchTerm,
                                nonce: '<?php echo wp_create_nonce('category_search_nonce'); ?>'
                            },
                            beforeSend: function() {
                                console.log('产品类目优化: 发送搜索AJAX请求');
                                $searchResults.html('<div style="padding: 10px; text-align: center; color: #666;">搜索中...</div>').show();
                            },
                            success: function(response) {
                                console.log('产品类目优化: 搜索AJAX响应', response);
                                if (response && response.success) {
                                    displaySearchResults(response.data);
                                    console.log('产品类目优化: 搜索成功，找到 ' + response.data.length + ' 个结果');
                                } else {
                                    var errorMsg = response && response.data ? response.data : '搜索失败';
                                    $searchResults.html('<div style="padding: 10px; color: #d63638;">' + errorMsg + '</div>');
                                    console.log('产品类目优化: 搜索失败 - ' + errorMsg);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('产品类目优化: 搜索AJAX请求失败', {xhr: xhr, status: status, error: error});
                                $searchResults.html('<div style="padding: 10px; color: #d63638;">搜索请求失败，请重试<br>错误: ' + error + '</div>');
                            }
                        });
                    }

                    // 显示搜索结果
                    function displaySearchResults(categories) {
                        var html = '';

                        if (categories.length === 0) {
                            html = '<div style="padding: 10px; color: #666; text-align: center;">未找到匹配的类目</div>';
                        } else {
                            $.each(categories, function(index, category) {
                                // 检查是否已经选择
                                var isSelected = $selectedList.find('[data-category-id="' + category.id + '"]').length > 0;
                                var disabledClass = isSelected ? ' style="opacity: 0.5; cursor: not-allowed;"' : '';
                                var disabledText = isSelected ? ' (已选择)' : '';

                                html += '<div class="category-search-result-item"' + disabledClass + ' data-category-id="' + category.id + '" data-category-name="' + category.name + '">';
                                html += category.name + ' (' + category.count + ' 个产品)' + disabledText;
                                html += '</div>';
                            });
                        }

                        $searchResults.html(html).show();
                    }

                    // 点击搜索结果添加类目
                    $(document).off('click', '.category-search-result-item').on('click', '.category-search-result-item', function() {
                        var $this = $(this);
                        var categoryId = $this.data('category-id');
                        var categoryName = $this.data('category-name');

                        // 检查是否已经选择
                        if ($selectedList.find('[data-category-id="' + categoryId + '"]').length > 0) {
                            console.log('产品类目优化: 类目已选择，跳过 - ' + categoryName);
                            return;
                        }

                        addSelectedCategory(categoryId, categoryName);
                        $searchInput.val('').focus();
                        $searchResults.hide();

                        console.log('产品类目优化: 添加类目 - ' + categoryName + ' (ID: ' + categoryId + ')');
                    });

                    // 添加选中的类目
                    function addSelectedCategory(categoryId, categoryName) {
                        // 移除"暂未选择"提示
                        $selectedList.find('p').remove();

                        var categoryHtml = '<div class="selected-category-item" data-category-id="' + categoryId + '" ' +
                            'style="display: inline-block; background: #0073aa; color: white; padding: 4px 8px; margin: 2px; border-radius: 3px; font-size: 12px;">' +
                            categoryName +
                            '<span class="remove-category" style="margin-left: 5px; cursor: pointer; font-weight: bold;">&times;</span>' +
                            '<input type="hidden" name="product_cat[]" value="' + categoryId + '">' +
                            '</div>';

                        $selectedList.append(categoryHtml);
                    }

                    // 移除选中的类目
                    $(document).off('click', '.remove-category').on('click', '.remove-category', function() {
                        var $categoryItem = $(this).closest('.selected-category-item');
                        var categoryName = $categoryItem.text().replace('×', '').trim();

                        $categoryItem.remove();

                        // 如果没有选中的类目，显示提示
                        if ($selectedList.find('.selected-category-item').length === 0) {
                            $selectedList.html('<p style="color: #666; font-style: italic; font-size: 12px;">暂未选择任何类目</p>');
                        }

                        console.log('产品类目优化: 移除类目 - ' + categoryName);
                    });

                    // 点击其他地方隐藏搜索结果
                    $(document).off('click.categorySearch').on('click.categorySearch', function(e) {
                        if (!$(e.target).closest('#ajax-category-search-container').length) {
                            $searchResults.hide();
                        }
                    });

                } else {
                    console.error('产品类目优化: 搜索输入框未找到');
                }

            }, 1000); // 延迟1秒执行
        });
        </script>
        <?php

        error_log('产品类目优化: 简化版JavaScript已输出');
    }
}




?>