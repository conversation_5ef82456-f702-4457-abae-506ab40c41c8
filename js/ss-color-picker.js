jQuery(document).ready(function($){
    $('.ss-color-field').wpColorPicker();

    console.log('ss_admin_params:', ss_admin_params);

    // 检测分类图片的变化
    $('#thumbnail_id, #product_cat_thumbnail_id').on('change', function(){
        var attachmentId = $(this).val();
        console.log('Attachment ID:', attachmentId);

        if (attachmentId) {
            // 发送 AJAX 请求，获取提取的颜色
            $.ajax({
                url: ss_admin_params.ajaxurl,
                type: 'POST',
                data: {
                    action: 'ss_get_extracted_colors',
                    attachment_id: attachmentId,
                    nonce: ss_admin_params.nonce
                },
                success: function(response) {
                    console.log('AJAX Response:', response);
                    if (response.success) {
                        var colors = response.data.colors;
                        var html = '<div class="ss-extracted-colors">';
                        $.each(colors, function(index, color){
                            html += '<div class="ss-extracted-color-item">';
                            html += '<div class="ss-extracted-color-circle" style="background-color: '+color+';"></div>';
                            html += '<div class="ss-extracted-color-code">'+color+'</div>';
                            html += '</div>';
                        });
                        html += '</div>';
                        html += '<p class="description">' + ss_admin_params.extracted_colors_description + '</p>';
                        $('#ss-extracted-colors').html(html);
                    } else {
                        var errorMessage = response.data && response.data.message ? response.data.message : '未知错误';
                        $('#ss-extracted-colors').html('<p>' + errorMessage + '</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', error);
                    $('#ss-extracted-colors').html('<p>' + error + '</p>');
                }
            });
        } else {
            // 如果没有选择图片，显示提示
            $('#ss-extracted-colors').html('<p>' + ss_admin_params.upload_image_notice + '</p>');
        }
    });

    // 触发一次 change 事件，以在页面加载时获取颜色
    $('#thumbnail_id, #product_cat_thumbnail_id').trigger('change');
});