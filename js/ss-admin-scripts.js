jQuery(document).ready(function($){

    // 图片上传
    var frame;
    $('#ss_upload_template_image').on('click', function(e){
        e.preventDefault();

        if ( frame ) {
            frame.open();
            return;
        }

        frame = wp.media({
            title: ss_admin_params.selectImage,
            button: {
                text: ss_admin_params.selectImage
            },
            library: {
                type: ['image/webp', 'image/jpeg', 'image/png']
            },
            multiple: false
        });

        frame.on('select', function() {
            var attachment = frame.state().get('selection').first().toJSON();
            $('#ss_template_image_id').val(attachment.id);

            // 使用新的HTML结构和样式，包含与Gallery模板一致的选框容器
            var imageHtml = '<div class="main-template-image-container" style="position: relative; display: inline-block;">' +
                           '<img src="' + attachment.url + '" id="ss_selectable_image" class="main-template-image" style="max-width: 500px; height: auto;">' +
                           // SVG区域选框
                           '<div id="ss_crop_overlay_svg" class="main-template-area-overlay svg-area" style="position: absolute; border: 2px dashed red; background-color: rgba(255,0,0,0.1); display: none; z-index: 10;">' +
                               '<div class="main-template-resize-handle nw" data-direction="nw" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle n" data-direction="n" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle ne" data-direction="ne" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle e" data-direction="e" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle se" data-direction="se" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle s" data-direction="s" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle sw" data-direction="sw" data-area="svg"></div>' +
                               '<div class="main-template-resize-handle w" data-direction="w" data-area="svg"></div>' +
                               '<div class="main-template-move-handle" data-area="svg"></div>' +
                           '</div>' +
                           // 鞋子区域选框
                           '<div id="ss_crop_overlay_shoe" class="main-template-area-overlay shoe-area" style="position: absolute; border: 2px dashed blue; background-color: rgba(0,0,255,0.1); display: none; z-index: 10;">' +
                               '<div class="main-template-resize-handle nw" data-direction="nw" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle n" data-direction="n" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle ne" data-direction="ne" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle e" data-direction="e" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle se" data-direction="se" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle s" data-direction="s" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle sw" data-direction="sw" data-area="shoe"></div>' +
                               '<div class="main-template-resize-handle w" data-direction="w" data-area="shoe"></div>' +
                               '<div class="main-template-move-handle" data-area="shoe"></div>' +
                           '</div>' +
                           '</div>';
            $('#ss_template_image_preview').html(imageHtml);

            // 等待图片加载完成后初始化主图模板选框系统
            $('#ss_selectable_image').on('load', function() {
                initMainTemplateSelection();
                // 重置缩放级别
                if (typeof mainTemplateZoomLevel !== 'undefined') {
                    mainTemplateZoomLevel = 1;
                }
            });
        });

        frame.open();
    });

    // 移除图片
    $('#ss_remove_template_image').on('click', function(e){
        e.preventDefault();

        // 重置主图模板选框状态
        mainTemplateSelectionState.selecting = false;
        mainTemplateSelectionState.dragging = false;
        mainTemplateSelectionState.currentAreaType = '';

        // 解绑事件
        $(document).off('mousemove.mainSelection mouseup.mainSelection');
        $('body').removeClass('main-template-selecting');

        // 清空表单和预览
        $('#ss_template_image_id').val('');
        $('#ss_template_image_preview').html('');

        // 清空输入框
        $('#ss_svg_area_x, #ss_svg_area_y, #ss_svg_area_width, #ss_svg_area_height').val('');
        $('#ss_shoe_area_x, #ss_shoe_area_y, #ss_shoe_area_width, #ss_shoe_area_height').val('');

        console.log('🗑️ 主图模板图片已移除，状态已重置');
    });

    // 主图模板选框状态管理
    var mainTemplateSelectionState = {
        selecting: false,
        dragging: false,
        currentAreaType: '' // 'svg' or 'shoe'
    };

    // 初始化主图模板选框系统（替代 Cropper.js）
    function initMainTemplateSelection() {
        var image = document.getElementById('ss_selectable_image');
        if (!image) {
            console.log('主图模板图片元素不存在');
            return;
        }

        console.log('🔧 初始化主图模板选框系统...');

        // 读取之前保存的区域数据并显示选框
        var svgData = {
            x: parseInt($('#ss_svg_area_x').val()) || 0,
            y: parseInt($('#ss_svg_area_y').val()) || 0,
            width: parseInt($('#ss_svg_area_width').val()) || 0,
            height: parseInt($('#ss_svg_area_height').val()) || 0
        };
        var shoeData = {
            x: parseInt($('#ss_shoe_area_x').val()) || 0,
            y: parseInt($('#ss_shoe_area_y').val()) || 0,
            width: parseInt($('#ss_shoe_area_width').val()) || 0,
            height: parseInt($('#ss_shoe_area_height').val()) || 0
        };

        // 显示已保存的选框
        if (svgData.width && svgData.height) {
            updateMainTemplateOverlay('svg', svgData);
        }
        if (shoeData.width && shoeData.height) {
            updateMainTemplateOverlay('shoe', shoeData);
        }

        // 绑定按钮事件
        $('#ss_select_svg_area').off('click').on('click', function(){
            console.log('🎯 开始选择SVG区域');
            startMainTemplateSelection('svg');
        });

        $('#ss_select_shoe_area').off('click').on('click', function(){
            console.log('🎯 开始选择鞋子区域');
            startMainTemplateSelection('shoe');
        });

        $('#ss_save_area').off('click').on('click', function(){
            console.log('💾 保存主图模板区域设置');
            endMainTemplateSelection();
            alert(ss_admin_params.areaSaved);
        });

        // 主图模板缩放功能
        $('#ss_zoom_in_main').off('click').on('click', function(){
            console.log('主图模板放大按钮被点击');
            zoomMainTemplate(1.2);
        });

        $('#ss_zoom_out_main').off('click').on('click', function(){
            console.log('主图模板缩小按钮被点击');
            zoomMainTemplate(0.8);
        });

        $('#ss_reset_zoom_main').off('click').on('click', function(){
            console.log('主图模板重置缩放按钮被点击');
            resetMainTemplateZoom();
        });

        // 绑定主图模板选框事件
        bindMainTemplateSelectionEvents();

        console.log('✅ 主图模板选框系统初始化完成');
    }

    // 绑定主图模板选框事件
    function bindMainTemplateSelectionEvents() {
        console.log('🔗 绑定主图模板选框事件');

        // 主图模板图片鼠标事件
        $('#ss_selectable_image').off('mousedown.mainSelection').on('mousedown.mainSelection', function(e) {
            console.log('主图模板图片mousedown事件，选择状态:', mainTemplateSelectionState.selecting);

            if (mainTemplateSelectionState.selecting) {
                console.log('开始主图模板拖拽选择');
                e.preventDefault();
                e.stopPropagation();
                startMainTemplateDrag(e);
                return false;
            }
        });

        // 防止图片拖拽
        $('#ss_selectable_image').off('dragstart.mainSelection').on('dragstart.mainSelection', function(e) {
            e.preventDefault();
            return false;
        });

        // 禁用图片的右键菜单和选择
        $('#ss_selectable_image').off('contextmenu.mainSelection selectstart.mainSelection').on('contextmenu.mainSelection selectstart.mainSelection', function(e) {
            e.preventDefault();
            return false;
        });

        // 绑定主图模板调整手柄事件
        $(document).off('mousedown.mainResize', '.main-template-resize-handle').on('mousedown.mainResize', '.main-template-resize-handle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var $handle = $(this);
            var direction = $handle.data('direction');
            var areaType = $handle.data('area');

            console.log('🎯 主图模板调整手柄被点击！区域类型:', areaType, '方向:', direction);

            // 激活选框
            activateMainTemplateOverlay(areaType);

            // 开始调整
            startMainTemplateResize(e, areaType, direction);
        });

        // 绑定主图模板移动手柄事件
        $(document).off('mousedown.mainMove', '.main-template-move-handle').on('mousedown.mainMove', '.main-template-move-handle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var $handle = $(this);
            var areaType = $handle.data('area');

            console.log('🎯 主图模板移动手柄被点击！区域类型:', areaType);

            // 激活选框
            activateMainTemplateOverlay(areaType);

            // 开始移动
            startMainTemplateMove(e, areaType);
        });

        // 绑定主图模板选框点击事件，激活选框
        $(document).off('click.mainActivate', '.main-template-area-overlay').on('click.mainActivate', '.main-template-area-overlay', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var areaType = $(this).hasClass('svg-area') ? 'svg' : 'shoe';
            activateMainTemplateOverlay(areaType);
        });

        // 输入框变化时更新选框
        $('#ss_svg_area_x, #ss_svg_area_y, #ss_svg_area_width, #ss_svg_area_height').off('input.mainSelection').on('input.mainSelection', function() {
            console.log('SVG区域输入框变化，更新选框显示');
            var svgData = {
                x: parseInt($('#ss_svg_area_x').val()) || 0,
                y: parseInt($('#ss_svg_area_y').val()) || 0,
                width: parseInt($('#ss_svg_area_width').val()) || 0,
                height: parseInt($('#ss_svg_area_height').val()) || 0
            };
            updateMainTemplateOverlay('svg', svgData);
        });

        $('#ss_shoe_area_x, #ss_shoe_area_y, #ss_shoe_area_width, #ss_shoe_area_height').off('input.mainSelection').on('input.mainSelection', function() {
            console.log('鞋子区域输入框变化，更新选框显示');
            var shoeData = {
                x: parseInt($('#ss_shoe_area_x').val()) || 0,
                y: parseInt($('#ss_shoe_area_y').val()) || 0,
                width: parseInt($('#ss_shoe_area_width').val()) || 0,
                height: parseInt($('#ss_shoe_area_height').val()) || 0
            };
            updateMainTemplateOverlay('shoe', shoeData);
        });
    }

    // 激活主图模板选框（显示调整手柄）
    function activateMainTemplateOverlay(areaType) {
        console.log('激活主图模板选框，区域类型:', areaType);

        // 取消其他选框的激活状态
        $('.main-template-area-overlay').removeClass('active');

        // 激活当前选框
        var $overlay = $('#ss_crop_overlay_' + areaType);
        $overlay.addClass('active');

        // 确保选框可见
        if ($overlay.is(':hidden')) {
            var areaData = {
                x: parseInt($('#ss_' + areaType + '_area_x').val()) || 0,
                y: parseInt($('#ss_' + areaType + '_area_y').val()) || 0,
                width: parseInt($('#ss_' + areaType + '_area_width').val()) || 0,
                height: parseInt($('#ss_' + areaType + '_area_height').val()) || 0
            };
            updateMainTemplateOverlay(areaType, areaData);
        }
    }

    // 开始主图模板选框选择
    function startMainTemplateSelection(areaType) {
        console.log('开始主图模板选框选择，区域类型:', areaType);

        // 重置选择状态
        mainTemplateSelectionState.selecting = true;
        mainTemplateSelectionState.dragging = false;
        mainTemplateSelectionState.currentAreaType = areaType;

        // 清理可能存在的document事件绑定
        $(document).off('mousemove.mainSelection mouseup.mainSelection');
        $('body').removeClass('main-template-selecting');

        // 隐藏当前区域的选框，准备重新选择
        $('#ss_crop_overlay_' + areaType).hide();

        // 显示提示
        var areaName = areaType === 'svg' ? 'SVG填充' : '鞋子图片';
        alert('请在图片上拖拽选择' + areaName + '区域。选择完成后，点击保存区域设置按钮。');

        // 改变图片样式为选择模式
        var $img = $('#ss_selectable_image');
        $img.addClass('selecting').css('cursor', 'crosshair');

        console.log('主图模板选框选择状态已设置，区域类型:', areaType);
    }

    // 结束主图模板选框选择
    function endMainTemplateSelection() {
        console.log('结束主图模板选框选择');

        // 重置选择状态
        mainTemplateSelectionState.selecting = false;
        mainTemplateSelectionState.dragging = false;
        mainTemplateSelectionState.currentAreaType = '';

        // 解绑事件
        $(document).off('mousemove.mainSelection mouseup.mainSelection');

        // 恢复图片样式
        $('#ss_selectable_image').removeClass('selecting').css('cursor', 'default');
        $('body').removeClass('main-template-selecting');
    }

    // 开始主图模板拖拽
    function startMainTemplateDrag(e) {
        e.preventDefault();
        e.stopPropagation();

        var $img = $('#ss_selectable_image');
        var $container = $('.main-template-image-container');
        var areaType = mainTemplateSelectionState.currentAreaType;

        if (!areaType) {
            console.warn('未设置区域类型');
            return;
        }

        // 获取图片的实际显示区域
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 计算鼠标相对于图片的位置
        var startX = e.clientX - imgRect.left;
        var startY = e.clientY - imgRect.top;

        // 获取图片的显示尺寸
        var imgWidth = imgRect.width;
        var imgHeight = imgRect.height;

        // 确保起始点在图片范围内
        startX = Math.max(0, Math.min(startX, imgWidth));
        startY = Math.max(0, Math.min(startY, imgHeight));

        // 计算相对于容器的位置
        var containerStartX = startX + (imgRect.left - containerRect.left);
        var containerStartY = startY + (imgRect.top - containerRect.top);

        // 保存拖拽状态
        mainTemplateSelectionState.dragging = true;
        mainTemplateSelectionState.startX = containerStartX;
        mainTemplateSelectionState.startY = containerStartY;
        mainTemplateSelectionState.$img = $img;
        mainTemplateSelectionState.$container = $container;
        mainTemplateSelectionState.imgRect = imgRect;
        mainTemplateSelectionState.containerRect = containerRect;

        console.log('开始主图模板拖拽 - 起始位置:', { x: containerStartX, y: containerStartY, areaType: areaType });

        // 获取或创建选框
        var $overlay = $('#ss_crop_overlay_' + areaType);
        if (!$overlay.length) {
            console.error('选框元素不存在:', 'ss_crop_overlay_' + areaType);
            return;
        }

        // 显示选框
        $overlay.css({
            left: containerStartX + 'px',
            top: containerStartY + 'px',
            width: '0px',
            height: '0px'
        }).show();

        // 绑定鼠标移动和释放事件到document
        $(document).on('mousemove.mainSelection', function(e) {
            e.preventDefault();
            updateMainTemplateDrag(e);
        }).on('mouseup.mainSelection', function(e) {
            e.preventDefault();
            endMainTemplateDrag(e);
        });

        // 禁用文本选择
        $('body').addClass('main-template-selecting');

        return false;
    }

    // 更新主图模板拖拽
    function updateMainTemplateDrag(e) {
        if (!mainTemplateSelectionState.dragging) return;

        var state = mainTemplateSelectionState;
        var $img = state.$img;
        var $container = state.$container;
        var areaType = state.currentAreaType;

        // 获取当前图片和容器的位置（实时获取，因为可能有缩放）
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 计算当前鼠标相对于图片的位置
        var currentImgX = e.clientX - imgRect.left;
        var currentImgY = e.clientY - imgRect.top;

        // 限制在图片范围内
        currentImgX = Math.max(0, Math.min(currentImgX, imgRect.width));
        currentImgY = Math.max(0, Math.min(currentImgY, imgRect.height));

        // 转换为相对于容器的位置
        var currentX = currentImgX + (imgRect.left - containerRect.left);
        var currentY = currentImgY + (imgRect.top - containerRect.top);

        var startX = state.startX;
        var startY = state.startY;

        // 计算选框的位置和大小
        var left = Math.min(startX, currentX);
        var top = Math.min(startY, currentY);
        var width = Math.abs(currentX - startX);
        var height = Math.abs(currentY - startY);

        // 确保选框不超出容器范围
        var containerWidth = containerRect.width;
        var containerHeight = containerRect.height;

        left = Math.max(0, Math.min(left, containerWidth));
        top = Math.max(0, Math.min(top, containerHeight));
        width = Math.min(width, containerWidth - left);
        height = Math.min(height, containerHeight - top);

        console.log('更新主图模板拖拽:', { areaType: areaType, left: left, top: top, width: width, height: height });

        // 更新选框显示
        var $overlay = $('#ss_crop_overlay_' + areaType);
        $overlay.css({
            left: left + 'px',
            top: top + 'px',
            width: width + 'px',
            height: height + 'px'
        });
    }

    // 结束主图模板拖拽
    function endMainTemplateDrag(e) {
        if (!mainTemplateSelectionState.dragging) return;

        var state = mainTemplateSelectionState;
        var areaType = state.currentAreaType;

        state.dragging = false;

        // 解绑事件
        $(document).off('mousemove.mainSelection mouseup.mainSelection');

        // 恢复鼠标样式和文本选择
        $('#ss_selectable_image').removeClass('selecting').css('cursor', 'default');
        $('body').removeClass('main-template-selecting');

        // 计算实际坐标（相对于原始图片尺寸）
        var $img = state.$img;
        var $overlay = $('#ss_crop_overlay_' + areaType);

        // 获取当前图片的实际显示区域
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = state.$container[0].getBoundingClientRect();

        // 获取选框的位置和大小（相对于容器）
        var overlayLeft = parseInt($overlay.css('left')) || 0;
        var overlayTop = parseInt($overlay.css('top')) || 0;
        var overlayWidth = parseInt($overlay.css('width')) || 0;
        var overlayHeight = parseInt($overlay.css('height')) || 0;

        // 转换为相对于图片的坐标
        var imgLeft = imgRect.left - containerRect.left;
        var imgTop = imgRect.top - containerRect.top;

        var imgRelativeX = overlayLeft - imgLeft;
        var imgRelativeY = overlayTop - imgTop;

        // 获取原始图片尺寸和当前显示尺寸
        var originalWidth = $img[0].naturalWidth;
        var originalHeight = $img[0].naturalHeight;
        var displayWidth = imgRect.width;
        var displayHeight = imgRect.height;

        if (!originalWidth || !originalHeight) {
            console.error('无法获取原始图片尺寸');
            return;
        }

        // 计算缩放比例（考虑CSS transform缩放）
        var scaleX = originalWidth / displayWidth;
        var scaleY = originalHeight / displayHeight;

        // 转换为原始图片坐标
        var actualX = Math.round(Math.max(0, imgRelativeX * scaleX));
        var actualY = Math.round(Math.max(0, imgRelativeY * scaleY));
        var actualWidth = Math.round(overlayWidth * scaleX);
        var actualHeight = Math.round(overlayHeight * scaleY);

        console.log('🔍 主图模板坐标转换详情:', {
            areaType: areaType,
            overlay: { left: overlayLeft, top: overlayTop, width: overlayWidth, height: overlayHeight },
            img: { left: imgLeft, top: imgTop, width: displayWidth, height: displayHeight },
            relative: { x: imgRelativeX, y: imgRelativeY },
            scale: { x: scaleX, y: scaleY },
            actual: { x: actualX, y: actualY, width: actualWidth, height: actualHeight }
        });

        // 添加调试日志记录关键错误
        if (actualX < 0 || actualY < 0) {
            console.error('❌ 主图模板坐标转换错误：负坐标值', { areaType: areaType, x: actualX, y: actualY });
        }
        if (actualWidth <= 0 || actualHeight <= 0) {
            console.error('❌ 主图模板坐标转换错误：无效尺寸', { areaType: areaType, width: actualWidth, height: actualHeight });
        }
        if (scaleX <= 0 || scaleY <= 0) {
            console.error('❌ 主图模板坐标转换错误：无效缩放比例', { areaType: areaType, scaleX: scaleX, scaleY: scaleY });
        }

        // 只有当选框有实际大小时才更新输入框
        if (actualWidth > 0 && actualHeight > 0) {
            $('#ss_' + areaType + '_area_x').val(actualX);
            $('#ss_' + areaType + '_area_y').val(actualY);
            $('#ss_' + areaType + '_area_width').val(actualWidth);
            $('#ss_' + areaType + '_area_height').val(actualHeight);

            console.log('主图模板选框完成:', { areaType: areaType, x: actualX, y: actualY, width: actualWidth, height: actualHeight });
        }

        // 清理状态
        delete state.$img;
        delete state.$container;
        delete state.imgRect;
        delete state.containerRect;

        // 重置选择状态，但保持当前区域类型以便继续选择其他区域
        state.selecting = false;
    }

    // 开始调整主图模板选框大小
    function startMainTemplateResize(e, areaType, direction) {
        console.log('开始调整主图模板选框大小，区域类型:', areaType, '方向:', direction);

        var $overlay = $('#ss_crop_overlay_' + areaType);
        var $img = $('#ss_selectable_image');
        var $container = $('.main-template-image-container');

        // 获取当前选框的位置和大小
        var overlayRect = $overlay[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();
        var imgRect = $img[0].getBoundingClientRect();

        // 保存初始状态
        var resizeState = {
            areaType: areaType,
            direction: direction,
            startX: e.clientX,
            startY: e.clientY,
            startLeft: overlayRect.left - containerRect.left,
            startTop: overlayRect.top - containerRect.top,
            startWidth: overlayRect.width,
            startHeight: overlayRect.height,
            $overlay: $overlay,
            $img: $img,
            $container: $container,
            imgRect: imgRect,
            containerRect: containerRect
        };

        // 绑定鼠标移动和释放事件
        $(document).on('mousemove.mainResize', function(e) {
            updateMainTemplateResize(e, resizeState);
        }).on('mouseup.mainResize', function(e) {
            endMainTemplateResize(e, resizeState);
        });

        // 禁用文本选择
        $('body').addClass('main-template-selecting');
    }

    // 更新主图模板调整过程
    function updateMainTemplateResize(e, state) {
        var deltaX = e.clientX - state.startX;
        var deltaY = e.clientY - state.startY;

        var newLeft = state.startLeft;
        var newTop = state.startTop;
        var newWidth = state.startWidth;
        var newHeight = state.startHeight;

        // 根据调整方向计算新的位置和大小
        switch (state.direction) {
            case 'nw':
                newLeft += deltaX;
                newTop += deltaY;
                newWidth -= deltaX;
                newHeight -= deltaY;
                break;
            case 'n':
                newTop += deltaY;
                newHeight -= deltaY;
                break;
            case 'ne':
                newTop += deltaY;
                newWidth += deltaX;
                newHeight -= deltaY;
                break;
            case 'e':
                newWidth += deltaX;
                break;
            case 'se':
                newWidth += deltaX;
                newHeight += deltaY;
                break;
            case 's':
                newHeight += deltaY;
                break;
            case 'sw':
                newLeft += deltaX;
                newWidth -= deltaX;
                newHeight += deltaY;
                break;
            case 'w':
                newLeft += deltaX;
                newWidth -= deltaX;
                break;
        }

        // 限制最小尺寸
        var minSize = 20;
        if (newWidth < minSize) {
            if (state.direction.includes('w')) {
                newLeft = state.startLeft + state.startWidth - minSize;
            }
            newWidth = minSize;
        }
        if (newHeight < minSize) {
            if (state.direction.includes('n')) {
                newTop = state.startTop + state.startHeight - minSize;
            }
            newHeight = minSize;
        }

        // 限制在图片范围内
        var imgLeft = state.imgRect.left - state.containerRect.left;
        var imgTop = state.imgRect.top - state.containerRect.top;
        var imgWidth = state.imgRect.width;
        var imgHeight = state.imgRect.height;

        newLeft = Math.max(imgLeft, Math.min(newLeft, imgLeft + imgWidth - newWidth));
        newTop = Math.max(imgTop, Math.min(newTop, imgTop + imgHeight - newHeight));
        newWidth = Math.min(newWidth, imgLeft + imgWidth - newLeft);
        newHeight = Math.min(newHeight, imgTop + imgHeight - newTop);

        // 应用新的位置和大小
        state.$overlay.css({
            left: newLeft + 'px',
            top: newTop + 'px',
            width: newWidth + 'px',
            height: newHeight + 'px'
        });
    }

    // 结束主图模板调整
    function endMainTemplateResize(e, state) {
        console.log('结束调整主图模板选框大小，区域类型:', state.areaType);

        // 解绑事件
        $(document).off('mousemove.mainResize mouseup.mainResize');
        $('body').removeClass('main-template-selecting');

        // 更新输入框值
        updateMainTemplateInputsFromOverlay(state.areaType);
    }

    // 开始移动主图模板选框
    function startMainTemplateMove(e, areaType) {
        console.log('开始移动主图模板选框，区域类型:', areaType);

        var $overlay = $('#ss_crop_overlay_' + areaType);
        var $img = $('#ss_selectable_image');
        var $container = $('.main-template-image-container');

        // 获取当前选框的位置
        var overlayRect = $overlay[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();
        var imgRect = $img[0].getBoundingClientRect();

        // 保存初始状态
        var moveState = {
            areaType: areaType,
            startX: e.clientX,
            startY: e.clientY,
            startLeft: overlayRect.left - containerRect.left,
            startTop: overlayRect.top - containerRect.top,
            overlayWidth: overlayRect.width,
            overlayHeight: overlayRect.height,
            $overlay: $overlay,
            $img: $img,
            $container: $container,
            imgRect: imgRect,
            containerRect: containerRect
        };

        // 绑定鼠标移动和释放事件
        $(document).on('mousemove.mainMove', function(e) {
            updateMainTemplateMove(e, moveState);
        }).on('mouseup.mainMove', function(e) {
            endMainTemplateMove(e, moveState);
        });

        // 禁用文本选择
        $('body').addClass('main-template-selecting');
    }

    // 更新主图模板移动过程
    function updateMainTemplateMove(e, state) {
        var deltaX = e.clientX - state.startX;
        var deltaY = e.clientY - state.startY;

        var newLeft = state.startLeft + deltaX;
        var newTop = state.startTop + deltaY;

        // 限制在图片范围内
        var imgLeft = state.imgRect.left - state.containerRect.left;
        var imgTop = state.imgRect.top - state.containerRect.top;
        var imgWidth = state.imgRect.width;
        var imgHeight = state.imgRect.height;

        newLeft = Math.max(imgLeft, Math.min(newLeft, imgLeft + imgWidth - state.overlayWidth));
        newTop = Math.max(imgTop, Math.min(newTop, imgTop + imgHeight - state.overlayHeight));

        // 应用新的位置
        state.$overlay.css({
            left: newLeft + 'px',
            top: newTop + 'px'
        });
    }

    // 结束主图模板移动
    function endMainTemplateMove(e, state) {
        console.log('结束移动主图模板选框，区域类型:', state.areaType);

        // 解绑事件
        $(document).off('mousemove.mainMove mouseup.mainMove');
        $('body').removeClass('main-template-selecting');

        // 更新输入框值
        updateMainTemplateInputsFromOverlay(state.areaType);
    }

    // 根据主图模板选框位置更新输入框值
    function updateMainTemplateInputsFromOverlay(areaType) {
        var $overlay = $('#ss_crop_overlay_' + areaType);
        var $img = $('#ss_selectable_image');
        var $container = $('.main-template-image-container');

        if (!$overlay.is(':visible') || !$img.length) return;

        // 获取选框相对于容器的位置
        var overlayLeft = parseInt($overlay.css('left')) || 0;
        var overlayTop = parseInt($overlay.css('top')) || 0;
        var overlayWidth = parseInt($overlay.css('width')) || 0;
        var overlayHeight = parseInt($overlay.css('height')) || 0;

        // 获取图片位置信息
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 转换为相对于图片的坐标
        var imgLeft = imgRect.left - containerRect.left;
        var imgTop = imgRect.top - containerRect.top;

        var imgRelativeX = overlayLeft - imgLeft;
        var imgRelativeY = overlayTop - imgTop;

        // 获取原始图片尺寸和当前显示尺寸
        var originalWidth = $img[0].naturalWidth;
        var originalHeight = $img[0].naturalHeight;
        var displayWidth = imgRect.width;
        var displayHeight = imgRect.height;

        if (!originalWidth || !originalHeight) return;

        // 计算缩放比例
        var scaleX = originalWidth / displayWidth;
        var scaleY = originalHeight / displayHeight;

        // 转换为原始图片坐标
        var actualX = Math.round(Math.max(0, imgRelativeX * scaleX));
        var actualY = Math.round(Math.max(0, imgRelativeY * scaleY));
        var actualWidth = Math.round(overlayWidth * scaleX);
        var actualHeight = Math.round(overlayHeight * scaleY);

        // 更新输入框
        $('#ss_' + areaType + '_area_x').val(actualX);
        $('#ss_' + areaType + '_area_y').val(actualY);
        $('#ss_' + areaType + '_area_width').val(actualWidth);
        $('#ss_' + areaType + '_area_height').val(actualHeight);

        console.log('更新主图模板输入框值:', { areaType: areaType, x: actualX, y: actualY, width: actualWidth, height: actualHeight });
    }

    // 根据输入框值更新主图模板选框显示
    function updateMainTemplateOverlay(areaType, areaData) {
        console.log('🔄 更新主图模板选框显示，区域类型:', areaType, '数值:', areaData);

        var $img = $('#ss_selectable_image');
        var $overlay = $('#ss_crop_overlay_' + areaType);

        if (!$img.length || !$overlay.length) {
            console.warn('⚠️ 未找到主图模板图片或选框元素，区域类型:', areaType);
            return;
        }

        if (areaData.width > 0 && areaData.height > 0) {
            var displayWidth = $img.width();
            var displayHeight = $img.height();
            var originalWidth = $img[0].naturalWidth;
            var originalHeight = $img[0].naturalHeight;

            console.log('🔄 主图模板图片尺寸信息:', {
                display: { width: displayWidth, height: displayHeight },
                original: { width: originalWidth, height: originalHeight }
            });

            if (!originalWidth || !originalHeight) {
                console.warn('⚠️ 无法获取主图模板原始图片尺寸，区域类型:', areaType);
                return;
            }

            var scaleX = displayWidth / originalWidth;
            var scaleY = displayHeight / originalHeight;

            var displayX = areaData.x * scaleX;
            var displayY = areaData.y * scaleY;
            var displayW = areaData.width * scaleX;
            var displayH = areaData.height * scaleY;

            $overlay.css({
                left: displayX + 'px',
                top: displayY + 'px',
                width: displayW + 'px',
                height: displayH + 'px'
            }).show();

            console.log('✅ 主图模板选框显示成功:', {
                areaType: areaType,
                display: { x: displayX, y: displayY, width: displayW, height: displayH },
                scale: { x: scaleX, y: scaleY }
            });
        } else {
            $overlay.hide();
            console.log('🔄 隐藏主图模板选框，区域类型:', areaType);
        }
    }

    // 如果页面加载时已经有图片，初始化主图模板选框系统
    if ($('#ss_selectable_image').length) {
        $('#ss_selectable_image').on('load', function() {
            initMainTemplateSelection();
        }).each(function() {
            if (this.complete) {
                $(this).trigger('load');
            }
        });
    }

    // Gallery模板管理
    var galleryTemplateIndex = $('.ss-gallery-template-item').length;

    // 添加Gallery模板
    $('#add_gallery_template').on('click', function(e) {
        e.preventDefault();

        var templateHtml = `
            <div class="ss-gallery-template-item" data-index="${galleryTemplateIndex}">
                <h5>Gallery 模板 ${galleryTemplateIndex + 1}</h5>

                <p>
                    <label>模板图片:</label><br>
                    <input type="hidden" name="ss_gallery_templates[${galleryTemplateIndex}][image_id]" value="" class="gallery-template-image-id" />
                    <button type="button" class="button upload-gallery-template-image">选择图片</button>
                    <button type="button" class="button remove-gallery-template-image" style="display:none;">移除图片</button>
                </p>

                <div class="gallery-template-image-preview"></div>

                <p>
                    <button type="button" class="button button-secondary remove-gallery-template">删除此模板</button>
                </p>
                <hr>
            </div>
        `;

        $('#ss_gallery_templates_container').append(templateHtml);
        galleryTemplateIndex++;

        // 重新绑定事件
        bindGalleryTemplateEvents();
    });

    // 绑定Gallery模板事件
    function bindGalleryTemplateEvents() {
        // 上传Gallery模板图片
        $('.upload-gallery-template-image').off('click').on('click', function(e) {
            e.preventDefault();

            var button = $(this);
            var container = button.closest('.ss-gallery-template-item');
            var index = container.data('index');

            var galleryFrame = wp.media({
                title: '选择Gallery模板图片',
                button: {
                    text: '选择图片'
                },
                library: {
                    type: ['image/webp', 'image/jpeg', 'image/png']
                },
                multiple: false
            });

            galleryFrame.on('select', function() {
                var attachment = galleryFrame.state().get('selection').first().toJSON();

                // 更新隐藏字段
                container.find('.gallery-template-image-id').val(attachment.id);

                // 显示预览图
                container.find('.gallery-template-image-preview').html(
                    '<img src="' + attachment.url + '" style="max-width: 300px; height: auto;">'
                );

                // 显示移除按钮
                container.find('.remove-gallery-template-image').show();

                // 更新预览图显示，包含可调整选框功能
                container.find('.gallery-template-image-preview').html(
                    `<div class="gallery-template-image-container" style="position: relative; display: inline-block;">
                        <img src="${attachment.url}" class="gallery-template-image" data-index="${index}" style="max-width: 300px; height: auto;">
                        <div class="gallery-svg-area-overlay" data-index="${index}">
                            <div class="gallery-resize-handle nw" data-direction="nw"></div>
                            <div class="gallery-resize-handle n" data-direction="n"></div>
                            <div class="gallery-resize-handle ne" data-direction="ne"></div>
                            <div class="gallery-resize-handle e" data-direction="e"></div>
                            <div class="gallery-resize-handle se" data-direction="se"></div>
                            <div class="gallery-resize-handle s" data-direction="s"></div>
                            <div class="gallery-resize-handle sw" data-direction="sw"></div>
                            <div class="gallery-resize-handle w" data-direction="w"></div>
                            <div class="gallery-move-handle"></div>
                        </div>
                    </div>`
                );

                // 等待图片加载完成后重新绑定事件
                container.find('.gallery-template-image').on('load', function() {
                    console.log('Gallery图片加载完成，重新绑定选框事件');
                    bindGallerySelectionEvents();
                    // 重置缩放级别
                    if (galleryZoomLevels[index]) {
                        galleryZoomLevels[index] = 1;
                    }
                });

                // 添加缩放控制按钮
                var zoomControlsHtml = `
                    <div class="gallery-template-controls">
                        <p>
                            <button type="button" class="button gallery-zoom-in" data-index="${index}">放大图片</button>
                            <button type="button" class="button gallery-zoom-out" data-index="${index}">缩小图片</button>
                            <button type="button" class="button gallery-reset-zoom" data-index="${index}">重置缩放</button>
                        </p>
                    </div>
                `;

                // 添加SVG区域设置
                var svgAreaHtml = `
                    <h6>SVG置入区域</h6>
                    <p>
                        <button type="button" class="button select-gallery-svg-area" data-index="${index}">在图片上选择区域</button>
                    </p>
                    <p>
                        <label>X:</label>
                        <input type="number" name="ss_gallery_templates[${index}][svg_area][x]" class="gallery-svg-x" data-index="${index}" value="" />
                        <label>Y:</label>
                        <input type="number" name="ss_gallery_templates[${index}][svg_area][y]" class="gallery-svg-y" data-index="${index}" value="" />
                        <label>宽度:</label>
                        <input type="number" name="ss_gallery_templates[${index}][svg_area][width]" class="gallery-svg-width" data-index="${index}" value="" />
                        <label>高度:</label>
                        <input type="number" name="ss_gallery_templates[${index}][svg_area][height]" class="gallery-svg-height" data-index="${index}" value="" />
                    </p>
                `;

                // 如果还没有缩放控制和SVG区域设置，添加它们
                if (container.find('.gallery-template-controls').length === 0) {
                    container.find('.gallery-template-image-preview').after(zoomControlsHtml);
                }
                if (container.find('h6:contains("SVG置入区域")').length === 0) {
                    container.find('.gallery-template-controls').after(svgAreaHtml);
                }
            });

            galleryFrame.open();
        });

        // 移除Gallery模板图片
        $('.remove-gallery-template-image').off('click').on('click', function(e) {
            e.preventDefault();

            var container = $(this).closest('.ss-gallery-template-item');
            var index = container.data('index');

            // 清空数据
            container.find('.gallery-template-image-id').val('');
            container.find('.gallery-template-image-preview').html('');

            // 移除缩放控制
            container.find('.gallery-template-controls').remove();

            // 移除SVG区域设置
            container.find('h6:contains("SVG置入区域")').next('p').remove();
            container.find('h6:contains("SVG置入区域")').remove();

            // 重置缩放级别
            if (galleryZoomLevels[index]) {
                delete galleryZoomLevels[index];
            }

            // 隐藏移除按钮
            $(this).hide();
        });

        // 删除Gallery模板
        $('.remove-gallery-template').off('click').on('click', function(e) {
            e.preventDefault();

            if (confirm('确定要删除此Gallery模板吗？')) {
                $(this).closest('.ss-gallery-template-item').remove();

                // 重新编号
                $('.ss-gallery-template-item').each(function(index) {
                    $(this).data('index', index);
                    $(this).find('h5').text('Gallery 模板 ' + (index + 1));

                    // 更新表单字段名称
                    $(this).find('input[name*="ss_gallery_templates"]').each(function() {
                        var name = $(this).attr('name');
                        var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                        $(this).attr('name', newName);
                    });
                });

                // 更新索引
                galleryTemplateIndex = $('.ss-gallery-template-item').length;
            }
        });
    }

    // 初始绑定事件
    bindGalleryTemplateEvents();

    // Gallery模板选框功能
    var gallerySelectionState = {};

    // 绑定Gallery模板选框事件
    function bindGallerySelectionEvents() {
        console.log('重新绑定Gallery选框事件');

        // 选择Gallery SVG区域按钮 - 使用事件委托确保动态添加的元素也能响应
        $(document).off('click.gallerySelection', '.select-gallery-svg-area').on('click.gallerySelection', '.select-gallery-svg-area', function(e) {
            e.preventDefault();
            var index = $(this).data('index');
            console.log('点击选择Gallery SVG区域按钮，索引:', index);
            startGallerySelection(index);
        });

        // Gallery模板图片鼠标事件 - 使用事件委托
        $(document).off('mousedown.gallerySelection', '.gallery-template-image').on('mousedown.gallerySelection', '.gallery-template-image', function(e) {
            var index = $(this).data('index');
            console.log('Gallery图片mousedown事件，索引:', index, '选择状态:', gallerySelectionState[index]);

            if (gallerySelectionState[index] && gallerySelectionState[index].selecting) {
                console.log('开始Gallery拖拽选择');
                e.preventDefault(); // 防止图片被拖拽
                e.stopPropagation(); // 阻止事件冒泡
                startGalleryDrag(e, index);
                return false; // 阻止默认行为
            }
        });

        // 防止图片拖拽 - 使用事件委托
        $(document).off('dragstart.gallerySelection', '.gallery-template-image').on('dragstart.gallerySelection', '.gallery-template-image', function(e) {
            e.preventDefault();
            return false;
        });

        // 禁用图片的右键菜单和选择 - 使用事件委托
        $(document).off('contextmenu.gallerySelection selectstart.gallerySelection', '.gallery-template-image').on('contextmenu.gallerySelection selectstart.gallerySelection', '.gallery-template-image', function(e) {
            e.preventDefault();
            return false;
        });

        // 输入框变化时更新选框 - 使用事件委托
        $(document).off('input.gallerySelection', '.gallery-svg-x, .gallery-svg-y, .gallery-svg-width, .gallery-svg-height').on('input.gallerySelection', '.gallery-svg-x, .gallery-svg-y, .gallery-svg-width, .gallery-svg-height', function() {
            var index = $(this).data('index');
            console.log('输入框变化，更新选框显示，索引:', index);
            updateGalleryOverlay(index);
        });

        // 绑定调整手柄事件 - 使用事件委托
        $(document).off('mousedown.galleryResize', '.gallery-resize-handle').on('mousedown.galleryResize', '.gallery-resize-handle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var $handle = $(this);
            var direction = $handle.data('direction');
            var $overlay = $handle.closest('.gallery-svg-area-overlay');
            var index = $overlay.data('index');

            console.log('🎯 调整手柄被点击！索引:', index, '方向:', direction);
            console.log('🎯 手柄元素:', $handle[0]);
            console.log('🎯 选框元素:', $overlay[0]);

            // 自动激活选框
            activateGalleryOverlay(index);

            // 开始调整
            startGalleryResize(e, index, direction);
        });

        // 绑定移动手柄事件 - 使用事件委托
        $(document).off('mousedown.galleryMove', '.gallery-move-handle').on('mousedown.galleryMove', '.gallery-move-handle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var $handle = $(this);
            var $overlay = $handle.closest('.gallery-svg-area-overlay');
            var index = $overlay.data('index');

            console.log('🎯 移动手柄被点击！索引:', index);
            console.log('🎯 手柄元素:', $handle[0]);
            console.log('🎯 选框元素:', $overlay[0]);

            // 自动激活选框
            activateGalleryOverlay(index);

            // 开始移动
            startGalleryMove(e, index);
        });

        // 绑定选框点击事件，激活选框
        $(document).off('click.galleryActivate', '.gallery-svg-area-overlay').on('click.galleryActivate', '.gallery-svg-area-overlay', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var index = $(this).data('index');
            activateGalleryOverlay(index);
        });
    }

    // 开始Gallery选框选择
    function startGallerySelection(index) {
        console.log('开始Gallery选框选择，索引:', index);

        // 重置所有其他选择状态
        Object.keys(gallerySelectionState).forEach(function(key) {
            if (key != index) {
                if (gallerySelectionState[key]) {
                    gallerySelectionState[key].selecting = false;
                    gallerySelectionState[key].dragging = false;
                }
                $('.gallery-svg-area-overlay[data-index="' + key + '"]').hide();
                $('.gallery-template-image[data-index="' + key + '"]').removeClass('selecting').css('cursor', 'default');
            }
        });

        // 清理可能存在的document事件绑定
        $(document).off('mousemove.gallerySelection mouseup.gallerySelection');
        $('body').removeClass('gallery-selecting');

        // 初始化或重置当前选择状态
        if (!gallerySelectionState[index]) {
            gallerySelectionState[index] = {};
        }
        gallerySelectionState[index].selecting = true;
        gallerySelectionState[index].dragging = false;

        // 取消当前选框的激活状态，准备重新选择
        $('.gallery-svg-area-overlay[data-index="' + index + '"]').removeClass('active').hide();

        // 显示提示
        alert('请在图片上拖拽选择SVG置入区域。选择完成后，点击选框可以激活调整功能。');

        // 改变图片样式为选择模式
        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        $img.addClass('selecting').css('cursor', 'crosshair');

        console.log('Gallery选框选择状态已设置，索引:', index, '状态:', gallerySelectionState[index]);
    }

    // 开始拖拽
    function startGalleryDrag(e, index) {
        e.preventDefault();
        e.stopPropagation();

        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $img.parent('.gallery-template-image-container');
        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');

        // 获取图片的实际显示区域（考虑缩放）
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 计算鼠标相对于图片的位置（考虑缩放）
        var startX = e.clientX - imgRect.left;
        var startY = e.clientY - imgRect.top;

        // 获取图片的显示尺寸（缩放后的尺寸）
        var imgWidth = imgRect.width;
        var imgHeight = imgRect.height;

        // 确保起始点在图片范围内
        startX = Math.max(0, Math.min(startX, imgWidth));
        startY = Math.max(0, Math.min(startY, imgHeight));

        // 计算相对于容器的位置
        var containerStartX = startX + (imgRect.left - containerRect.left);
        var containerStartY = startY + (imgRect.top - containerRect.top);

        gallerySelectionState[index].startX = containerStartX;
        gallerySelectionState[index].startY = containerStartY;
        gallerySelectionState[index].dragging = true;
        gallerySelectionState[index].$img = $img;
        gallerySelectionState[index].$container = $container;
        gallerySelectionState[index].$overlay = $overlay;
        gallerySelectionState[index].imgRect = imgRect;
        gallerySelectionState[index].containerRect = containerRect;

        console.log('开始拖拽 - 起始位置:', { x: containerStartX, y: containerStartY });

        // 显示选框
        $overlay.css({
            left: containerStartX + 'px',
            top: containerStartY + 'px',
            width: '0px',
            height: '0px'
        }).show();

        // 绑定鼠标移动和释放事件到document，确保在图片外也能响应
        $(document).on('mousemove.gallerySelection', function(e) {
            e.preventDefault();
            updateGalleryDrag(e, index);
        }).on('mouseup.gallerySelection', function(e) {
            e.preventDefault();
            endGalleryDrag(e, index);
        });

        // 禁用文本选择
        $('body').addClass('gallery-selecting');

        return false;
    }

    // 更新拖拽
    function updateGalleryDrag(e, index) {
        if (!gallerySelectionState[index] || !gallerySelectionState[index].dragging) return;

        var state = gallerySelectionState[index];
        var $img = state.$img;
        var $container = state.$container;
        var $overlay = state.$overlay;

        // 获取当前图片和容器的位置（实时获取，因为可能有缩放）
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 计算当前鼠标相对于图片的位置
        var currentImgX = e.clientX - imgRect.left;
        var currentImgY = e.clientY - imgRect.top;

        // 限制在图片范围内
        currentImgX = Math.max(0, Math.min(currentImgX, imgRect.width));
        currentImgY = Math.max(0, Math.min(currentImgY, imgRect.height));

        // 转换为相对于容器的位置
        var currentX = currentImgX + (imgRect.left - containerRect.left);
        var currentY = currentImgY + (imgRect.top - containerRect.top);

        var startX = state.startX;
        var startY = state.startY;

        // 计算选框的位置和大小
        var left = Math.min(startX, currentX);
        var top = Math.min(startY, currentY);
        var width = Math.abs(currentX - startX);
        var height = Math.abs(currentY - startY);

        // 确保选框不超出容器范围
        var containerWidth = containerRect.width;
        var containerHeight = containerRect.height;

        left = Math.max(0, Math.min(left, containerWidth));
        top = Math.max(0, Math.min(top, containerHeight));
        width = Math.min(width, containerWidth - left);
        height = Math.min(height, containerHeight - top);

        console.log('更新拖拽:', { left: left, top: top, width: width, height: height });

        $overlay.css({
            left: left + 'px',
            top: top + 'px',
            width: width + 'px',
            height: height + 'px'
        });
    }

    // 结束拖拽
    function endGalleryDrag(e, index) {
        if (!gallerySelectionState[index] || !gallerySelectionState[index].dragging) return;

        var state = gallerySelectionState[index];
        state.dragging = false;
        state.selecting = false;

        // 解绑事件
        $(document).off('mousemove.gallerySelection mouseup.gallerySelection');

        // 恢复鼠标样式和文本选择
        $('.gallery-template-image[data-index="' + index + '"]')
            .removeClass('selecting')
            .css('cursor', 'default');
        $('body').removeClass('gallery-selecting');

        // 计算实际坐标（相对于原始图片尺寸）
        var $img = state.$img;
        var $overlay = state.$overlay;

        // 获取当前图片的实际显示区域
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = state.$container[0].getBoundingClientRect();

        // 获取选框的位置和大小（相对于容器）
        var overlayLeft = parseInt($overlay.css('left')) || 0;
        var overlayTop = parseInt($overlay.css('top')) || 0;
        var overlayWidth = parseInt($overlay.css('width')) || 0;
        var overlayHeight = parseInt($overlay.css('height')) || 0;

        // 转换为相对于图片的坐标
        var imgLeft = imgRect.left - containerRect.left;
        var imgTop = imgRect.top - containerRect.top;

        var imgRelativeX = overlayLeft - imgLeft;
        var imgRelativeY = overlayTop - imgTop;

        // 获取原始图片尺寸和当前显示尺寸
        var originalWidth = $img[0].naturalWidth;
        var originalHeight = $img[0].naturalHeight;
        var displayWidth = imgRect.width;
        var displayHeight = imgRect.height;

        if (!originalWidth || !originalHeight) {
            console.error('无法获取原始图片尺寸');
            return;
        }

        // 计算缩放比例（考虑CSS transform缩放）
        var scaleX = originalWidth / displayWidth;
        var scaleY = originalHeight / displayHeight;

        // 转换为原始图片坐标
        var actualX = Math.round(Math.max(0, imgRelativeX * scaleX));
        var actualY = Math.round(Math.max(0, imgRelativeY * scaleY));
        var actualWidth = Math.round(overlayWidth * scaleX);
        var actualHeight = Math.round(overlayHeight * scaleY);

        console.log('坐标转换详情:', {
            overlay: { left: overlayLeft, top: overlayTop, width: overlayWidth, height: overlayHeight },
            img: { left: imgLeft, top: imgTop, width: displayWidth, height: displayHeight },
            relative: { x: imgRelativeX, y: imgRelativeY },
            scale: { x: scaleX, y: scaleY },
            actual: { x: actualX, y: actualY, width: actualWidth, height: actualHeight }
        });

        // 只有当选框有实际大小时才更新输入框
        if (actualWidth > 0 && actualHeight > 0) {
            $('.gallery-svg-x[data-index="' + index + '"]').val(actualX);
            $('.gallery-svg-y[data-index="' + index + '"]').val(actualY);
            $('.gallery-svg-width[data-index="' + index + '"]').val(actualWidth);
            $('.gallery-svg-height[data-index="' + index + '"]').val(actualHeight);

            console.log('Gallery选框完成:', { index: index, x: actualX, y: actualY, width: actualWidth, height: actualHeight });
        }

        // 清理状态
        delete state.$img;
        delete state.$container;
        delete state.$overlay;
        delete state.imgRect;
        delete state.containerRect;
    }

    // 激活Gallery选框（显示调整手柄）
    function activateGalleryOverlay(index) {
        console.log('激活Gallery选框，索引:', index);

        // 取消其他选框的激活状态
        $('.gallery-svg-area-overlay').removeClass('active');

        // 激活当前选框
        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');
        $overlay.addClass('active');

        // 确保选框可见
        if ($overlay.is(':hidden')) {
            updateGalleryOverlay(index);
        }
    }

    // 开始调整Gallery选框大小
    function startGalleryResize(e, index, direction) {
        console.log('开始调整选框大小，索引:', index, '方向:', direction);

        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');
        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $img.parent('.gallery-template-image-container');

        // 获取当前选框的位置和大小
        var overlayRect = $overlay[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();
        var imgRect = $img[0].getBoundingClientRect();

        // 保存初始状态
        var resizeState = {
            index: index,
            direction: direction,
            startX: e.clientX,
            startY: e.clientY,
            startLeft: overlayRect.left - containerRect.left,
            startTop: overlayRect.top - containerRect.top,
            startWidth: overlayRect.width,
            startHeight: overlayRect.height,
            $overlay: $overlay,
            $img: $img,
            $container: $container,
            imgRect: imgRect,
            containerRect: containerRect
        };

        // 绑定鼠标移动和释放事件
        $(document).on('mousemove.galleryResize', function(e) {
            updateGalleryResize(e, resizeState);
        }).on('mouseup.galleryResize', function(e) {
            endGalleryResize(e, resizeState);
        });

        // 禁用文本选择
        $('body').addClass('gallery-selecting');
    }

    // 更新调整过程
    function updateGalleryResize(e, state) {
        var deltaX = e.clientX - state.startX;
        var deltaY = e.clientY - state.startY;

        var newLeft = state.startLeft;
        var newTop = state.startTop;
        var newWidth = state.startWidth;
        var newHeight = state.startHeight;

        // 根据调整方向计算新的位置和大小
        switch (state.direction) {
            case 'nw':
                newLeft += deltaX;
                newTop += deltaY;
                newWidth -= deltaX;
                newHeight -= deltaY;
                break;
            case 'n':
                newTop += deltaY;
                newHeight -= deltaY;
                break;
            case 'ne':
                newTop += deltaY;
                newWidth += deltaX;
                newHeight -= deltaY;
                break;
            case 'e':
                newWidth += deltaX;
                break;
            case 'se':
                newWidth += deltaX;
                newHeight += deltaY;
                break;
            case 's':
                newHeight += deltaY;
                break;
            case 'sw':
                newLeft += deltaX;
                newWidth -= deltaX;
                newHeight += deltaY;
                break;
            case 'w':
                newLeft += deltaX;
                newWidth -= deltaX;
                break;
        }

        // 限制最小尺寸
        var minSize = 20;
        if (newWidth < minSize) {
            if (state.direction.includes('w')) {
                newLeft = state.startLeft + state.startWidth - minSize;
            }
            newWidth = minSize;
        }
        if (newHeight < minSize) {
            if (state.direction.includes('n')) {
                newTop = state.startTop + state.startHeight - minSize;
            }
            newHeight = minSize;
        }

        // 限制在图片范围内
        var imgLeft = state.imgRect.left - state.containerRect.left;
        var imgTop = state.imgRect.top - state.containerRect.top;
        var imgWidth = state.imgRect.width;
        var imgHeight = state.imgRect.height;

        newLeft = Math.max(imgLeft, Math.min(newLeft, imgLeft + imgWidth - newWidth));
        newTop = Math.max(imgTop, Math.min(newTop, imgTop + imgHeight - newHeight));
        newWidth = Math.min(newWidth, imgLeft + imgWidth - newLeft);
        newHeight = Math.min(newHeight, imgTop + imgHeight - newTop);

        // 应用新的位置和大小
        state.$overlay.css({
            left: newLeft + 'px',
            top: newTop + 'px',
            width: newWidth + 'px',
            height: newHeight + 'px'
        });
    }

    // 结束调整
    function endGalleryResize(e, state) {
        console.log('结束调整选框大小，索引:', state.index);

        // 解绑事件
        $(document).off('mousemove.galleryResize mouseup.galleryResize');
        $('body').removeClass('gallery-selecting');

        // 更新输入框值
        updateGalleryInputsFromOverlay(state.index);
    }

    // 开始移动Gallery选框
    function startGalleryMove(e, index) {
        console.log('开始移动选框，索引:', index);

        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');
        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $img.parent('.gallery-template-image-container');

        // 获取当前选框的位置
        var overlayRect = $overlay[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();
        var imgRect = $img[0].getBoundingClientRect();

        // 保存初始状态
        var moveState = {
            index: index,
            startX: e.clientX,
            startY: e.clientY,
            startLeft: overlayRect.left - containerRect.left,
            startTop: overlayRect.top - containerRect.top,
            overlayWidth: overlayRect.width,
            overlayHeight: overlayRect.height,
            $overlay: $overlay,
            $img: $img,
            $container: $container,
            imgRect: imgRect,
            containerRect: containerRect
        };

        // 绑定鼠标移动和释放事件
        $(document).on('mousemove.galleryMove', function(e) {
            updateGalleryMove(e, moveState);
        }).on('mouseup.galleryMove', function(e) {
            endGalleryMove(e, moveState);
        });

        // 禁用文本选择
        $('body').addClass('gallery-selecting');
    }

    // 更新移动过程
    function updateGalleryMove(e, state) {
        var deltaX = e.clientX - state.startX;
        var deltaY = e.clientY - state.startY;

        var newLeft = state.startLeft + deltaX;
        var newTop = state.startTop + deltaY;

        // 限制在图片范围内
        var imgLeft = state.imgRect.left - state.containerRect.left;
        var imgTop = state.imgRect.top - state.containerRect.top;
        var imgWidth = state.imgRect.width;
        var imgHeight = state.imgRect.height;

        newLeft = Math.max(imgLeft, Math.min(newLeft, imgLeft + imgWidth - state.overlayWidth));
        newTop = Math.max(imgTop, Math.min(newTop, imgTop + imgHeight - state.overlayHeight));

        // 应用新的位置
        state.$overlay.css({
            left: newLeft + 'px',
            top: newTop + 'px'
        });
    }

    // 结束移动
    function endGalleryMove(e, state) {
        console.log('结束移动选框，索引:', state.index);

        // 解绑事件
        $(document).off('mousemove.galleryMove mouseup.galleryMove');
        $('body').removeClass('gallery-selecting');

        // 更新输入框值
        updateGalleryInputsFromOverlay(state.index);
    }

    // 根据选框位置更新输入框值
    function updateGalleryInputsFromOverlay(index) {
        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');
        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $img.parent('.gallery-template-image-container');

        if (!$overlay.is(':visible') || !$img.length) return;

        // 获取选框相对于容器的位置
        var overlayLeft = parseInt($overlay.css('left')) || 0;
        var overlayTop = parseInt($overlay.css('top')) || 0;
        var overlayWidth = parseInt($overlay.css('width')) || 0;
        var overlayHeight = parseInt($overlay.css('height')) || 0;

        // 获取图片位置信息
        var imgRect = $img[0].getBoundingClientRect();
        var containerRect = $container[0].getBoundingClientRect();

        // 转换为相对于图片的坐标
        var imgLeft = imgRect.left - containerRect.left;
        var imgTop = imgRect.top - containerRect.top;

        var imgRelativeX = overlayLeft - imgLeft;
        var imgRelativeY = overlayTop - imgTop;

        // 获取原始图片尺寸和当前显示尺寸
        var originalWidth = $img[0].naturalWidth;
        var originalHeight = $img[0].naturalHeight;
        var displayWidth = imgRect.width;
        var displayHeight = imgRect.height;

        if (!originalWidth || !originalHeight) return;

        // 计算缩放比例
        var scaleX = originalWidth / displayWidth;
        var scaleY = originalHeight / displayHeight;

        // 转换为原始图片坐标
        var actualX = Math.round(Math.max(0, imgRelativeX * scaleX));
        var actualY = Math.round(Math.max(0, imgRelativeY * scaleY));
        var actualWidth = Math.round(overlayWidth * scaleX);
        var actualHeight = Math.round(overlayHeight * scaleY);

        // 更新输入框
        $('.gallery-svg-x[data-index="' + index + '"]').val(actualX);
        $('.gallery-svg-y[data-index="' + index + '"]').val(actualY);
        $('.gallery-svg-width[data-index="' + index + '"]').val(actualWidth);
        $('.gallery-svg-height[data-index="' + index + '"]').val(actualHeight);

        console.log('更新输入框值:', { index: index, x: actualX, y: actualY, width: actualWidth, height: actualHeight });
    }

    // 根据输入框值更新选框显示
    function updateGalleryOverlay(index) {
        var x = parseInt($('.gallery-svg-x[data-index="' + index + '"]').val()) || 0;
        var y = parseInt($('.gallery-svg-y[data-index="' + index + '"]').val()) || 0;
        var width = parseInt($('.gallery-svg-width[data-index="' + index + '"]').val()) || 0;
        var height = parseInt($('.gallery-svg-height[data-index="' + index + '"]').val()) || 0;

        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $overlay = $('.gallery-svg-area-overlay[data-index="' + index + '"]');

        console.log('🔄 更新Gallery选框显示，索引:', index, '数值:', { x: x, y: y, width: width, height: height });
        console.log('🔄 找到的元素:', { img: $img.length, overlay: $overlay.length });

        if (!$img.length || !$overlay.length) {
            console.warn('⚠️ 未找到图片或选框元素，索引:', index);
            return;
        }

        if (width > 0 && height > 0) {
            var displayWidth = $img.width();
            var displayHeight = $img.height();
            var originalWidth = $img[0].naturalWidth;
            var originalHeight = $img[0].naturalHeight;

            console.log('🔄 图片尺寸信息:', {
                display: { width: displayWidth, height: displayHeight },
                original: { width: originalWidth, height: originalHeight }
            });

            if (!originalWidth || !originalHeight) {
                console.warn('⚠️ 无法获取原始图片尺寸，索引:', index);
                return;
            }

            var scaleX = displayWidth / originalWidth;
            var scaleY = displayHeight / originalHeight;

            var displayX = x * scaleX;
            var displayY = y * scaleY;
            var displayW = width * scaleX;
            var displayH = height * scaleY;

            $overlay.css({
                left: displayX + 'px',
                top: displayY + 'px',
                width: displayW + 'px',
                height: displayH + 'px'
            }).show().addClass('active'); // 自动激活选框

            console.log('✅ Gallery选框显示成功:', {
                index: index,
                display: { x: displayX, y: displayY, width: displayW, height: displayH },
                scale: { x: scaleX, y: scaleY }
            });
        } else {
            $overlay.hide().removeClass('active');
            console.log('🔄 隐藏选框，索引:', index);
        }
    }

    // 在bindGalleryTemplateEvents函数中调用选框事件绑定
    var originalBindGalleryTemplateEvents = bindGalleryTemplateEvents;
    bindGalleryTemplateEvents = function() {
        originalBindGalleryTemplateEvents();
        bindGallerySelectionEvents();

        // 为已有的Gallery模板显示选框
        $('.gallery-template-image').each(function() {
            var index = $(this).data('index');
            if (index !== undefined) {
                updateGalleryOverlay(index);
            }
        });
    };

    // 页面加载时初始化
    $(document).ready(function() {
        console.log('📋 页面加载完成，开始初始化Gallery选框功能');

        // 立即绑定Gallery选框事件
        bindGallerySelectionEvents();

        // 检查页面上的Gallery选框元素
        setTimeout(function() {
            console.log('🔍 检查页面上的Gallery元素...');

            var $galleryImages = $('.gallery-template-image');
            var $galleryOverlays = $('.gallery-svg-area-overlay');
            var $resizeHandles = $('.gallery-resize-handle');
            var $moveHandles = $('.gallery-move-handle');

            console.log('📊 Gallery元素统计:', {
                images: $galleryImages.length,
                overlays: $galleryOverlays.length,
                resizeHandles: $resizeHandles.length,
                moveHandles: $moveHandles.length
            });

            // 为每个Gallery模板初始化选框显示
            $galleryImages.each(function() {
                var index = $(this).data('index');
                if (index !== undefined) {
                    console.log('🔄 初始化Gallery选框，索引:', index);
                    updateGalleryOverlay(index);
                }
            });

            // 测试事件绑定
            console.log('🧪 测试事件绑定...');
            if ($resizeHandles.length > 0) {
                console.log('✅ 找到调整手柄，数量:', $resizeHandles.length);
            } else {
                console.warn('⚠️ 未找到调整手柄');
            }

            if ($moveHandles.length > 0) {
                console.log('✅ 找到移动手柄，数量:', $moveHandles.length);
            } else {
                console.warn('⚠️ 未找到移动手柄');
            }

        }, 500);
    });

    // 主图模板缩放功能
    var mainTemplateZoomLevel = 1;
    var maxZoom = 3;
    var minZoom = 0.5;

    function zoomMainTemplate(factor) {
        var newZoom = mainTemplateZoomLevel * factor;
        if (newZoom > maxZoom) newZoom = maxZoom;
        if (newZoom < minZoom) newZoom = minZoom;

        mainTemplateZoomLevel = newZoom;

        var $img = $('.main-template-image');
        var $container = $('.main-template-image-container');

        if ($img.length) {
            $img.css('transform', 'scale(' + mainTemplateZoomLevel + ')');

            if (mainTemplateZoomLevel > 1) {
                $img.addClass('zoomed');
                $container.addClass('zoom-mode');
            } else {
                $img.removeClass('zoomed');
                $container.removeClass('zoom-mode');
            }

            console.log('主图模板缩放级别:', mainTemplateZoomLevel);
        }
    }

    function resetMainTemplateZoom() {
        mainTemplateZoomLevel = 1;
        var $img = $('.main-template-image');
        var $container = $('.main-template-image-container');

        if ($img.length) {
            $img.css('transform', 'scale(1)');
            $img.removeClass('zoomed');
            $container.removeClass('zoom-mode');
            console.log('主图模板缩放已重置');
        }
    }

    // Gallery模板缩放功能
    var galleryZoomLevels = {};

    function zoomGalleryTemplate(index, factor) {
        if (!galleryZoomLevels[index]) {
            galleryZoomLevels[index] = 1;
        }

        var newZoom = galleryZoomLevels[index] * factor;
        if (newZoom > maxZoom) newZoom = maxZoom;
        if (newZoom < minZoom) newZoom = minZoom;

        galleryZoomLevels[index] = newZoom;

        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $('.gallery-template-image-container').has($img);

        if ($img.length) {
            $img.css('transform', 'scale(' + galleryZoomLevels[index] + ')');

            if (galleryZoomLevels[index] > 1) {
                $img.addClass('zoomed');
                $container.addClass('zoom-mode');
            } else {
                $img.removeClass('zoomed');
                $container.removeClass('zoom-mode');
            }

            console.log('Gallery模板 ' + index + ' 缩放级别:', galleryZoomLevels[index]);
        }
    }

    function resetGalleryTemplateZoom(index) {
        galleryZoomLevels[index] = 1;
        var $img = $('.gallery-template-image[data-index="' + index + '"]');
        var $container = $('.gallery-template-image-container').has($img);

        if ($img.length) {
            $img.css('transform', 'scale(1)');
            $img.removeClass('zoomed');
            $container.removeClass('zoom-mode');
            console.log('Gallery模板 ' + index + ' 缩放已重置');
        }
    }

    // 绑定Gallery模板缩放事件
    $(document).on('click', '.gallery-zoom-in', function() {
        var index = $(this).data('index');
        console.log('Gallery模板放大按钮被点击，索引:', index);
        zoomGalleryTemplate(index, 1.2);
    });

    $(document).on('click', '.gallery-zoom-out', function() {
        var index = $(this).data('index');
        console.log('Gallery模板缩小按钮被点击，索引:', index);
        zoomGalleryTemplate(index, 0.8);
    });

    $(document).on('click', '.gallery-reset-zoom', function() {
        var index = $(this).data('index');
        console.log('Gallery模板重置缩放按钮被点击，索引:', index);
        resetGalleryTemplateZoom(index);
    });

});
