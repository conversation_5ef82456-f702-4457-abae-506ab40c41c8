jQuery(document).ready(function($) {
    $('#ss-sync-svg-btn').on('click', function() {
        var $button = $(this);
        var originalText = $button.text();
        
        // 禁用按钮并显示加载状态
        $button.prop('disabled', true).text('同步中...');

        // 发送同步请求
        $.ajax({
            url: ss_svg_library_params.ajaxurl,
            type: 'POST',
            data: {
                action: 'ss_sync_svg',
                nonce: ss_svg_library_params.nonce
            },
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    alert(ss_svg_library_params.sync_success);
                    // 刷新页面以显示更新后的文件列表
                    window.location.reload();
                } else {
                    alert(ss_svg_library_params.sync_error);
                }
            },
            error: function() {
                alert(ss_svg_library_params.sync_error);
            },
            complete: function() {
                // 恢复按钮状态
                $button.prop('disabled', false).text(originalText);
            }
        });
    });
});
