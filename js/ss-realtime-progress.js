/**
 * 实时进度推送 JavaScript 模块
 * 
 * 使用 Server-Sent Events (SSE) 替代 AJAX 轮询，实现实时进度更新
 * 
 * @package ShoeSVGGenerator
 * @since 1.0.0
 */

class SSRealtimeProgress {
    constructor(categoryId, options = {}) {
        this.categoryId = categoryId;
        this.options = {
            updateInterval: 1000, // 更新间隔（毫秒）
            maxRetries: 5,        // 最大重试次数
            retryDelay: 2000,     // 重试延迟（毫秒）
            ...options
        };
        
        this.eventSource = null;
        this.retryCount = 0;
        this.isConnected = false;
        this.callbacks = {
            progress: [],
            complete: [],
            error: [],
            connect: [],
            disconnect: []
        };
        
        // 绑定方法上下文
        this.handleMessage = this.handleMessage.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleOpen = this.handleOpen.bind(this);
        
        console.log('[实时进度] 初始化完成，类目ID:', categoryId);
    }
    
    /**
     * 开始监听进度
     */
    start() {
        if (this.eventSource) {
            this.stop();
        }
        
        // 检查浏览器支持
        if (typeof EventSource === 'undefined') {
            console.warn('[实时进度] 浏览器不支持 Server-Sent Events，回退到轮询模式');
            this.startPolling();
            return;
        }
        
        try {
            // 构建 SSE URL
            const url = new URL(ss_frontend_params.ajax_url);
            url.searchParams.set('action', 'ss_progress_stream');
            url.searchParams.set('category_id', this.categoryId);
            url.searchParams.set('nonce', ss_frontend_params.nonce);
            
            console.log('[实时进度] 连接 SSE:', url.toString());
            
            // 创建 EventSource 连接
            this.eventSource = new EventSource(url.toString());
            
            // 绑定事件处理器
            this.eventSource.onopen = this.handleOpen;
            this.eventSource.onmessage = this.handleMessage;
            this.eventSource.onerror = this.handleError;
            
        } catch (error) {
            console.error('[实时进度] 启动失败:', error);
            this.triggerCallback('error', { error: error.message });
            this.startPolling(); // 回退到轮询
        }
    }
    
    /**
     * 停止监听
     */
    stop() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
            this.isConnected = false;
            console.log('[实时进度] 连接已关闭');
            this.triggerCallback('disconnect');
        }
        
        if (this.pollingTimer) {
            clearInterval(this.pollingTimer);
            this.pollingTimer = null;
        }
    }
    
    /**
     * 处理连接打开
     */
    handleOpen(event) {
        this.isConnected = true;
        this.retryCount = 0;
        console.log('[实时进度] 连接已建立');
        this.triggerCallback('connect');
    }
    
    /**
     * 处理消息接收
     */
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('[实时进度] 收到进度更新:', data);
            
            // 触发进度回调
            this.triggerCallback('progress', data);
            
            // 检查是否完成
            if (data.completed || data.percentage >= 100) {
                console.log('[实时进度] 处理完成');
                this.triggerCallback('complete', data);
                this.stop();
            }
            
        } catch (error) {
            console.error('[实时进度] 解析消息失败:', error, event.data);
        }
    }
    
    /**
     * 处理连接错误
     */
    handleError(event) {
        console.error('[实时进度] 连接错误:', event);
        this.isConnected = false;
        
        // 如果重试次数未超限，尝试重连
        if (this.retryCount < this.options.maxRetries) {
            this.retryCount++;
            console.log(`[实时进度] 尝试重连 (${this.retryCount}/${this.options.maxRetries})`);
            
            setTimeout(() => {
                if (!this.isConnected) {
                    this.start();
                }
            }, this.options.retryDelay);
        } else {
            console.warn('[实时进度] 重试次数已达上限，切换到轮询模式');
            this.triggerCallback('error', { error: '连接失败，切换到轮询模式' });
            this.startPolling();
        }
    }
    
    /**
     * 回退到轮询模式
     */
    startPolling() {
        console.log('[实时进度] 启动轮询模式');
        
        this.pollingTimer = setInterval(() => {
            this.checkProgressPolling();
        }, this.options.updateInterval);
        
        // 立即检查一次
        this.checkProgressPolling();
    }
    
    /**
     * 轮询检查进度
     */
    checkProgressPolling() {
        jQuery.ajax({
            url: ss_frontend_params.ajax_url,
            type: 'POST',
            data: {
                action: 'ss_get_progress',
                category_id: this.categoryId,
                nonce: ss_frontend_params.nonce
            },
            success: (response) => {
                if (response.success && response.data) {
                    console.log('[实时进度] 轮询获取进度:', response.data);
                    this.triggerCallback('progress', response.data);
                    
                    if (response.data.completed || response.data.percentage >= 100) {
                        console.log('[实时进度] 轮询检测到完成');
                        this.triggerCallback('complete', response.data);
                        this.stop();
                    }
                }
            },
            error: (xhr, status, error) => {
                console.error('[实时进度] 轮询请求失败:', error);
            }
        });
    }
    
    /**
     * 注册回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
        return this;
    }
    
    /**
     * 移除回调函数
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
        return this;
    }
    
    /**
     * 触发回调函数
     */
    triggerCallback(event, data = null) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`[实时进度] 回调函数执行失败 (${event}):`, error);
                }
            });
        }
    }
    
    /**
     * 获取连接状态
     */
    isConnectedToServer() {
        return this.isConnected;
    }
    
    /**
     * 手动触发进度检查
     */
    checkProgress() {
        if (this.isConnected && this.eventSource) {
            // SSE 模式下不需要手动检查
            return;
        }
        
        // 轮询模式下立即检查
        this.checkProgressPolling();
    }
}

/**
 * 全局进度管理器
 */
window.SSProgressManager = {
    instances: new Map(),
    
    /**
     * 创建或获取进度监听器
     */
    create(categoryId, options = {}) {
        if (this.instances.has(categoryId)) {
            return this.instances.get(categoryId);
        }
        
        const instance = new SSRealtimeProgress(categoryId, options);
        this.instances.set(categoryId, instance);
        return instance;
    },
    
    /**
     * 销毁进度监听器
     */
    destroy(categoryId) {
        if (this.instances.has(categoryId)) {
            const instance = this.instances.get(categoryId);
            instance.stop();
            this.instances.delete(categoryId);
        }
    },
    
    /**
     * 销毁所有监听器
     */
    destroyAll() {
        this.instances.forEach((instance, categoryId) => {
            instance.stop();
        });
        this.instances.clear();
    }
};

// 页面卸载时清理所有连接
window.addEventListener('beforeunload', () => {
    window.SSProgressManager.destroyAll();
});

console.log('[实时进度] 模块加载完成');
