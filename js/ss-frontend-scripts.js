/**
 * 前端脚本文件
 *
 * 用于处理前端交互，包括SVG设计选择、加载更多等
 */

jQuery(document).ready(function($) {
    // 初始化性能监控
    const perfMonitor = {
        startTime: performance.now(),
        metrics: {
            firstContentfulPaint: null,
            timeToInteractive: null,
            loadTimes: []
        },
        trackEvent: function(event, data = {}) {
            const timeElapsed = performance.now() - this.startTime;
            data.timeElapsed = timeElapsed;

            // 低优先级发送数据到服务器
            setTimeout(() => {
                $.ajax({
                    url: ss_frontend_params.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'ss_track_interaction',
                        nonce: ss_frontend_params.nonce,
                        event: event,
                        data: data
                    }
                });
            }, 100);
        }
    };

    // 模板选择弹窗管理器
    const templateModal = {
        modal: null,
        currentSvg: null,
        debug: false, // 关闭调试模式

        init: function() {
            this.modal = $('#ss-template-modal');
            this.bindEvents();
            this.bindWindowEvents(); // 【新增】绑定窗口事件
            this.initHorizontalSlider(); // 【新增】初始化横向滑块
        },

        // 调试日志（保留但不执行任何操作）
        log: function(message, data = null) {
            // 调试模式关闭，不执行任何操作
            return;
        },

        // 显示错误信息
        showError: function(message) {
            const $error = this.modal.find('.ss-modal-error');
            $error.find('.ss-error-message').text(message);
            $error.show();
            this.modal.find('.ss-modal-loading').hide();
        },

        // 绑定事件
        bindEvents: function() {
            // 【修复】点击SVG预览图 - 修复加载状态管理
            $(document).on('click', '.ss-svg-preview-item', (e) => {
                const $item = $(e.currentTarget);
                const svgFile = $item.data('file');
                const svgIndex = $item.data('index');

                this.currentSvg = {
                    file: svgFile,
                    index: svgIndex
                };

                // 【优化】记录触发元素的位置，用于智能定位
                this.triggerElement = $item;

                // 【修复】先重置状态（不清除currentSvg），再显示弹窗，最后加载模板
                this.resetModal(false);
                this.show();
                this.loadTemplates();
            });

            // 关闭弹窗
            this.modal.find('.ss-modal-close').on('click', () => {
                this.hide();
            });

            // 点击弹窗外部关闭
            $(document).on('click', '.ss-modal', (e) => {
                if ($(e.target).hasClass('ss-modal')) {
                    this.hide();
                }
            });

            // 重试按钮点击
            this.modal.find('.ss-retry-btn').on('click', () => {
                this.loadTemplates();
            });

            // 点击模板项
            $(document).on('click', '.ss-template-item', (e) => {
                const $item = $(e.currentTarget);
                const templateId = $item.data('id');

                // 移除其他模板的选中状态和生成状态
                $('.ss-template-item').removeClass('selected generating');
                $('.ss-template-item .ss-template-generation-status').remove();

                // 添加当前模板的选中状态
                $item.addClass('selected');

                // 【优化】在当前模板项上显示生成状态
                this.showTemplateGenerationStatus($item, 'Checking product...');

                // 检查是否已生成过该产品
                this.checkProductExists(templateId, $item);
            });
        },

        // 【优化】绑定窗口事件 - 增强移动端支持
        bindWindowEvents: function() {
            let resizeTimeout;
            let scrollTimeout;
            let orientationTimeout;

            // 窗口大小变化时重新计算位置
            $(window).on('resize', () => {
                if (this.modal.hasClass('show')) {
                    clearTimeout(resizeTimeout);
                    resizeTimeout = setTimeout(() => {
                        this.calculateOptimalPosition();
                        console.log('窗口大小变化，重新计算模态框位置');
                    }, 150);
                }
            });

            // 【新增】移动端方向变化处理
            $(window).on('orientationchange', () => {
                if (this.modal.hasClass('show')) {
                    clearTimeout(orientationTimeout);
                    orientationTimeout = setTimeout(() => {
                        this.calculateOptimalPosition();
                        console.log('设备方向变化，重新计算模态框位置');
                    }, 300);
                }
            });

            // 滚动时重新计算位置（节流处理，移动端优化）
            $(window).on('scroll', () => {
                if (this.modal.hasClass('show')) {
                    clearTimeout(scrollTimeout);
                    const isMobile = $(window).width() <= 768;
                    const delay = isMobile ? 100 : 50; // 移动端减少计算频率

                    scrollTimeout = setTimeout(() => {
                        this.calculateOptimalPosition();
                    }, delay);
                }
            });

            // 【新增】移动端触摸事件优化
            if ('ontouchstart' in window) {
                // 防止模态框背景滚动
                this.modal.on('touchmove', (e) => {
                    if ($(e.target).hasClass('ss-modal')) {
                        e.preventDefault();
                    }
                });

                // 模态框内容区域允许滚动
                this.modal.find('.ss-modal-body').on('touchmove', (e) => {
                    e.stopPropagation();
                });
            }
        },

        // 【优化】显示弹窗 - 添加智能定位逻辑
        show: function() {
            // 【新增】智能定位逻辑
            this.calculateOptimalPosition();

            this.modal.addClass('show');
            // 隐藏调试信息
            this.modal.find('.ss-debug-info').hide();

            // 【修复】添加安全检查，确保加载状态在5秒后自动隐藏
            setTimeout(() => {
                if (this.modal.find('.ss-template-grid').children().length > 0) {
                    // 如果已经有模板内容，确保隐藏加载状态
                    this.modal.find('.ss-modal-loading').hide();
                }
            }, 5000);

            // 【新增】添加调试日志
            console.log('模板悬浮窗已显示，当前滚动位置:', window.pageYOffset || document.documentElement.scrollTop);
        },

        // 隐藏弹窗
        hide: function() {
            this.modal.removeClass('show');
            // 重置弹窗状态
            this.resetModal();
        },

        // 【优化】计算最佳显示位置 - 增强移动端体验
        calculateOptimalPosition: function() {
            const $modalContent = this.modal.find('.ss-modal-content');
            const windowHeight = $(window).height();
            const windowWidth = $(window).width();
            const scrollTop = $(window).scrollTop();

            // 获取触发元素的位置信息
            let triggerTop = 0;
            let triggerHeight = 0;

            if (this.triggerElement && this.triggerElement.length > 0) {
                const triggerOffset = this.triggerElement.offset();
                triggerTop = triggerOffset.top;
                triggerHeight = this.triggerElement.outerHeight();

                console.log('触发元素位置信息:', {
                    triggerTop: triggerTop,
                    triggerHeight: triggerHeight,
                    scrollTop: scrollTop,
                    windowHeight: windowHeight,
                    windowWidth: windowWidth
                });
            }

            // 移动端优化定位逻辑
            let optimalTop;
            let modalMaxHeight;
            let minTopMargin;
            let maxBottomMargin;

            if (windowWidth <= 480) {
                // 小屏幕手机 (iPhone SE等)
                modalMaxHeight = windowHeight * 0.95;
                minTopMargin = 10;
                maxBottomMargin = 10;
                optimalTop = scrollTop + minTopMargin;
                console.log('小屏幕手机模式，模态框紧贴视口顶部');
            } else if (windowWidth <= 768) {
                // 平板和大屏手机
                modalMaxHeight = windowHeight * 0.92;
                minTopMargin = 15;
                maxBottomMargin = 15;

                // 判断触发元素在视口中的位置
                const triggerRelativeToViewport = triggerTop - scrollTop;
                const isInUpperThird = triggerRelativeToViewport < windowHeight / 3;

                if (isInUpperThird) {
                    // 触发元素在视口上三分之一，模态框显示在触发元素下方
                    optimalTop = Math.max(triggerTop + triggerHeight + 8, scrollTop + minTopMargin);
                } else {
                    // 其他情况显示在视口顶部
                    optimalTop = scrollTop + minTopMargin;
                }
                console.log('移动端模式，智能定位完成');
            } else {
                // 桌面端
                modalMaxHeight = windowHeight * 0.9;
                minTopMargin = 20;
                maxBottomMargin = 20;

                // 判断触发元素在视口中的位置
                const triggerRelativeToViewport = triggerTop - scrollTop;
                const isInUpperHalf = triggerRelativeToViewport < windowHeight / 2;

                if (isInUpperHalf) {
                    // 触发元素在视口上半部分，模态框显示在触发元素下方
                    optimalTop = Math.max(triggerTop + triggerHeight + 10, scrollTop + minTopMargin);
                } else {
                    // 触发元素在视口下半部分，模态框显示在视口中上部
                    optimalTop = scrollTop + minTopMargin;
                }
                console.log('桌面端模式，智能定位完成');
            }

            // 确保模态框不会超出屏幕底部
            const maxAllowedTop = scrollTop + windowHeight - modalMaxHeight - maxBottomMargin;
            optimalTop = Math.min(optimalTop, maxAllowedTop);

            // 应用计算出的位置
            $modalContent.css('top', optimalTop + 'px');

            // 【修复】移动端确保水平居中，防止超出屏幕边界
            if (windowWidth <= 768) {
                $modalContent.css({
                    'left': '50%',
                    'transform': 'translateX(-50%)',
                    '-webkit-overflow-scrolling': 'touch',
                    'overscroll-behavior': 'contain'
                });

                // 【调试】检查是否超出屏幕边界
                const modalWidth = $modalContent.outerWidth();
                const screenWidth = windowWidth;
                if (modalWidth > screenWidth) {
                    console.warn('悬浮窗宽度超出屏幕:', {
                        modalWidth: modalWidth,
                        screenWidth: screenWidth,
                        overflow: modalWidth - screenWidth
                    });
                }
            }

            console.log('模态框智能定位完成:', {
                optimalTop: optimalTop,
                scrollTop: scrollTop,
                windowHeight: windowHeight,
                windowWidth: windowWidth,
                modalMaxHeight: modalMaxHeight,
                deviceType: windowWidth <= 480 ? '小屏手机' : windowWidth <= 768 ? '移动端' : '桌面端'
            });
        },

        // 【修复】重置弹窗状态 - 修复加载状态管理
        resetModal: function(clearSvg = true) {
            // 【修复】只在关闭弹窗时清除currentSvg，打开时保留
            if (clearSvg) {
                this.currentSvg = null;
                this.triggerElement = null; // 【新增】清理触发元素引用
            }
            this.modal.find('.ss-template-grid').empty();
            this.modal.find('.ss-modal-error').hide();
            // 【修复】重置时不自动显示加载状态，让loadTemplates函数控制
            this.modal.find('.ss-modal-loading').hide();
            this.modal.find('.ss-debug-output').empty();
            this.modal.find('.ss-generation-status').hide();
        },

        // 【修复】加载模板列表 - 修复缓存时加载状态不消失的bug
        loadTemplates: function() {
            if (!this.currentSvg) {
                this.showError('Invalid SVG file information');
                return;
            }

            // 【修复】先显示加载状态，确保状态正确初始化
            this.modal.find('.ss-modal-loading').show();
            this.modal.find('.ss-modal-error').hide();
            this.modal.find('.ss-template-grid').empty();

            // 【优化】检查本地缓存，使用更智能的缓存策略
            const templateCacheKey = `category_templates_${ss_category_data.term_id}`;
            const cachedTemplates = sessionStorage.getItem(templateCacheKey);

            if (cachedTemplates) {
                try {
                    const templateData = JSON.parse(cachedTemplates);
                    // 【优化】根据类目类型调整缓存时间
                    const cacheTime = ss_category_data.is_custom_matching ? 600000 : 300000; // 自定义匹配类目缓存10分钟

                    if (Date.now() - templateData.timestamp < cacheTime) {
                        console.log('[优化] Loading templates from cache, cache age:', Math.round((Date.now() - templateData.timestamp) / 1000), 'seconds');
                        // 【修复】从缓存加载时也要隐藏加载状态
                        this.renderTemplates(templateData.templates);
                        this.modal.find('.ss-modal-loading').hide();
                        return;
                    } else {
                        console.log('[优化] Cache expired, removing');
                        sessionStorage.removeItem(templateCacheKey);
                    }
                } catch (e) {
                    // 缓存数据损坏，清除
                    sessionStorage.removeItem(templateCacheKey);
                    console.warn('[优化] Cache data corrupted, removed');
                }
            }

            // 【优化】发送AJAX请求获取模板列表
            const startTime = Date.now();
            $.ajax({
                url: ss_frontend_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'ss_get_category_templates',
                    nonce: ss_frontend_params.nonce,
                    category_id: ss_category_data.term_id
                },
                timeout: 15000, // 【极速优化】减少超时时间到15秒，提升响应速度
                success: (response) => {
                    const duration = Date.now() - startTime;
                    console.log(`[极速优化] Templates loaded in ${duration}ms`);

                    // 【极速优化】性能监控和用户提示
                    if (response.data && response.data.performance) {
                        console.log('[性能监控] 模板加载性能:', response.data.performance);
                    }

                    if (duration > 5000) {
                        console.warn('[极速优化] Template loading is slow, consider optimizing');
                    } else if (duration < 1000) {
                        console.log('[极速优化] Template loading is very fast!');
                    }

                    if (response.success && response.data && response.data.templates && response.data.templates.length > 0) {
                        // 【优化】缓存模板数据，添加性能指标
                        const cacheData = {
                            templates: response.data.templates,
                            timestamp: Date.now(),
                            loadTime: duration,
                            count: response.data.templates.length
                        };
                        sessionStorage.setItem(templateCacheKey, JSON.stringify(cacheData));

                        console.log(`[优化] Cached ${response.data.templates.length} templates`);
                        this.renderTemplates(response.data.templates);
                    } else {
                        this.showError(response.data?.message || 'No available template products found');
                    }
                },
                error: (xhr, status, error) => {
                    const duration = Date.now() - startTime;
                    console.error(`Template loading failed after ${duration}ms:`, {
                        status: status,
                        error: error,
                        responseText: xhr.responseText,
                        category_id: ss_category_data.term_id
                    });

                    let errorMessage = 'Error loading templates: ';
                    if (status === 'timeout') {
                        errorMessage += 'Request timeout. Please try again.';
                    } else if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage += response.data?.message || error;
                        } catch (e) {
                            errorMessage += error;
                        }
                    } else {
                        errorMessage += error;
                    }

                    this.showError(errorMessage);
                },
                complete: () => {
                    this.modal.find('.ss-modal-loading').hide();
                }
            });
        },

        // 【修复】渲染模板列表 - 确保正确隐藏加载状态
        renderTemplates: function(templates) {
            // 【修复】确保隐藏加载状态
            this.modal.find('.ss-modal-loading').hide();

            if (!Array.isArray(templates) || templates.length === 0) {
                this.showError('No templates available');
                return;
            }

            const $grid = this.modal.find('.ss-template-grid');
            $grid.empty();

            templates.forEach((template) => {
                const title = template.name || template.title || '';
                const image = template.image || '';

                const templateHtml = `
                    <div class="ss-template-item" data-id="${template.id}">
                        <div class="ss-template-image">
                            <img src="${image}" alt="${title}">
                        </div>
                        <div class="ss-template-title">${title}</div>
                    </div>
                `;
                $grid.append(templateHtml);
            });

            // 【修复】确保模板网格可见
            $grid.show();

            // 【新增】模板渲染完成后更新横向滑块
            setTimeout(() => {
                this.updateHorizontalSlider();
            }, 100);
        },

        // 【新增】更新横向滑块的便捷方法
        updateHorizontalSlider: function() {
            const $sliderWrapper = this.modal.find('.ss-template-slider-wrapper');
            if ($sliderWrapper.length > 0) {
                $sliderWrapper.trigger('scroll');
                console.log('横向滑块已更新');
            }
        },

        // 【优化】检查产品是否已存在 - 提升响应速度
        checkProductExists: function(templateId, $templateItem = null) {
            if (!this.currentSvg || !templateId) {
                this.showError('Missing required parameters');
                return;
            }

            // 【优化】使用更智能的本地缓存检查
            const cacheKey = `product_exists_${templateId}_${this.currentSvg.file}_${ss_category_data.term_id}`;
            const cachedResult = sessionStorage.getItem(cacheKey);

            if (cachedResult) {
                try {
                    const result = JSON.parse(cachedResult);
                    const cacheAge = Date.now() - result.timestamp;

                    if (result.exists) {
                        console.log('[优化] Product exists in cache, redirecting directly');
                        if ($templateItem) {
                            this.showTemplateGenerationStatus($templateItem, 'Product found, redirecting...');
                        }
                        this.redirectToProduct(result.product_url);
                        return;
                    } else if (cacheAge < 120000) { // 【优化】增加到2分钟缓存，减少重复检查
                        console.log('[优化] Product non-existence cached, generating directly');
                        this.generateProduct(templateId, $templateItem);
                        return;
                    } else {
                        console.log('[优化] Cache expired, removing');
                        sessionStorage.removeItem(cacheKey);
                    }
                } catch (e) {
                    console.warn('[优化] Cache data corrupted, removing');
                    sessionStorage.removeItem(cacheKey);
                }
            }

            // 【优化】发送AJAX请求检查产品是否存在
            $.ajax({
                url: ss_frontend_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'ss_check_svg_product_exists',
                    nonce: ss_frontend_params.nonce,
                    template_id: templateId,
                    svg_file: this.currentSvg.file,
                    category_id: ss_category_data.term_id
                },
                timeout: 15000, // 【极速优化】减少超时时间到15秒，提升响应速度
                success: (response) => {
                    if (response.success) {
                        // 【优化】缓存结果
                        const cacheData = {
                            exists: response.data.exists,
                            product_url: response.data.product_url || '',
                            timestamp: Date.now()
                        };
                        sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));

                        if (response.data.exists) {
                            if ($templateItem) {
                                this.showTemplateGenerationStatus($templateItem, 'Product found, redirecting...');
                            }
                            this.redirectToProduct(response.data.product_url);
                        } else {
                            this.generateProduct(templateId, $templateItem);
                        }
                    } else {
                        this.showError(response.data?.message || 'Product check failed');
                        if ($templateItem) {
                            this.hideTemplateGenerationStatus($templateItem);
                        }
                    }
                },
                error: (xhr, status, error) => {
                    this.showError('Error checking product: ' + error);
                    if ($templateItem) {
                        this.hideTemplateGenerationStatus($templateItem);
                    }
                }
            });
        },

        // 【优化】生成产品 - 提升处理速度
        generateProduct: function(templateId, $templateItem = null) {
            // 【优化】在模板项上显示生成状态
            if ($templateItem) {
                this.showTemplateGenerationStatus($templateItem, 'Generating product...');
            }

            // 【优化】记录开始时间
            const startTime = Date.now();

            // 【优化】发送AJAX请求生成产品
            $.ajax({
                url: ss_frontend_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'ss_generate_svg_product',
                    nonce: ss_frontend_params.nonce,
                    template_id: templateId,
                    svg_file: this.currentSvg.file,
                    category_id: ss_category_data.term_id
                },
                timeout: 120000, // 【极速优化】减少超时时间到2分钟，配合缓存机制提升响应速度
                success: (response) => {
                    const duration = Date.now() - startTime;
                    console.log(`[极速优化] Product generation completed in ${duration}ms`);

                    // 【调试】记录完整的响应数据
                    console.log('[调试] 完整响应数据:', response);

                    // 【性能监控】记录性能数据
                    if (response.data && response.data.performance) {
                        console.log('[性能监控] 产品生成性能:', response.data.performance);

                        // 性能等级评估
                        const totalTime = response.data.performance.total_time_ms;
                        let performanceLevel = '';
                        if (totalTime < 5000) {
                            performanceLevel = '🚀 极速';
                        } else if (totalTime < 15000) {
                            performanceLevel = '✅ 良好';
                        } else if (totalTime < 30000) {
                            performanceLevel = '⚠️ 一般';
                        } else {
                            performanceLevel = '❌ 需要优化';
                        }

                        console.log(`[性能评估] ${performanceLevel} (${totalTime}ms)`);

                        if (response.data.performance.cache_hit) {
                            console.log('[缓存加速] 使用了缓存的合成图片，显著提升速度');
                        }
                    }

                    // 【修复】检查响应结构和product_url
                    if (response.success && response.data) {
                        console.log('[调试] 产品生成成功，产品ID:', response.data.product_id);
                        console.log('[调试] 产品URL:', response.data.product_url);

                        // 检查product_url是否有效
                        if (!response.data.product_url) {
                            console.error('[错误] 产品URL为空');
                            this.showError('产品生成成功但无法获取产品URL');
                            if ($templateItem) {
                                this.hideTemplateGenerationStatus($templateItem);
                            }
                            return;
                        }

                        // 【优化】清除相关缓存
                        const cacheKey = `product_exists_${templateId}_${this.currentSvg.file}_${ss_category_data.term_id}`;
                        sessionStorage.removeItem(cacheKey);

                        // 【优化】在模板项上显示成功状态
                        if ($templateItem) {
                            this.showTemplateGenerationStatus($templateItem, 'Product generated successfully!');
                        }

                        // 跳转到生成的产品页面
                        console.log('[调试] 准备跳转到:', response.data.product_url);
                        this.redirectToProduct(response.data.product_url);
                    } else {
                        console.error('[错误] 产品生成失败:', response);
                        this.showError(response.data?.message || 'Failed to generate product');
                        if ($templateItem) {
                            this.hideTemplateGenerationStatus($templateItem);
                        }
                    }
                },
                error: (xhr, status, error) => {
                    const duration = Date.now() - startTime;
                    console.error(`[极速优化] Product generation failed after ${duration}ms:`, error);

                    // 【优化】提供更友好的错误信息
                    let errorMessage = 'Error generating product: ';
                    if (status === 'timeout') {
                        errorMessage += 'Request timed out. Please try again.';
                    } else {
                        errorMessage += error;
                    }

                    this.showError(errorMessage);
                    if ($templateItem) {
                        this.hideTemplateGenerationStatus($templateItem);
                    }
                }
            });
        },

        // 重定向到产品页面
        redirectToProduct: function(url) {
            // 【优化】立即跳转到产品页面，无延迟
            window.location.href = url;
        },

        // 【新增】在模板项上显示生成状态
        showTemplateGenerationStatus: function($templateItem, message) {
            if (!$templateItem || $templateItem.length === 0) return;

            // 移除现有的状态显示
            $templateItem.find('.ss-template-generation-status').remove();

            // 添加生成中的样式类
            $templateItem.addClass('generating');

            // 创建状态显示元素
            const statusHtml = `
                <div class="ss-template-generation-status">
                    <div class="ss-template-status-overlay">
                        <div class="ss-template-status-content">
                            <div class="ss-template-status-spinner"></div>
                            <p class="ss-template-status-message">${message}</p>
                        </div>
                    </div>
                </div>
            `;

            $templateItem.append(statusHtml);

            // 【修复Safari】强制重绘以确保状态显示
            setTimeout(() => {
                const $status = $templateItem.find('.ss-template-generation-status');
                if ($status.length > 0) {
                    $status[0].offsetHeight; // 强制重绘
                    $status.css('opacity', '1'); // 确保可见
                }
            }, 10);

            // 【修复Safari】添加额外的检查确保状态显示
            setTimeout(() => {
                const $overlay = $templateItem.find('.ss-template-status-overlay');
                if ($overlay.length > 0 && $overlay.css('display') === 'none') {
                    $overlay.css('display', 'flex');
                    console.log('[Safari修复] 强制显示状态覆盖层');
                }
            }, 50);
        },

        // 【新增】隐藏模板项上的生成状态
        hideTemplateGenerationStatus: function($templateItem) {
            if (!$templateItem || $templateItem.length === 0) return;

            $templateItem.removeClass('generating');
            $templateItem.find('.ss-template-generation-status').remove();
        },

        // 【新增】初始化横向滑块
        initHorizontalSlider: function() {
            const $sliderWrapper = this.modal.find('.ss-template-slider-wrapper');
            const $scrollbarContainer = this.modal.find('.ss-horizontal-scrollbar');
            const $scrollbarThumb = this.modal.find('.ss-horizontal-scrollbar-thumb');

            let isDragging = false;
            let startX = 0;
            let startScrollLeft = 0;

            // 更新横向滑块位置和大小
            const updateHorizontalScrollbar = () => {
                const contentWidth = $sliderWrapper[0].scrollWidth;
                const containerWidth = $sliderWrapper.width();

                if (contentWidth <= containerWidth) {
                    // 内容不需要滚动时隐藏滑块
                    $scrollbarContainer.removeClass('visible');
                    return;
                }

                // 显示滑块
                $scrollbarContainer.addClass('visible');

                const scrollLeft = $sliderWrapper.scrollLeft();
                const scrollRatio = scrollLeft / (contentWidth - containerWidth);
                const thumbWidth = Math.max((containerWidth / contentWidth) * containerWidth, 30);
                const thumbLeft = scrollRatio * (containerWidth - thumbWidth);

                $scrollbarThumb.css({
                    width: thumbWidth + 'px',
                    left: thumbLeft + 'px'
                });

                console.log('横向滑块更新:', {
                    contentWidth: contentWidth,
                    containerWidth: containerWidth,
                    scrollLeft: scrollLeft,
                    thumbWidth: thumbWidth,
                    thumbLeft: thumbLeft
                });
            };

            // 内容滚动时更新滑块
            $sliderWrapper.on('scroll', updateHorizontalScrollbar);

            // 滑块拖拽功能
            $scrollbarThumb.on('mousedown touchstart', (e) => {
                e.preventDefault();
                isDragging = true;
                startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                startScrollLeft = $sliderWrapper.scrollLeft();

                $scrollbarThumb.addClass('active');
                $(document).on('mousemove touchmove', handleDrag);
                $(document).on('mouseup touchend', handleDragEnd);
            });

            const handleDrag = (e) => {
                if (!isDragging) return;

                e.preventDefault();
                const currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
                const deltaX = currentX - startX;

                const containerWidth = $sliderWrapper.width();
                const contentWidth = $sliderWrapper[0].scrollWidth;
                const thumbWidth = $scrollbarThumb.width();

                const maxThumbLeft = containerWidth - thumbWidth;
                const scrollRatio = deltaX / maxThumbLeft;
                const newScrollLeft = startScrollLeft + scrollRatio * (contentWidth - containerWidth);

                $sliderWrapper.scrollLeft(Math.max(0, Math.min(newScrollLeft, contentWidth - containerWidth)));
            };

            const handleDragEnd = () => {
                isDragging = false;
                $scrollbarThumb.removeClass('active');
                $(document).off('mousemove touchmove', handleDrag);
                $(document).off('mouseup touchend', handleDragEnd);
            };

            // 点击滑块轨道跳转
            $scrollbarContainer.find('.ss-horizontal-scrollbar-track').on('click', (e) => {
                if ($(e.target).hasClass('ss-horizontal-scrollbar-thumb')) return;

                const trackRect = e.currentTarget.getBoundingClientRect();
                const clickX = e.clientX - trackRect.left;
                const containerWidth = $sliderWrapper.width();
                const contentWidth = $sliderWrapper[0].scrollWidth;

                const scrollRatio = clickX / containerWidth;
                const newScrollLeft = scrollRatio * (contentWidth - containerWidth);

                $sliderWrapper.scrollLeft(Math.max(0, Math.min(newScrollLeft, contentWidth - containerWidth)));
            });

            // 监听内容变化
            const observer = new MutationObserver(updateHorizontalScrollbar);
            const $templateGrid = this.modal.find('.ss-template-grid');
            if ($templateGrid.length > 0) {
                observer.observe($templateGrid[0], {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
            }

            // 窗口大小变化时更新滑块
            $(window).on('resize', updateHorizontalScrollbar);

            // 初始更新
            setTimeout(updateHorizontalScrollbar, 100);

            // 【新增】移动端触摸滑动优化
            if ($(window).width() <= 768) {
                let startTouchX = 0;
                let isScrolling = false;

                $sliderWrapper.on('touchstart', (e) => {
                    startTouchX = e.touches[0].clientX;
                    isScrolling = false;
                });

                $sliderWrapper.on('touchmove', (e) => {
                    if (!isScrolling) {
                        const deltaX = Math.abs(e.touches[0].clientX - startTouchX);
                        if (deltaX > 10) {
                            isScrolling = true;
                        }
                    }
                });

                // 添加惯性滚动效果
                $sliderWrapper.css({
                    '-webkit-overflow-scrolling': 'touch',
                    'scroll-behavior': 'smooth'
                });
            }
        }
    };

    // 初始化模板选择弹窗
    templateModal.init();

    // 记录首次交互
    $(document).one('click', function() {
        perfMonitor.trackEvent('first_interaction');
    });

    // 记录页面完全加载完成
    $(window).on('load', function() {
        perfMonitor.trackEvent('page_fully_loaded');
    });

    // Load More 按钮优化
    $('.ss-load-more-btn').on('click', function() {
        perfMonitor.trackEvent('load_more_clicked');
    });

    // 【清理】移除重复的加载逻辑，统一使用svg-design-list.php中的逻辑
    // 这里只保留性能监控功能
    if ($('.woocommerce-products-header').length > 0) {
        // 立即记录首次内容展示时间
        perfMonitor.metrics.firstContentfulPaint = performance.now() - perfMonitor.startTime;
        perfMonitor.trackEvent('frameRendered');
    }

    // 【清理】移除重复的fetchCategoryData函数

    // 【清理】移除重复的initializeSkeletonLayout函数

    // 【新增】缓存管理和清理机制
    const cacheManager = {
        // 清理过期缓存
        cleanExpiredCache: function() {
            const now = Date.now();
            const keys = Object.keys(sessionStorage);

            keys.forEach(key => {
                if (key.startsWith('category_templates_') || key.startsWith('product_exists_')) {
                    try {
                        const data = JSON.parse(sessionStorage.getItem(key));
                        if (data.timestamp && (now - data.timestamp > 600000)) { // 10分钟过期
                            sessionStorage.removeItem(key);
                            console.log(`Cleaned expired cache: ${key}`);
                        }
                    } catch (e) {
                        // 数据格式错误，直接删除
                        sessionStorage.removeItem(key);
                    }
                }
            });
        },

        // 清理所有相关缓存
        clearAllCache: function() {
            const keys = Object.keys(sessionStorage);
            keys.forEach(key => {
                if (key.startsWith('category_templates_') || key.startsWith('product_exists_')) {
                    sessionStorage.removeItem(key);
                }
            });
            console.log('All cache cleared');
        },

        // 获取缓存统计信息
        getCacheStats: function() {
            const keys = Object.keys(sessionStorage);
            const stats = {
                templateCache: 0,
                productCache: 0,
                totalSize: 0
            };

            keys.forEach(key => {
                if (key.startsWith('category_templates_')) {
                    stats.templateCache++;
                } else if (key.startsWith('product_exists_')) {
                    stats.productCache++;
                }
                stats.totalSize += sessionStorage.getItem(key).length;
            });

            return stats;
        }
    };

    // 【新增】定期清理过期缓存
    setInterval(() => {
        cacheManager.cleanExpiredCache();
    }, 300000); // 每5分钟清理一次

    // 【新增】页面卸载时清理缓存
    $(window).on('beforeunload', function() {
        cacheManager.cleanExpiredCache();
    });

    // 【新增】开发者工具 - 仅在调试模式下可用
    if (typeof ss_frontend_params !== 'undefined' && ss_frontend_params.debug) {
        window.ssCacheManager = cacheManager;
        window.ssTemplateModal = templateModal;
        console.log('SS Debug tools available: ssCacheManager, ssTemplateModal');
    }

    // 【清理】移除所有重复的加载逻辑，统一使用svg-design-list.php中的逻辑
    // 这里只保留模板选择和性能监控功能
});