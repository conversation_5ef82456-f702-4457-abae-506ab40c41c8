jQuery(document).ready(function($){
    // 初始化 SelectWoo
    $('.ss-color-select').each(function(){
        var $select = $(this);

        $select.selectWoo({
            templateResult: formatColorOption,
            templateSelection: function(state) {
                return state.text;
            },
            width: '50%',
            allowClear: true,
            placeholder: $select.data('placeholder'),
            escapeMarkup: function(markup) {
                return markup; // 不转义HTML
            }
        });
    });

    function formatColorOption(state) {
        if (!state.id) {
            return state.text;
        }

        var color = $(state.element).data('color');
        if (color) {
            var $span = $('<span class="ss-color-option"><span class="ss-color-swatch"></span></span>');
            $span.find('.ss-color-swatch').css('background-color', color);
            $span.append(state.text);
            return $span;
        } else {
            return state.text;
        }
    }
});
