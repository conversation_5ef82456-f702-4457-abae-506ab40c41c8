/**
 * 模板保护功能的JavaScript
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // 保护状态切换
        $('#ss_template_protected_checkbox').on('change', function() {
            var isChecked = $(this).is(':checked');
            $('#protection_level_settings').toggle(isChecked);
        });

        // AJAX切换保护状态
        $('#ss_toggle_protection_btn').on('click', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var productId = $btn.data('product-id');
            var isProtected = $btn.data('protected') === 1;
            
            // 禁用按钮
            $btn.prop('disabled', true).text('处理中...');
            
            $.ajax({
                url: ssProtector.ajax_url,
                type: 'POST',
                data: {
                    action: 'ss_toggle_template_protection',
                    product_id: productId,
                    protected: isProtected ? '1' : '0',
                    nonce: ssProtector.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // 更新按钮状态
                        var newProtected = !isProtected;
                        $btn.data('protected', newProtected ? 1 : 0);
                        $btn.text(newProtected ? '解除保护' : '立即保护');
                        
                        // 更新复选框
                        $('#ss_template_protected_checkbox').prop('checked', newProtected);
                        
                        // 更新保护级别设置显示
                        $('#protection_level_settings').toggle(newProtected);
                        
                        // 更新状态显示
                        updateProtectionStatus(newProtected);
                        
                        // 显示成功消息
                        showNotice(response.data.message, 'success');
                        
                        // 刷新页面以更新所有状态
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showNotice('操作失败: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showNotice('网络错误，请重试', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false);
                }
            });
        });

        // 更新保护状态显示
        function updateProtectionStatus(isProtected) {
            var $statusDiv = $('.ss-protection-status');
            
            if (isProtected) {
                $statusDiv.removeClass('unprotected').addClass('protected');
                $statusDiv.html(
                    '<p><span class="dashicons dashicons-shield-alt"></span> <strong>此模板已受保护</strong></p>' +
                    '<p><small>受保护的内容包括：</small></p>' +
                    '<ul style="font-size: 11px; margin-left: 15px;">' +
                    '<li>产品基本信息（标题、描述、价格）</li>' +
                    '<li>产品图片（主图、画廊图片）</li>' +
                    '<li>产品变体和属性</li>' +
                    '<li>模板相关设置</li>' +
                    '<li>关联的图片文件</li>' +
                    '</ul>'
                );
            } else {
                $statusDiv.removeClass('protected').addClass('unprotected');
                $statusDiv.html(
                    '<p><span class="dashicons dashicons-warning"></span> <strong>此模板未受保护</strong></p>' +
                    '<p><small>建议启用保护以防止意外修改或删除</small></p>'
                );
            }
        }

        // 显示通知消息
        function showNotice(message, type) {
            var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
            var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
            
            // 移除现有通知
            $('.notice.ss-temp-notice').remove();
            
            // 添加新通知
            $notice.addClass('ss-temp-notice');
            $('.wrap h1').after($notice);
            
            // 自动移除通知
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }

        // 表单提交前的确认
        $('form#post').on('submit', function(e) {
            var isProtected = $('#ss_template_protected_checkbox').is(':checked');
            var originalProtected = $('#ss_toggle_protection_btn').data('protected') === 1;
            
            // 如果要解除保护，需要确认
            if (originalProtected && !isProtected) {
                if (!confirm('确定要解除模板保护吗？解除保护后，此模板可能会被意外修改或删除。')) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // 防止受保护模板的关键字段被修改
        function protectFormFields() {
            var isProtected = $('#ss_template_protected_checkbox').is(':checked');
            var protectionLevel = $('input[name="ss_template_protection_level"]:checked').val();
            
            if (isProtected && protectionLevel === 'full') {
                // 添加只读属性到关键字段
                var criticalFields = [
                    '#title',
                    '#content',
                    '#excerpt',
                    'input[name="_regular_price"]',
                    'input[name="_sale_price"]',
                    'input[name="_sku"]',
                    'input[name="_weight"]',
                    'input[name="_length"]',
                    'input[name="_width"]',
                    'input[name="_height"]'
                ];
                
                criticalFields.forEach(function(selector) {
                    $(selector).prop('readonly', true).addClass('ss-protected-field');
                });
                
                // 添加样式
                if (!$('#ss-protection-styles').length) {
                    $('<style id="ss-protection-styles">' +
                      '.ss-protected-field { background-color: #f9f9f9 !important; border-color: #ddd !important; }' +
                      '.ss-protected-field:focus { box-shadow: none !important; }' +
                      '</style>').appendTo('head');
                }
                
                // 显示保护提示
                if (!$('.ss-protection-warning').length) {
                    var $warning = $('<div class="notice notice-info ss-protection-warning">' +
                                   '<p><span class="dashicons dashicons-shield-alt"></span> ' +
                                   '此模板已受完全保护，关键字段无法修改。如需修改，请先解除保护。</p>' +
                                   '</div>');
                    $('.wrap h1').after($warning);
                }
            }
        }

        // 监听保护设置变化
        $('#ss_template_protected_checkbox, input[name="ss_template_protection_level"]').on('change', function() {
            // 移除现有保护
            $('.ss-protected-field').prop('readonly', false).removeClass('ss-protected-field');
            $('.ss-protection-warning').remove();
            $('#ss-protection-styles').remove();
            
            // 重新应用保护
            setTimeout(protectFormFields, 100);
        });

        // 初始化时应用保护
        protectFormFields();

        // 产品图片上传保护
        if (typeof wp !== 'undefined' && wp.media) {
            var originalMediaFrame = wp.media.view.MediaFrame.Select;
            wp.media.view.MediaFrame.Select = originalMediaFrame.extend({
                initialize: function() {
                    originalMediaFrame.prototype.initialize.apply(this, arguments);
                    
                    // 检查是否是受保护的模板
                    var isProtected = $('#ss_template_protected_checkbox').is(':checked');
                    var protectionLevel = $('input[name="ss_template_protection_level"]:checked').val();
                    
                    if (isProtected && protectionLevel === 'full') {
                        this.on('select', function() {
                            alert('此模板已受保护，无法修改图片。请先解除保护。');
                            return false;
                        });
                    }
                }
            });
        }
    });

})(jQuery);
