#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSS连接测试脚本
"""

import os
import sys

# 添加配置路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

def test_oss_connection():
    """测试OSS连接"""
    print("🔍 测试OSS连接...")
    print("=" * 50)
    
    # 1. 检查oss2模块
    try:
        import oss2
        print(f"✅ OSS2 SDK 版本: {oss2.__version__}")
    except ImportError as e:
        print(f"❌ OSS2 SDK 导入失败: {e}")
        print("请运行: pip3 install --break-system-packages oss2")
        return False
    
    # 2. 检查配置文件
    try:
        import oss_config
        print("✅ OSS配置文件导入成功")
        print(f"  - Endpoint: {oss_config.ENDPOINT}")
        print(f"  - Bucket: {oss_config.BUCKET_NAME}")
        print(f"  - Access Key ID: {oss_config.ACCESS_KEY_ID[:8]}...")
    except ImportError as e:
        print(f"❌ OSS配置文件导入失败: {e}")
        return False
    
    # 3. 测试OSS连接
    try:
        auth = oss2.Auth(oss_config.ACCESS_KEY_ID, oss_config.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, oss_config.ENDPOINT, oss_config.BUCKET_NAME)
        
        # 测试bucket访问
        bucket_info = bucket.get_bucket_info()
        print("✅ OSS连接测试成功")
        print(f"  - Bucket创建时间: {bucket_info.creation_date}")
        print(f"  - 存储类型: {bucket_info.storage_class}")
        
        # 测试列举文件（只列举前5个）
        try:
            objects = list(bucket.list_objects(prefix='filf/', max_keys=5))
            print(f"  - FILF文件数量（前5个）: {len(objects.object_list)}")
            for obj in objects.object_list[:3]:  # 只显示前3个
                print(f"    * {obj.key}")
        except Exception as e:
            print(f"  - 列举文件失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OSS连接测试失败: {e}")
        return False

def test_upload_small_file():
    """测试上传小文件"""
    print("\n🔍 测试文件上传...")
    print("=" * 50)
    
    try:
        import oss2
        import oss_config

        auth = oss2.Auth(oss_config.ACCESS_KEY_ID, oss_config.ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, oss_config.ENDPOINT, oss_config.BUCKET_NAME)
        
        # 创建测试文件
        test_content = "OSS连接测试文件 - " + str(os.getpid())
        test_key = "test/connection_test.txt"
        
        # 上传测试
        result = bucket.put_object(test_key, test_content)
        if result.status == 200:
            print("✅ 文件上传测试成功")
            print(f"  - 测试文件: {test_key}")
            print(f"  - ETag: {result.etag}")
            
            # 删除测试文件
            bucket.delete_object(test_key)
            print("✅ 测试文件已清理")
            return True
        else:
            print(f"❌ 文件上传失败，状态码: {result.status}")
            return False
            
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 OSS Python SDK 连接测试")
    print("=" * 50)
    
    success = True
    
    # 测试连接
    if not test_oss_connection():
        success = False
    
    # 测试上传
    if not test_upload_small_file():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！OSS Python SDK 配置正确")
        sys.exit(0)
    else:
        print("❌ 测试失败，请检查配置")
        sys.exit(1)
