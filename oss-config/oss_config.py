# -*- coding: utf-8 -*-
"""
阿里云OSS配置文件
"""

# OSS访问凭证
ACCESS_KEY_ID = 'LTAI5tRkZypdtnXYMKx2h1a3'
ACCESS_KEY_SECRET = '******************************'

# OSS配置信息
ENDPOINT = 'oss-us-west-1.aliyuncs.com'  # 外网访问endpoint
BUCKET_NAME = 'oss-svg'

# 上传配置
UPLOAD_TIMEOUT = 300  # 上传超时时间（秒）
MULTIPART_THRESHOLD = 100 * 1024 * 1024  # 100MB，超过此大小使用分片上传
PART_SIZE = 10 * 1024 * 1024  # 分片大小 10MB

# 路径配置
FILF_PREFIX = 'filf/'  # FILF文件在OSS中的前缀路径
PNG_PREFIX = 'png/'    # PNG文件在OSS中的前缀路径（备用）

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 1  # 重试间隔（秒）

# 日志配置
ENABLE_LOGGING = True
LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR
