<?php
/**
 * SVG预览任务管理器类
 *
 * 使用Action Scheduler实现并行处理多个类目的SVG预览图生成
 *
 * @package Shoe_SVG_Generator
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

class SS_Preview_Task_Manager {
    /**
     * 权限修复函数 - 确保目录和文件所有者为www用户
     */
    private function ensure_www_permissions($path, $is_file = false) {
        if (!file_exists($path)) {
            return false;
        }
        
        // 设置基本权限
        $mode = $is_file ? 0644 : 0755;
        @chmod($path, $mode);
        
        // 尝试设置www用户所有权（优先使用www用户）
        $www_users = ['www', 'www-data', 'apache', 'nginx', 'httpd'];
        
        foreach ($www_users as $www_user) {
            if (function_exists('posix_getpwnam')) {
                $user_info = posix_getpwnam($www_user);
                if ($user_info !== false) {
                    $www_uid = $user_info['uid'];
                    $www_gid = $user_info['gid'];
                    
                    // 尝试使用PHP函数设置所有者
                    if (@chown($path, $www_uid) && @chgrp($path, $www_gid)) {
                        error_log("[权限修复] 成功设置所有者: " . basename($path) . " -> {$www_user}");
                        return true;
                    }
                }
            }
            
            // 尝试使用系统命令
            $cmd = "chown {$www_user}:{$www_user} " . escapeshellarg($path) . " 2>/dev/null";
            $result = shell_exec($cmd);
            if ($result === null || empty(trim($result))) {
                error_log("[权限修复] 使用系统命令设置所有者成功: " . basename($path) . " -> {$www_user}");
                return true;
            }
        }
        
        error_log("[权限修复] 无法设置www用户所有权: " . basename($path));
        return false;
    }

    /**
     * 创建目录并设置正确权限
     */
    private function create_directory_with_permissions($dir_path) {
        if (file_exists($dir_path)) {
            $this->ensure_www_permissions($dir_path, false);
            return true;
        }
        
        // 创建目录
        $created = wp_mkdir_p($dir_path);
        if ($created) {
            @chmod($dir_path, 0755);
            $this->ensure_www_permissions($dir_path, false);
            error_log("[权限修复] 创建目录并设置权限: {$dir_path}");
            return true;
        }
        
        error_log("[权限修复] 创建目录失败: {$dir_path}");
        return false;
    }

    /**
     * 递归设置整个目录树的权限
     */
    private function fix_directory_tree_permissions($dir_path) {
        if (!file_exists($dir_path) || !is_dir($dir_path)) {
            return false;
        }

        // 首先设置当前目录权限
        $this->ensure_www_permissions($dir_path, false);
        error_log("[权限修复] 递归修复目录权限: {$dir_path}");

        // 递归处理所有子目录和文件
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir_path, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                $this->ensure_www_permissions($item->getPathname(), false);
                error_log("[权限修复] 递归修复子目录权限: " . $item->getPathname());
            } else {
                $this->ensure_www_permissions($item->getPathname(), true);
                error_log("[权限修复] 递归修复文件权限: " . $item->getPathname());
            }
        }

        return true;
    }

    /**
     * 创建类目完整目录结构并设置权限
     */
    private function create_category_directory_structure($category_name) {
        $upload_dir = wp_upload_dir();
        
        // 获取安全的类目名称
        $safe_category_name = sanitize_title($category_name);
        
        // 主类目目录
        $category_main_dir = $upload_dir['basedir'] . '/shoe-svg-generator/frontend-gallery/' . $safe_category_name . '/';
        
        // 预览图子目录
        $previews_dir = $category_main_dir . 'previews/';
        
        // 创建主目录
        if (!file_exists($category_main_dir)) {
            $this->create_directory_with_permissions($category_main_dir);
        } else {
            $this->ensure_www_permissions($category_main_dir, false);
        }
        
        // 创建预览图子目录
        if (!file_exists($previews_dir)) {
            $this->create_directory_with_permissions($previews_dir);
        } else {
            $this->ensure_www_permissions($previews_dir, false);
        }
        
        // 递归修复整个目录树的权限
        $this->fix_directory_tree_permissions($category_main_dir);
        
        error_log("[权限修复] 完整类目目录结构创建完成: {$category_main_dir}");
        
        return $category_main_dir;
    }

    /**
     * 单例实例
     *
     * @var SS_Preview_Task_Manager
     */
    private static $instance = null;

    /**
     * 任务组前缀
     *
     * @var string
     */
    private $group_prefix = 'ss_preview_category_';

    /**
     * 最大并行任务数
     *
     * @var int
     */
    private $max_concurrent_tasks = 10;

    /**
     * 获取单例实例
     *
     * @return SS_Preview_Task_Manager
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        // 添加Action Scheduler处理钩子
        add_action('ss_process_svg_preview', array($this, 'process_svg_preview'), 10, 4);

        // 添加初始化钩子
        add_action('init', array($this, 'init'));

        // 尝试加载Action Scheduler库
        $this->load_action_scheduler();
    }

    /**
     * 尝试加载Action Scheduler库
     */
    private function load_action_scheduler() {
        // 检查Action Scheduler库是否已加载
        if (function_exists('as_schedule_single_action') && function_exists('as_get_scheduled_actions')) {
            // error_log("[预览任务管理器] Action Scheduler库已加载");

            // 检查数据库表是否存在
            global $wpdb;
            $table_name = $wpdb->prefix . 'actionscheduler_actions';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if (!$table_exists) {
                // error_log("[预览任务管理器] Action Scheduler数据库表不存在，尝试初始化");

                // 尝试初始化Action Scheduler
                if (class_exists('ActionScheduler')) {
                    ActionScheduler::init();
                    // error_log("[预览任务管理器] 尝试初始化Action Scheduler");

                    // 尝试安装数据库表
                    if (class_exists('ActionScheduler_HybridStore')) {
                        $store = new ActionScheduler_HybridStore();
                        $store->init();
                        // error_log("[预览任务管理器] 尝试初始化Action Scheduler存储");
                    }

                    // 再次检查表是否存在
                    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                    // error_log("[预览任务管理器] 初始化后检查表是否存在: " . ($table_exists ? '是' : '否'));
                }
            } else {
                // error_log("[预览任务管理器] Action Scheduler数据库表已存在");
            }

            return true;
        }

        // 尝试加载Action Scheduler库
        if (function_exists('ss_load_action_scheduler')) {
            $result = ss_load_action_scheduler();
            // error_log("[预览任务管理器] 尝试加载Action Scheduler库: " . ($result ? '成功' : '失败'));
            return $result;
        }

        // 尝试直接加载
        if (file_exists(WP_PLUGIN_DIR . '/woocommerce/packages/action-scheduler/action-scheduler.php')) {
            require_once WP_PLUGIN_DIR . '/woocommerce/packages/action-scheduler/action-scheduler.php';
            // error_log("[预览任务管理器] 直接加载Action Scheduler库");

            // 检查是否加载成功
            if (function_exists('as_schedule_single_action') && function_exists('as_get_scheduled_actions')) {
                // error_log("[预览任务管理器] Action Scheduler库加载成功");

                // 初始化Action Scheduler
                if (class_exists('ActionScheduler')) {
                    ActionScheduler::init();
                    // error_log("[预览任务管理器] 初始化Action Scheduler");

                    // 检查数据库表是否存在
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'actionscheduler_actions';
                    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

                    if (!$table_exists) {
                        // error_log("[预览任务管理器] Action Scheduler数据库表不存在，尝试初始化");

                        // 尝试安装数据库表
                        if (class_exists('ActionScheduler_HybridStore')) {
                            $store = new ActionScheduler_HybridStore();
                            $store->init();
                            // error_log("[预览任务管理器] 尝试初始化Action Scheduler存储");
                        }

                        // 再次检查表是否存在
                        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                        // error_log("[预览任务管理器] 初始化后检查表是否存在: " . ($table_exists ? '是' : '否'));
                    } else {
                        // error_log("[预览任务管理器] Action Scheduler数据库表已存在");
                    }
                }

                return true;
            } else {
                // error_log("[预览任务管理器] Action Scheduler库加载失败");
            }
        }

        return false;
    }

    /**
     * 初始化
     */
    public function init() {
        // 从设置中获取最大并行任务数
        $this->max_concurrent_tasks = get_option('ss_max_concurrent_preview_tasks', 10);

        // 再次尝试加载Action Scheduler库
        $this->load_action_scheduler();

        // 检查Action Scheduler库是否可用
        if (function_exists('as_schedule_single_action') && function_exists('as_get_scheduled_actions')) {
            // error_log("[预览任务管理器] 初始化完成，Action Scheduler库可用");
        } else {
            // error_log("[预览任务管理器] 初始化完成，Action Scheduler库不可用，将使用备选方案");
        }
    }

    /**
     * 设置最大并行任务数
     *
     * @param int $max_tasks 最大任务数
     */
    public function set_max_concurrent_tasks($max_tasks) {
        $max_tasks = intval($max_tasks);
        if ($max_tasks > 0) {
            $this->max_concurrent_tasks = $max_tasks;
            update_option('ss_max_concurrent_preview_tasks', $max_tasks);
        }
    }

    /**
     * 获取最大并行任务数
     *
     * @return int
     */
    public function get_max_concurrent_tasks() {
        return $this->max_concurrent_tasks;
    }

    /**
     * 添加SVG预览生成任务
     *
     * @param string $category_name 类目名称
     * @param string $svg_file SVG文件路径
     * @param int $index SVG索引
     * @param int $term_id 类目ID
     * @param string $priority 优先级 (high/normal)
     * @return int|bool 任务ID或false
     */
    public function add_preview_task($category_name, $svg_file, $index, $term_id, $priority = 'normal') {
        // 记录调试信息
        // error_log("[预览任务管理器] 添加预览任务: 类目={$category_name}, 文件=" . basename($svg_file) . ", 索引={$index}, 优先级={$priority}");

        // 检查文件是否存在
        if (!file_exists($svg_file)) {
            // error_log("[预览任务管理器] 错误: SVG文件不存在: {$svg_file}");
            return false;
        }

        // 再次尝试加载Action Scheduler库
        $as_loaded = $this->load_action_scheduler();
        // error_log("[预览任务管理器] Action Scheduler库加载结果: " . ($as_loaded ? '成功' : '失败'));

        // 检查Action Scheduler是否可用
        if (!function_exists('as_schedule_single_action') || !function_exists('as_get_scheduled_actions')) {
            // error_log("[预览任务管理器] Action Scheduler库不可用，使用WordPress原生调度");

            // 使用WordPress原生调度作为备选方案
            if ($priority === 'ultra_high') {
                $schedule_time = time(); // 立即执行
            } elseif ($priority === 'high') {
                $schedule_time = time();
            } else {
                $schedule_time = time() + 2;
            }
            $result = wp_schedule_single_event($schedule_time, 'ss_process_svg_preview', [
                $svg_file,
                $category_name,
                $index,
                $term_id
            ]);

            if ($result === false) {
                // error_log("[预览任务管理器] WordPress原生调度失败，尝试直接处理");
                // 直接处理
                $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                return true;
            }

            // error_log("[预览任务管理器] WordPress原生调度成功，计划时间: " . date('Y-m-d H:i:s', $schedule_time));
            return true;
        } else {
            // error_log("[预览任务管理器] Action Scheduler库可用，使用Action Scheduler添加任务");
        }

        // 创建任务组名称
        $group = $this->group_prefix . $term_id;

        // 设置任务优先级
        if ($priority === 'ultra_high') {
            $task_priority = 1; // 最高优先级
        } elseif ($priority === 'high') {
            $task_priority = 5;
        } else {
            $task_priority = 10;
        }

        try {
            // 检查是否已有相同任务
            $existing_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'args' => array($svg_file, $category_name, $index, $term_id),
                    'status' => array('pending', 'in-progress'),
                    'group' => $group
                )
            );

            if (!empty($existing_tasks)) {
                // error_log("[预览任务管理器] 任务已存在，跳过添加: 类目={$category_name}, 文件=" . basename($svg_file));
                return current($existing_tasks)->get_id();
            }

            // 检查数据库表是否存在
            global $wpdb;
            $table_name = $wpdb->prefix . 'actionscheduler_actions';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;

            if (!$table_exists) {
                // error_log("[预览任务管理器] Action Scheduler数据库表不存在，无法添加任务");

                // 尝试初始化Action Scheduler
                if (class_exists('ActionScheduler_AdminView')) {
                    // error_log("[预览任务管理器] 尝试使用ActionScheduler_AdminView::init_schema()初始化数据库表");
                    ActionScheduler_AdminView::init_schema();

                    // 再次检查表是否存在
                    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                    // error_log("[预览任务管理器] 初始化后检查表是否存在: " . ($table_exists ? '是' : '否'));

                    if (!$table_exists) {
                        // error_log("[预览任务管理器] 无法创建Action Scheduler数据库表，回退到直接处理");
                        $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                        return true;
                    }
                } else {
                    // error_log("[预览任务管理器] ActionScheduler_AdminView类不存在，回退到直接处理");
                    $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                    return true;
                }
            }

            // 添加任务到Action Scheduler
            if ($priority === 'ultra_high') {
                $schedule_time = time(); // 立即执行
            } elseif ($priority === 'high') {
                $schedule_time = time();
            } else {
                $schedule_time = time() + 2;
            }
            // error_log("[预览任务管理器] 尝试添加任务: 类目={$category_name}, 文件=" . basename($svg_file) . ", 计划时间=" . date('Y-m-d H:i:s', $schedule_time));

            try {
                $task_id = as_schedule_single_action(
                    $schedule_time,
                    'ss_process_svg_preview',
                    array($svg_file, $category_name, $index, $term_id),
                    $group,
                    array(
                        'priority' => $task_priority
                    )
                );

                if ($task_id) {
                    // error_log("[预览任务管理器] 成功添加任务 ID: {$task_id}, 类目={$category_name}, 文件=" . basename($svg_file));

                    // 尝试立即运行Action Scheduler
                    if (function_exists('as_unschedule_action') && function_exists('as_schedule_single_action')) {
                        // error_log("[预览任务管理器] 尝试触发Action Scheduler立即运行");

                        // 触发Action Scheduler运行
                        $run_id = as_schedule_single_action(time(), 'action_scheduler_run_queue');
                        // error_log("[预览任务管理器] 触发Action Scheduler运行，任务ID: " . ($run_id ? $run_id : '失败'));

                        // 尝试直接运行队列
                        if (class_exists('ActionScheduler_QueueRunner')) {
                            // error_log("[预览任务管理器] 尝试直接运行Action Scheduler队列");
                            $runner = ActionScheduler_QueueRunner::instance();
                            $runner->run();
                        }

                        // 尝试直接执行当前任务
                        // error_log("[预览任务管理器] 尝试直接执行当前任务: {$task_id}");
                        try {
                            // 获取任务详情
                            global $wpdb;
                            $task_details = $wpdb->get_row($wpdb->prepare(
                                "SELECT hook, args FROM {$wpdb->prefix}actionscheduler_actions WHERE action_id = %d",
                                $task_id
                            ));

                            if ($task_details && $task_details->hook === 'ss_process_svg_preview') {
                                // error_log("[预览任务管理器] 找到任务详情，准备直接执行");
                                $args = maybe_unserialize($task_details->args);
                                if (is_array($args) && count($args) >= 4) {
                                    // error_log("[预览任务管理器] 直接执行任务: " . basename($args[0]));
                                    // 直接执行任务
                                    $this->process_svg_preview($args[0], $args[1], $args[2], $args[3]);

                                    // 标记任务为已完成
                                    if (function_exists('as_mark_complete_action')) {
                                        as_mark_complete_action($task_id);
                                        // error_log("[预览任务管理器] 标记任务为已完成: {$task_id}");
                                    }
                                } else {
                                    // error_log("[预览任务管理器] 任务参数无效: " . print_r($args, true));
                                }
                            } else {
                                // error_log("[预览任务管理器] 未找到任务详情或钩子不匹配");
                            }
                        } catch (Exception $e) {
                            // error_log("[预览任务管理器] 直接执行任务时出错: " . $e->getMessage());
                        }
                    }

                    // 检查任务是否已添加到数据库
                    $task_exists = $wpdb->get_var($wpdb->prepare("SELECT action_id FROM {$wpdb->prefix}actionscheduler_actions WHERE action_id = %d", $task_id));
                    // error_log("[预览任务管理器] 检查任务是否已添加到数据库: " . ($task_exists ? '是' : '否'));

                    if (!$task_exists) {
                        // error_log("[预览任务管理器] 任务未添加到数据库，回退到直接处理");
                        $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                        return true;
                    }

                    return $task_id;
                } else {
                    // error_log("[预览任务管理器] 添加任务失败，尝试直接处理: 类目={$category_name}, 文件=" . basename($svg_file));
                    // 直接处理
                    $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                    return true;
                }
            } catch (Exception $e) {
                // error_log("[预览任务管理器] 添加任务时出错: " . $e->getMessage() . "\n" . $e->getTraceAsString());
                // 直接处理
                $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
                return true;
            }
        } catch (Exception $e) {
            // error_log("[预览任务管理器] 添加任务时出错: " . $e->getMessage());

            // 出错时使用WordPress原生调度作为备选方案
            if ($priority === 'ultra_high') {
                $schedule_time = time(); // 立即执行
            } elseif ($priority === 'high') {
                $schedule_time = time();
            } else {
                $schedule_time = time() + 2;
            }
            $result = wp_schedule_single_event($schedule_time, 'ss_process_svg_preview', [
                $svg_file,
                $category_name,
                $index,
                $term_id
            ]);

            // error_log("[预览任务管理器] 使用WordPress原生调度作为备选方案，结果: " . ($result ? '成功' : '失败'));

            if ($result === false) {
                // 如果WordPress原生调度也失败，尝试直接处理
                // error_log("[预览任务管理器] WordPress原生调度失败，尝试直接处理");
                $this->process_svg_preview($svg_file, $category_name, $index, $term_id);
            }

            return true;
        }
    }

    /**
     * 处理SVG预览生成
     *
     * @param string $svg_file SVG文件路径
     * @param string $category_name 类目名称
     * @param int $index SVG索引
     * @param int $term_id 类目ID
     */
    public function process_svg_preview($svg_file, $category_name, $index, $term_id) {
        // error_log("[预览任务管理器] 开始处理预览任务: 类目={$category_name}, 文件=" . basename($svg_file) . ", PID=" . getmypid());

        // 检查文件是否存在
        if (!file_exists($svg_file)) {
            // error_log("[预览任务管理器] 错误: SVG文件不存在: {$svg_file}");
            return false;
        }

        // 获取前端管理器实例
        $frontend_manager = SS_Frontend_Manager::get_instance();
        if (!$frontend_manager) {
            // error_log("[预览任务管理器] 错误: 无法获取前端管理器实例");
            return false;
        }

        // 获取类目预设颜色
        $preset_colors = get_term_meta($term_id, 'ss_category_colors', true);
        if (empty($preset_colors)) {
            error_log("[预览任务管理器] 类目没有预设颜色，尝试从图片提取");
            // 确保使用与后端相同的参数提取颜色
            $extracted_colors = $frontend_manager->extract_colors_from_category_image($term_id);

            if (!empty($extracted_colors)) {
                $preset_colors = $extracted_colors;
                error_log("[预览任务管理器] 从类目图片成功提取颜色: " . implode(', ', $preset_colors));

                // 更新类目的预设颜色
                update_term_meta($term_id, 'ss_category_colors', $preset_colors);
                error_log("[预览任务管理器] 更新类目预设颜色: " . implode(', ', $preset_colors));
            } else {
                error_log("[预览任务管理器] 无法从图片提取颜色，跳过该任务");
                return false; // 跳过没有颜色的任务
            }
        } else {
            error_log("[预览任务管理器] 使用已有的预设颜色: " . implode(', ', $preset_colors));
        }

        // 生成预览
        $start_time = microtime(true);
        // error_log("[预览任务管理器] 开始生成预览文件: 类目={$category_name}, 文件=" . basename($svg_file));

        // 【修复】检查目录权限 - 支持自定义匹配类目
        $upload_dir = wp_upload_dir();
        
        // 检查是否为自定义匹配类目
        $is_custom_matching = get_term_meta($term_id, '_is_custom_matching_category', true);
        if (!empty($is_custom_matching)) {
            $term = get_term($term_id);
            $processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/svg/';
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/previews/' . $term->slug . '/';
            error_log("[预览任务管理器][修复] 自定义匹配类目使用专用路径: SVG={$processed_svg_dir}, 预览={$preview_dir}");
        } else {
            $processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
            $preview_dir = $upload_dir['basedir'] . '/shoe-svg-generator/previews/';
        }

        // error_log("[预览任务管理器] 检查目录权限: processed_svg_dir=" .
        //          (file_exists($processed_svg_dir) ? '存在' : '不存在') .
        //          ", 权限=" . (file_exists($processed_svg_dir) ? substr(sprintf('%o', fileperms($processed_svg_dir)), -4) : 'N/A'));

        // error_log("[预览任务管理器] 检查目录权限: preview_dir=" .
        //          (file_exists($preview_dir) ? '存在' : '不存在') .
        //          ", 权限=" . (file_exists($preview_dir) ? substr(sprintf('%o', fileperms($preview_dir)), -4) : 'N/A'));

        // 确保目录存在
        if (!file_exists($processed_svg_dir)) {
            $created = wp_mkdir_p($processed_svg_dir);
            // error_log("[预览任务管理器] 创建processed_svg目录: " . ($created ? '成功' : '失败'));
            if ($created) {
                @chmod($processed_svg_dir, 0755);
            }
        }

        // 创建完整的类目目录结构并设置权限
        $this->create_category_directory_structure($category_name);

        // 调用前端管理器生成预览，并传递类目ID
        error_log("[预览任务管理器] 调用前端管理器生成预览，类目ID={$term_id}, 文件=" . basename($svg_file));
        $result = $frontend_manager->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);
        $end_time = microtime(true);
        $duration = round($end_time - $start_time, 2);

        if ($result) {
            // error_log("[预览任务管理器] 成功生成预览: 类目={$category_name}, 文件=" . basename($svg_file) . ", 耗时={$duration}秒");

            // 标记类目已生成SVG预览图
            $mark_result = $frontend_manager->mark_category_frontend_preview_generated($term_id);
            // error_log("[预览任务管理器] 标记类目已生成SVG预览图: " . ($mark_result ? '成功' : '失败'));
        } else {
            // error_log("[预览任务管理器] 生成预览失败: 类目={$category_name}, 文件=" . basename($svg_file));

            // 尝试直接调用Python脚本
            // error_log("[预览任务管理器] 尝试直接调用Python脚本作为备选方案");

            $python_script = WP_PLUGIN_DIR . '/shoe-svg-generator/python/process_svg.py';
            
            // 【修复】根据类目类型设置处理后SVG文件路径
            if (!empty($is_custom_matching)) {
                $term = get_term($term_id);
                $processed_svg_file = $processed_svg_dir . $term->slug . '/' . basename($svg_file);
                error_log("[预览任务管理器][修复] 自定义匹配类目SVG文件路径: {$processed_svg_file}");
            } else {
                $processed_svg_file = $processed_svg_dir . $category_name . '/' . basename($svg_file);
            }
            
            $color_str = ltrim($preset_colors[0], '#');

            // 确保目标目录存在（使用权限修复函数）
            $target_dir = dirname($processed_svg_file);
            if (!file_exists($target_dir)) {
                $this->create_directory_with_permissions($target_dir);
            } else {
                $this->ensure_www_permissions($target_dir, false);
            }

            // 构建命令
            $cmd = sprintf('python3 %s %s %s %s %s 2>&1',
                escapeshellarg($python_script),
                escapeshellarg($svg_file),
                escapeshellarg($processed_svg_file),
                escapeshellarg($color_str),
                escapeshellarg($category_name)
            );

            // error_log("[预览任务管理器] 执行Python命令: " . $cmd);
            $output = shell_exec($cmd);
            // error_log("[预览任务管理器] Python执行结果: " . $output);

            // 检查是否成功生成
            if (file_exists($processed_svg_file)) {
                // error_log("[预览任务管理器] 直接调用Python脚本成功生成处理后的SVG");

                // 再次尝试生成预览，并传递类目ID
                error_log("[预览任务管理器] 再次尝试生成预览，类目ID={$term_id}, 文件=" . basename($svg_file));
                $result = $frontend_manager->generate_svg_preview_file($svg_file, $category_name, $preset_colors[0], true, $term_id);
                // error_log("[预览任务管理器] 再次尝试生成预览结果: " . ($result ? '成功' : '失败'));
            }
        }

        // 清除处理标记
        $file_name = basename($svg_file);
        $safe_name = pathinfo($file_name, PATHINFO_FILENAME);
        $processing_marker = $upload_dir['basedir'] . '/shoe-svg-generator/processing/' .
                          $category_name . '_' . $safe_name . '.processing';

        if (file_exists($processing_marker)) {
            $unlink_result = @unlink($processing_marker);
            // error_log("[预览任务管理器] 清除处理标记: " . ($unlink_result ? '成功' : '失败') . " - {$processing_marker}");
        } else {
            // error_log("[预览任务管理器] 处理标记不存在: {$processing_marker}");
        }

        return $result;
    }

    /**
     * 获取类目的任务状态
     *
     * @param int $term_id 类目ID
     * @return array 任务状态数组
     */
    public function get_category_tasks_status($term_id) {
        // 检查Action Scheduler是否可用
        if (!function_exists('as_get_scheduled_actions')) {
            // error_log("[预览任务管理器] Action Scheduler库不可用，无法获取详细任务状态");
            return array(
                'pending' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'total' => 0
            );
        }

        try {
            $group = $this->group_prefix . $term_id;

            $pending_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'pending',
                    'group' => $group
                )
            );

            $in_progress_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'in-progress',
                    'group' => $group
                )
            );

            $completed_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'complete',
                    'group' => $group
                ),
                'ids'
            );

            return array(
                'pending' => count($pending_tasks),
                'in_progress' => count($in_progress_tasks),
                'completed' => count($completed_tasks),
                'total' => count($pending_tasks) + count($in_progress_tasks) + count($completed_tasks)
            );
        } catch (Exception $e) {
            // error_log("[预览任务管理器] 获取任务状态时出错: " . $e->getMessage());
            return array(
                'pending' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'total' => 0
            );
        }
    }

    /**
     * 获取所有任务的状态
     *
     * @return array 所有任务状态
     */
    public function get_all_tasks_status() {
        // 检查Action Scheduler是否可用
        if (!function_exists('as_get_scheduled_actions')) {
            // error_log("[预览任务管理器] Action Scheduler库不可用，无法获取详细任务状态");
            return array(
                'pending' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'total' => 0
            );
        }

        try {
            $pending_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'pending'
                )
            );

            $in_progress_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'in-progress'
                )
            );

            $completed_tasks = as_get_scheduled_actions(
                array(
                    'hook' => 'ss_process_svg_preview',
                    'status' => 'complete'
                ),
                'ids'
            );

            return array(
                'pending' => count($pending_tasks),
                'in_progress' => count($in_progress_tasks),
                'completed' => count($completed_tasks),
                'total' => count($pending_tasks) + count($in_progress_tasks) + count($completed_tasks)
            );
        } catch (Exception $e) {
            // error_log("[预览任务管理器] 获取任务状态时出错: " . $e->getMessage());
            return array(
                'pending' => 0,
                'in_progress' => 0,
                'completed' => 0,
                'total' => 0
            );
        }
    }


}