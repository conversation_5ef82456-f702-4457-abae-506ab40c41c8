<?php
/**
 * PNG并发处理专用CLI命令类
 * 
 * 专门为大数据量PNG处理优化的并发处理命令
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SS_PNG_Concurrent_CLI {
    
    /**
     * 大数据量PNG并发处理命令
     *
     * ## OPTIONS
     *
     * --template_id=<id>
     * : 模板产品ID
     *
     * [--max_parallel=<number>]
     * : 最大并发进程数 (默认: 16)
     *
     * [--batch_size=<number>]
     * : 每批处理文件数 (默认: 50)
     *
     * [--auto_adjust]
     * : 自动调整并发数基于系统性能
     *
     * [--folder=<name>]
     * : 指定处理的文件夹名称
     *
     * [--dry_run]
     * : 预览模式，不实际生成产品
     *
     * [--verbose]
     * : 显示详细信息
     *
     * [--white]
     * : 设置第一张画廊图背景为白色
     *
     * [--black]
     * : 设置第一张画廊图背景为黑色
     *
     * ## EXAMPLES
     *
     *     # 高并发处理所有PNG文件
     *     wp ss png_concurrent --template_id=324761 --max_parallel=32 --verbose
     *
     *     # 自动调整并发数
     *     wp ss png_concurrent --template_id=324761 --auto_adjust --verbose
     *
     *     # 处理指定文件夹
     *     wp ss png_concurrent --template_id=324761 --folder=test --max_parallel=16
     *
     *     # 设置白色背景
     *     wp ss png_concurrent --template_id=324761 --white --verbose
     *
     *     # 设置黑色背景
     *     wp ss png_concurrent --template_id=324761 --black --verbose
     *
     * @when after_wp_load
     */
    public function png_concurrent($args, $assoc_args) {
        // 【修复】设置环境变量避免第三方插件警告
        if (!isset($_SERVER['SERVER_PORT'])) {
            $_SERVER['SERVER_PORT'] = '80';
        }
        if (!isset($_SERVER['HTTP_HOST'])) {
            $_SERVER['HTTP_HOST'] = 'localhost';
        }

        $template_id = intval($assoc_args['template_id'] ?? 0);
        $max_parallel = intval($assoc_args['max_parallel'] ?? 16);
        $batch_size = intval($assoc_args['batch_size'] ?? 50);
        $auto_adjust = isset($assoc_args['auto_adjust']);
        $target_folder = $assoc_args['folder'] ?? null;
        $dry_run = isset($assoc_args['dry_run']);
        $verbose = isset($assoc_args['verbose']);

        // 背景色参数处理
        $background_color = 'transparent'; // 默认透明
        if (isset($assoc_args['white'])) {
            $background_color = 'white';
            error_log('[PNG并发处理][CLI] 检测到--white参数，设置背景色为: white');
        } elseif (isset($assoc_args['black'])) {
            $background_color = 'black';
            error_log('[PNG并发处理][CLI] 检测到--black参数，设置背景色为: black');
        } else {
            error_log('[PNG并发处理][CLI] 未检测到背景色参数，使用默认透明背景');
        }

        error_log('[PNG并发处理][CLI] 最终背景色设置: ' . $background_color);
        
        if (!$template_id) {
            WP_CLI::error('请提供模板产品ID (--template_id)');
        }
        
        // 验证模板产品
        $template_product = wc_get_product($template_id);
        if (!$template_product) {
            WP_CLI::error('模板产品不存在: ' . $template_id);
        }
        
        WP_CLI::log('🚀 PNG大数据量并发处理开始');
        WP_CLI::log('模板产品: ' . $template_product->get_name() . ' (ID: ' . $template_id . ')');
        WP_CLI::log('画廊图背景色: ' . $background_color);
        WP_CLI::log('并发配置: 最大并行=' . $max_parallel . ', 批次大小=' . $batch_size);
        WP_CLI::log('自动调整: ' . ($auto_adjust ? '启用' : '禁用'));
        
        if ($dry_run) {
            WP_CLI::log('🔍 预览模式 - 不会实际生成产品');
        }
        
        $start_time = microtime(true);
        
        // 检查并发处理支持
        if (!SS_Safe_Concurrent_Processor::is_supported()) {
            WP_CLI::error('系统不支持安全并发处理');
        }
        
        // 初始化处理器
        $png_manager = SS_PNG_File_Manager::get_instance();
        $concurrent_processor = SS_Safe_Concurrent_Processor::get_instance();
        
        // 扫描PNG文件
        $directory_structure = $png_manager->scan_png_directory_structure();
        
        if (empty($directory_structure)) {
            WP_CLI::error('未找到PNG文件夹');
        }
        
        // 过滤目标文件夹
        if ($target_folder) {
            if (!isset($directory_structure[$target_folder])) {
                WP_CLI::error('指定的文件夹不存在: ' . $target_folder);
            }
            $directory_structure = array($target_folder => $directory_structure[$target_folder]);
        }
        
        // 提前检查并跳过已完全处理的文件夹 - 使用更高效的方法
        $folders_to_process = array();
        $total_original_folders = count($directory_structure);
        
        WP_CLI::log('🔍 正在检查已处理的文件夹...');
        
        foreach ($directory_structure as $folder_path => $folder_info) {
            $category_id = $png_manager->get_or_create_category($folder_path);
            if (!$category_id) {
                WP_CLI::warning('  ❌ 无法为文件夹创建类目: ' . $folder_path);
                continue; // 跳过无法创建类目的文件夹
            }
            
            // 使用专门的检查方法 - 更高效
            $is_fully_processed = $png_manager->is_folder_fully_processed($folder_path, $category_id, $template_id);
            $total_files_in_folder = isset($folder_info['file_count']) ? $folder_info['file_count'] : 0;
            
            if ($is_fully_processed && $total_files_in_folder > 0) {
                $category_term = get_term($category_id, 'product_cat');
                $category_hierarchy = $this->get_category_hierarchy($category_term);
                WP_CLI::log('⏭️  跳过已处理完成的文件夹: ' . $folder_path . ' (' . $category_hierarchy . ')');
            } else {
                $unprocessed_count = $png_manager->get_unprocessed_files_count($folder_path, $category_id, $template_id);
                if ($verbose || $total_files_in_folder > 0) {
                    $processed_count = $total_files_in_folder - $unprocessed_count;
                    WP_CLI::log('  📂 需要处理: ' . $folder_path . ' (' . $processed_count . '/' . $total_files_in_folder . ' 已处理，剩余: ' . $unprocessed_count . ' 文件)');
                }
                $folders_to_process[$folder_path] = $folder_info;
            }
        }
        
        $skipped_folders = $total_original_folders - count($folders_to_process);
        
        if (empty($folders_to_process)) {
            WP_CLI::success("🎉 所有 {$total_original_folders} 个文件夹均已处理完成！");
            return;
        }
        
        $directory_structure = $folders_to_process;
        WP_CLI::log("📊 跳过统计: 跳过 {$skipped_folders} 个已处理文件夹，实际需要处理: " . count($directory_structure) . " 个文件夹");
        
        $total_files = 0;
        $total_processed = 0;
        $total_created = 0;
        $total_skipped = 0;
        
        foreach ($directory_structure as $folder_path => $folder_info) {
            WP_CLI::log('');
            WP_CLI::log('📂 处理文件夹: ' . $folder_path . ' (' . $folder_info['file_count'] . ' 个文件)');

            // 获取或创建类目（支持层级结构）
            $category_id = $png_manager->get_or_create_category($folder_path);
            if (!$category_id) {
                WP_CLI::warning('无法创建类目: ' . $folder_path);
                continue;
            }

            $category_term = get_term($category_id, 'product_cat');
            $category_name = $category_term->name;

            // 显示类目层级信息
            $category_hierarchy = $this->get_category_hierarchy($category_term);
            WP_CLI::log('  ✅ 类目: ' . $category_hierarchy . ' (ID: ' . $category_id . ')');

            // 生成或获取JSON记录（使用完整的相对路径）
            $folder_relative_path = $folder_info['relative_path']; // 使用完整的相对路径
            $folder_name = $folder_info['folder_name']; // 保留文件夹名称用于显示

            $json_record = $png_manager->get_folder_json_record($folder_relative_path, $category_id);
            if (!$json_record && !$dry_run) {
                $result = $png_manager->generate_folder_json_record($folder_relative_path, $category_id);
                if (!$result['success']) {
                    WP_CLI::warning('无法生成JSON记录: ' . $folder_relative_path . ' - ' . $result['message']);
                    continue;
                }
                $json_record = $png_manager->get_folder_json_record($folder_relative_path, $category_id);
            }

            // 获取未处理文件（使用完整的相对路径）
            $unprocessed_files = $png_manager->get_unprocessed_files($folder_relative_path, $category_id, $template_id);
            
            if (empty($unprocessed_files)) {
                WP_CLI::log('  ✅ 所有文件已处理完成');
                continue;
            }
            
            WP_CLI::log('  📋 未处理文件: ' . count($unprocessed_files) . ' 个');
            $total_files += count($unprocessed_files);
            
            if ($dry_run) {
                WP_CLI::log('  🔍 预览模式 - 将要处理的文件:');
                foreach (array_slice($unprocessed_files, 0, 10) as $file_info) {
                    WP_CLI::log('    - ' . $file_info['filename']);
                }
                if (count($unprocessed_files) > 10) {
                    WP_CLI::log('    ... 还有 ' . (count($unprocessed_files) - 10) . ' 个文件');
                }
                continue;
            }
            
            // 分批处理
            $batches = array_chunk($unprocessed_files, $batch_size);
            $folder_processed = 0;
            $folder_created = 0;
            $folder_skipped = 0;
            
            foreach ($batches as $batch_index => $batch_files) {
                $batch_start_time = microtime(true);
                
                WP_CLI::log('  🔄 处理批次 ' . ($batch_index + 1) . '/' . count($batches) . ' (' . count($batch_files) . ' 个文件)');
                
                // 性能监控和自动调整
                if ($auto_adjust) {
                    $performance = $concurrent_processor->get_system_performance();
                    $adjusted_parallel = $concurrent_processor->adjust_concurrent_count($max_parallel, $performance);
                    
                    if ($adjusted_parallel != $max_parallel && $verbose) {
                        WP_CLI::log('    🎛️ 自动调整并发数: ' . $max_parallel . ' → ' . $adjusted_parallel);
                        $max_parallel = $adjusted_parallel;
                    }
                }
                
                // 并发处理批次（传递完整的相对路径）
                $batch_results = $concurrent_processor->process_png_files_safe(
                    $batch_files,
                    $template_id,
                    $category_id,
                    $category_name,
                    $folder_relative_path, // 使用完整的相对路径
                    $max_parallel,
                    $background_color,
                    $verbose
                );
                
                // 统计结果
                $batch_created = 0;
                $batch_skipped = 0;
                
                foreach ($batch_results as $result) {
                    if ($result['success']) {
                        // 更新JSON记录（使用完整的相对路径）
                        $png_manager->update_file_processed_status(
                            $folder_relative_path,
                            $category_id,
                            $result['filename'],
                            $template_id,
                            $result['product_id']
                        );
                        
                        $batch_created++;
                        $folder_created++;
                        
                        if ($verbose) {
                            WP_CLI::log('    ✅ 成功: ' . $result['filename'] . ' (ID: ' . $result['product_id'] . ') - ' . round($result['total_duration'], 2) . 's');
                        }
                    } else {
                        $batch_skipped++;
                        $folder_skipped++;
                        
                        if ($verbose) {
                            WP_CLI::log('    ❌ 失败: ' . $result['filename'] . ' - ' . $result['error']);
                        }
                    }
                    $folder_processed++;
                }
                
                $batch_duration = microtime(true) - $batch_start_time;
                WP_CLI::log('    📊 批次完成: 创建 ' . $batch_created . ' 个，跳过 ' . $batch_skipped . ' 个 - 耗时: ' . round($batch_duration, 2) . 's');
                
                // 批次间短暂休息
                if ($batch_index < count($batches) - 1) {
                    sleep(1);
                }
            }
            
            $total_processed += $folder_processed;
            $total_created += $folder_created;
            $total_skipped += $folder_skipped;
            
            WP_CLI::log('  📊 文件夹完成: 处理 ' . $folder_processed . ' 个，创建 ' . $folder_created . ' 个产品');
        }
        
        // 清理临时文件
        $concurrent_processor->cleanup_temp_files();
        
        $total_duration = microtime(true) - $start_time;
        
        WP_CLI::log('');
        WP_CLI::log('🎉 PNG大数据量并发处理完成！');
        WP_CLI::log('');
        WP_CLI::log('📊 最终统计:');
        WP_CLI::log('  总文件数: ' . $total_files);
        WP_CLI::log('  处理文件: ' . $total_processed);
        WP_CLI::log('  创建产品: ' . $total_created);
        WP_CLI::log('  跳过文件: ' . $total_skipped);
        WP_CLI::log('  总耗时: ' . round($total_duration, 2) . ' 秒');
        
        if ($total_created > 0) {
            $avg_time = $total_duration / $total_created;
            WP_CLI::log('  平均耗时: ' . round($avg_time, 2) . ' 秒/产品');
        }

        WP_CLI::success('PNG大数据量并发处理任务成功完成！');
    }

    /**
     * 获取类目层级显示字符串
     *
     * @param object $term 类目对象
     * @return string 层级字符串
     */
    private function get_category_hierarchy($term) {
        $hierarchy = array();
        $current_term = $term;

        // 向上遍历获取完整层级
        while ($current_term) {
            array_unshift($hierarchy, $current_term->name);

            if ($current_term->parent > 0) {
                $current_term = get_term($current_term->parent, 'product_cat');
                if (is_wp_error($current_term)) {
                    break;
                }
            } else {
                break;
            }
        }

        return implode(' > ', $hierarchy);
    }
}

// 注册WP-CLI命令
if (defined('WP_CLI') && WP_CLI) {
    WP_CLI::add_command('ss png_concurrent', array('SS_PNG_Concurrent_CLI', 'png_concurrent'));
}
