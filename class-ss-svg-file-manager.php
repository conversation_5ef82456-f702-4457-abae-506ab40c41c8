<?php
/**
 * SVG 文件管理器类
 * 
 * 负责管理 SVG 文件的 JSON 记录、OSS 转移和文件验证功能
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SS_SVG_File_Manager {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * JSON 记录文件路径
     */
    private $json_records_dir;
    
    /**
     * processed_svg 目录路径
     */
    private $processed_svg_dir;
    
    /**
     * Rclone 配置名称
     */
    private $rclone_remote = 'aliyun';

    /**
     * OSS bucket 名称
     */
    private $oss_bucket = 'oss-svg';

    /**
     * 构造函数
     */
    private function __construct() {
        $upload_dir = wp_upload_dir();
        $this->processed_svg_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_svg/';
        $this->json_records_dir = $upload_dir['basedir'] . '/shoe-svg-generator/svg-records/';

        // 确保 JSON 记录目录存在
        if (!file_exists($this->json_records_dir)) {
            wp_mkdir_p($this->json_records_dir);
        }
    }
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 生成类目的 JSON 记录文件
     * 
     * @param int $category_id 类目ID
     * @return array 操作结果
     */
    public function generate_category_json_record($category_id) {
        error_log("[SVG文件管理] 开始为类目 {$category_id} 生成JSON记录");
        
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }
        
        $safe_category_name = sanitize_title($term->name);
        $category_svg_dir = $this->processed_svg_dir . $safe_category_name . '/';
        
        // 获取类目目录中的所有 SVG 文件
        $svg_files = array();
        if (is_dir($category_svg_dir)) {
            $files = glob($category_svg_dir . '*.svg');
            foreach ($files as $file) {
                $filename = basename($file);
                $svg_files[] = array(
                    'filename' => $filename,
                    'original_path' => $file,
                    'file_size' => filesize($file),
                    'created_time' => filemtime($file),
                    'migrated_to_oss' => false,
                    'migration_time' => null
                );
            }
        }
        
        // 检查根目录中的文件（兼容旧版本）
        $root_files = glob($this->processed_svg_dir . '*.svg');
        foreach ($root_files as $file) {
            $filename = basename($file);
            // 检查是否属于当前类目（通过文件名或其他方式判断）
            if ($this->is_file_belongs_to_category($filename, $category_id)) {
                $svg_files[] = array(
                    'filename' => $filename,
                    'original_path' => $file,
                    'file_size' => filesize($file),
                    'created_time' => filemtime($file),
                    'migrated_to_oss' => false,
                    'migration_time' => null
                );
            }
        }
        
        // 生成 JSON 记录
        $json_data = array(
            'category_id' => $category_id,
            'category_name' => $term->name,
            'safe_category_name' => $safe_category_name,
            'total_files' => count($svg_files),
            'generated_time' => current_time('mysql'),
            'files' => $svg_files
        );
        
        // 保存 JSON 文件
        $json_filename = "category_{$category_id}_{$safe_category_name}.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        if (file_put_contents($json_filepath, json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            error_log("[SVG文件管理] 成功生成JSON记录: {$json_filepath}，包含 " . count($svg_files) . " 个文件");
            return array(
                'success' => true, 
                'message' => "成功生成JSON记录，包含 " . count($svg_files) . " 个文件",
                'json_file' => $json_filepath,
                'files_count' => count($svg_files)
            );
        } else {
            error_log("[SVG文件管理] 无法保存JSON记录文件: {$json_filepath}");
            return array('success' => false, 'message' => '无法保存JSON记录文件');
        }
    }
    
    /**
     * 检查文件是否属于指定类目
     * 
     * @param string $filename 文件名
     * @param int $category_id 类目ID
     * @return bool
     */
    private function is_file_belongs_to_category($filename, $category_id) {
        // 通过数据库查询检查是否有产品使用了这个SVG文件并属于指定类目
        global $wpdb;
        
        $query = $wpdb->prepare("
            SELECT COUNT(*) 
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            INNER JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
            WHERE p.post_type = 'product'
            AND pm.meta_key = '_ss_svg_filename'
            AND pm.meta_value = %s
            AND tr.term_taxonomy_id = %d
        ", $filename, $category_id);
        
        $count = $wpdb->get_var($query);
        return $count > 0;
    }
    
    /**
     * 获取类目的 JSON 记录
     * 
     * @param int $category_id 类目ID
     * @return array|false JSON 数据或 false
     */
    public function get_category_json_record($category_id) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return false;
        }
        
        $safe_category_name = sanitize_title($term->name);
        $json_filename = "category_{$category_id}_{$safe_category_name}.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        if (file_exists($json_filepath)) {
            $json_content = file_get_contents($json_filepath);
            return json_decode($json_content, true);
        }
        
        return false;
    }
    
    /**
     * 检查 SVG 文件是否存在（基于 JSON 记录）
     *
     * @param string $filename SVG 文件名
     * @param int $category_id 类目ID
     * @return bool
     */
    public function svg_file_exists($filename, $category_id) {
        $json_record = $this->get_category_json_record($category_id);

        if (!$json_record || !isset($json_record['files'])) {
            // 如果没有 JSON 记录，回退到直接文件检查
            return $this->fallback_file_exists_check($filename, $category_id);
        }

        // 在 JSON 记录中查找文件
        foreach ($json_record['files'] as $file_info) {
            if ($file_info['filename'] === $filename) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取 SVG 文件的实际路径（优先从 OSS，回退到本地）
     *
     * @param string $filename SVG 文件名
     * @param int $category_id 类目ID
     * @return string|false 文件路径或 false
     */
    public function get_svg_file_path($filename, $category_id) {
        $json_record = $this->get_category_json_record($category_id);

        if ($json_record && isset($json_record['files'])) {
            // 在 JSON 记录中查找文件
            foreach ($json_record['files'] as $file_info) {
                if ($file_info['filename'] === $filename) {
                    // 如果文件已转移到 OSS，优先使用原始路径（因为OSS文件不在本地）
                    if ($file_info['migrated_to_oss']) {
                        error_log("[SVG文件管理] 文件 {$filename} 已转移到OSS，使用原始路径: {$file_info['original_path']}");
                        if (file_exists($file_info['original_path'])) {
                            return $file_info['original_path'];
                        } else {
                            error_log("[SVG文件管理] 警告：已转移文件的原始路径不存在: {$file_info['original_path']}");
                        }
                    }

                    // 检查原始路径
                    if (file_exists($file_info['original_path'])) {
                        return $file_info['original_path'];
                    }
                }
            }
        }

        // 回退到传统的文件查找
        return $this->fallback_get_file_path($filename, $category_id);
    }

    /**
     * 回退的文件路径获取（兼容性）
     *
     * @param string $filename SVG 文件名
     * @param int $category_id 类目ID
     * @return string|false 文件路径或 false
     */
    private function fallback_get_file_path($filename, $category_id) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            error_log("[SVG文件管理] 回退查找失败：类目 {$category_id} 不存在");
            return false;
        }

        $safe_category_name = sanitize_title($term->name);

        // 【修复】检查是否为自定义匹配类目，使用专用路径
        $is_custom_matching = get_term_meta($category_id, '_is_custom_matching_category', true);
        if (!empty($is_custom_matching)) {
            $upload_dir = wp_upload_dir();
            // 【修复】自定义匹配类目使用slug而不是name，确保路径一致性
            $custom_matching_dir = $upload_dir['basedir'] . '/shoe-svg-generator/custom-matching/svg/' . $term->slug . '/';
            $custom_file = $custom_matching_dir . $filename;

            error_log("[SVG文件管理] 【修复】检查自定义匹配文件: {$custom_file}");

            if (file_exists($custom_file)) {
                error_log("[SVG文件管理] 【修复】在自定义匹配目录找到文件: {$filename}");
                return $custom_file;
            } else {
                // 【修复】如果自定义匹配目录中没有文件，检查是否在默认目录中存在
                $fallback_file = $this->processed_svg_dir . $filename;
                $category_fallback_file = $this->processed_svg_dir . $safe_category_name . '/' . $filename;

                error_log("[SVG文件管理] 【修复】自定义匹配文件不存在，检查回退路径");
                error_log("[SVG文件管理] 【修复】回退路径1: {$fallback_file}");
                error_log("[SVG文件管理] 【修复】回退路径2: {$category_fallback_file}");

                $source_file = null;
                if (file_exists($category_fallback_file)) {
                    $source_file = $category_fallback_file;
                } elseif (file_exists($fallback_file)) {
                    $source_file = $fallback_file;
                }

                if ($source_file) {
                    error_log("[SVG文件管理] 【修复】在默认目录找到文件: {$source_file}");

                    // 确保自定义匹配目录存在
                    if (!file_exists($custom_matching_dir)) {
                        wp_mkdir_p($custom_matching_dir);
                        error_log("[SVG文件管理] 【修复】创建自定义匹配目录: {$custom_matching_dir}");
                    }

                    // 复制文件到自定义匹配目录
                    if (copy($source_file, $custom_file)) {
                        error_log("[SVG文件管理] 【修复】文件复制成功: {$filename}");
                        return $custom_file;
                    } else {
                        error_log("[SVG文件管理] 【修复】文件复制失败: {$filename}");
                        return $source_file; // 返回原始文件路径
                    }
                }
            }

            // 【修复】自定义匹配类目如果找不到文件，直接返回false，不继续查找普通类目路径
            error_log("[SVG文件管理] 【修复】自定义匹配类目 {$category_id} 未找到文件: {$filename}");
            return false;
        }

        // 检查类目特定目录
        $category_file = $this->processed_svg_dir . $safe_category_name . '/' . $filename;
        if (file_exists($category_file)) {
            error_log("[SVG文件管理] 回退查找成功：在类目目录找到文件 {$filename}");
            return $category_file;
        }

        // 检查根目录
        $root_file = $this->processed_svg_dir . $filename;
        if (file_exists($root_file)) {
            error_log("[SVG文件管理] 回退查找成功：在根目录找到文件 {$filename}");
            return $root_file;
        }

        error_log("[SVG文件管理] 回退查找失败：未找到文件 {$filename}");
        return false;
    }
    
    /**
     * 回退的文件存在性检查（兼容性）
     * 
     * @param string $filename SVG 文件名
     * @param int $category_id 类目ID
     * @return bool
     */
    private function fallback_file_exists_check($filename, $category_id) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return false;
        }
        
        $safe_category_name = sanitize_title($term->name);
        
        // 检查类目特定目录
        $category_file = $this->processed_svg_dir . $safe_category_name . '/' . $filename;
        if (file_exists($category_file)) {
            return true;
        }
        
        // 检查根目录
        $root_file = $this->processed_svg_dir . $filename;
        if (file_exists($root_file)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 使用 Rclone 将类目的 SVG 文件同步到 OSS
     *
     * @param int $category_id 类目ID
     * @return array 操作结果
     */
    public function sync_category_to_oss_rclone($category_id) {
        error_log("[SVG文件管理-Rclone] 开始将类目 {$category_id} 的文件同步到OSS");

        $json_record = $this->get_category_json_record($category_id);
        if (!$json_record) {
            error_log("[SVG文件管理-Rclone] 错误：未找到类目 {$category_id} 的JSON记录");
            return array('success' => false, 'message' => '未找到类目的JSON记录，请先生成记录');
        }

        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            error_log("[SVG文件管理-Rclone] 错误：类目 {$category_id} 不存在");
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $local_category_dir = $this->processed_svg_dir . $safe_category_name . '/';

        // 检查本地类目目录是否存在
        if (!file_exists($local_category_dir)) {
            error_log("[SVG文件管理-Rclone] 警告：本地类目目录不存在，跳过同步: {$local_category_dir}");
            return array(
                'success' => true,
                'message' => "本地类目目录不存在，跳过同步: {$safe_category_name}",
                'migrated_count' => 0,
                'already_migrated' => 0,
                'error_count' => 0,
                'rclone_output' => '目录不存在，已跳过'
            );
        }

        // 构建 OSS 目标路径
        $oss_target_path = "{$this->rclone_remote}:{$this->oss_bucket}/{$safe_category_name}/";

        // 执行 Rclone 同步命令 - 修复引号问题
        $rclone_command = sprintf(
            'rclone sync %s %s --progress --log-level INFO 2>&1',
            escapeshellarg($local_category_dir),
            escapeshellarg($oss_target_path)
        );

        error_log("[SVG文件管理-Rclone] 执行命令: {$rclone_command}");

        $output = array();
        $return_code = 0;
        exec($rclone_command, $output, $return_code);

        $output_str = implode("\n", $output);
        error_log("[SVG文件管理-Rclone] 命令输出: {$output_str}");
        error_log("[SVG文件管理-Rclone] 返回代码: {$return_code}");

        if ($return_code === 0) {
            // 同步成功，更新 JSON 记录
            $migrated_count = 0;
            foreach ($json_record['files'] as &$file_info) {
                if (!$file_info['migrated_to_oss']) {
                    $file_info['migrated_to_oss'] = true;
                    $file_info['migration_time'] = current_time('mysql');
                    $file_info['oss_path'] = $oss_target_path . $file_info['filename'];
                    $migrated_count++;
                }
            }

            // 更新 JSON 记录
            $this->update_category_json_record($category_id, $json_record);

            $message = "Rclone同步完成：{$migrated_count} 个文件已同步到OSS";
            error_log("[SVG文件管理-Rclone] " . $message);

            return array(
                'success' => true,
                'message' => $message,
                'migrated_count' => $migrated_count,
                'already_migrated' => 0,
                'error_count' => 0,
                'rclone_output' => $output_str
            );
        } else {
            $error_message = "Rclone同步失败，返回代码: {$return_code}，输出: {$output_str}";
            error_log("[SVG文件管理-Rclone] " . $error_message);

            return array(
                'success' => false,
                'message' => $error_message,
                'migrated_count' => 0,
                'already_migrated' => 0,
                'error_count' => 1,
                'rclone_output' => $output_str
            );
        }
    }

    /**
     * 将类目的 SVG 文件转移到 OSS（保留原方法以兼容性）
     *
     * @param int $category_id 类目ID
     * @return array 操作结果
     */
    public function migrate_category_to_oss($category_id) {
        // 调用新的 Rclone 同步方法
        return $this->sync_category_to_oss_rclone($category_id);
    }
    
    /**
     * 更新类目的 JSON 记录
     * 
     * @param int $category_id 类目ID
     * @param array $json_data JSON 数据
     * @return bool
     */
    private function update_category_json_record($category_id, $json_data) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return false;
        }
        
        $safe_category_name = sanitize_title($term->name);
        $json_filename = "category_{$category_id}_{$safe_category_name}.json";
        $json_filepath = $this->json_records_dir . $json_filename;
        
        return file_put_contents($json_filepath, json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) !== false;
    }
    
    /**
     * 获取所有类目的 JSON 记录文件
     * 
     * @return array
     */
    public function get_all_json_records() {
        $json_files = glob($this->json_records_dir . 'category_*.json');
        $records = array();
        
        foreach ($json_files as $json_file) {
            $json_content = file_get_contents($json_file);
            $data = json_decode($json_content, true);
            if ($data) {
                $records[] = $data;
            }
        }
        
        return $records;
    }
    
    /**
     * 清理已转移文件的本地副本（谨慎使用）
     * 
     * @param int $category_id 类目ID
     * @param bool $dry_run 是否为试运行
     * @return array 操作结果
     */
    public function cleanup_migrated_files($category_id, $dry_run = true) {
        error_log("[SVG文件管理] 开始清理类目 {$category_id} 的已转移文件（试运行: " . ($dry_run ? '是' : '否') . "）");
        
        $json_record = $this->get_category_json_record($category_id);
        if (!$json_record) {
            return array('success' => false, 'message' => '未找到类目的JSON记录');
        }
        
        $cleanup_count = 0;
        $files_to_cleanup = array();
        
        foreach ($json_record['files'] as $file_info) {
            if ($file_info['migrated_to_oss'] && file_exists($file_info['original_path'])) {
                $files_to_cleanup[] = $file_info['original_path'];
                if (!$dry_run) {
                    if (unlink($file_info['original_path'])) {
                        $cleanup_count++;
                        error_log("[SVG文件管理] 已删除本地文件: {$file_info['original_path']}");
                    } else {
                        error_log("[SVG文件管理] 无法删除本地文件: {$file_info['original_path']}");
                    }
                }
            }
        }
        
        if ($dry_run) {
            $message = "试运行完成：发现 " . count($files_to_cleanup) . " 个文件可以清理";
        } else {
            $message = "清理完成：成功删除 {$cleanup_count} 个本地文件";
        }
        
        return array(
            'success' => true,
            'message' => $message,
            'files_to_cleanup' => $files_to_cleanup,
            'cleanup_count' => $cleanup_count
        );
    }

    /**
     * 在产品生成后自动更新类目的JSON记录
     * 【修复】优化单个文件添加逻辑
     *
     * @param int $category_id 类目ID
     * @param string $svg_filename SVG文件名（可选，如果提供则只更新该文件）
     * @return bool
     */
    public function auto_update_json_record_after_product_generation($category_id, $svg_filename = null) {
        error_log("[SVG文件管理][修复] 产品生成后自动更新JSON记录: 类目ID={$category_id}, SVG文件={$svg_filename}");

        // 检查是否已有JSON记录
        $existing_record = $this->get_category_json_record($category_id);

        if (!$existing_record) {
            // 如果没有JSON记录，生成新的
            error_log("[SVG文件管理][修复] 未找到现有JSON记录，生成新记录");
            $result = $this->generate_category_json_record($category_id);
            return $result['success'];
        }

        // 【修复】如果指定了特定的SVG文件名，优先处理该文件
        if (!empty($svg_filename)) {
            error_log("[SVG文件管理][修复] 处理特定SVG文件: {$svg_filename}");
            return $this->add_single_svg_to_json_record($category_id, $svg_filename, $existing_record);
        }

        // 如果有现有记录，更新它
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            error_log("[SVG文件管理][修复] 类目不存在: {$category_id}");
            return false;
        }

        $safe_category_name = sanitize_title($term->name);
        $category_svg_dir = $this->processed_svg_dir . $safe_category_name . '/';

        // 获取当前目录中的所有SVG文件
        $current_files = array();
        if (is_dir($category_svg_dir)) {
            $files = glob($category_svg_dir . '*.svg');
            foreach ($files as $file) {
                $filename = basename($file);
                $current_files[$filename] = array(
                    'filename' => $filename,
                    'original_path' => $file,
                    'file_size' => filesize($file),
                    'created_time' => filemtime($file),
                    'migrated_to_oss' => false,
                    'migration_time' => null
                );
            }
        }

        // 合并现有记录和新文件
        $updated_files = array();
        $existing_files_map = array();

        // 先处理现有记录
        foreach ($existing_record['files'] as $file_info) {
            $existing_files_map[$file_info['filename']] = $file_info;
        }

        // 更新或添加文件
        foreach ($current_files as $filename => $file_info) {
            if (isset($existing_files_map[$filename])) {
                // 文件已存在，保持OSS状态，但更新其他信息
                $updated_file = $existing_files_map[$filename];
                $updated_file['file_size'] = $file_info['file_size'];
                $updated_file['created_time'] = $file_info['created_time'];
                $updated_file['original_path'] = $file_info['original_path'];
                $updated_files[] = $updated_file;
            } else {
                // 新文件
                $updated_files[] = $file_info;
                error_log("[SVG文件管理] 发现新SVG文件: {$filename}");
            }
        }

        // 保留已转移但本地不存在的文件记录
        foreach ($existing_files_map as $filename => $file_info) {
            if (!isset($current_files[$filename]) && $file_info['migrated_to_oss']) {
                $updated_files[] = $file_info;
                error_log("[SVG文件管理] 保留已转移的文件记录: {$filename}");
            }
        }

        // 更新JSON记录
        $updated_record = $existing_record;
        $updated_record['total_files'] = count($updated_files);
        $updated_record['files'] = $updated_files;
        $updated_record['last_updated'] = current_time('mysql');

        $success = $this->update_category_json_record($category_id, $updated_record);

        if ($success) {
            error_log("[SVG文件管理] 成功更新JSON记录: 类目ID={$category_id}, 总文件数=" . count($updated_files));
        } else {
            error_log("[SVG文件管理] 更新JSON记录失败: 类目ID={$category_id}");
        }

        return $success;
    }

    /**
     * 【新增】将单个SVG文件添加到JSON记录中
     *
     * @param int $category_id 类目ID
     * @param string $svg_filename SVG文件名
     * @param array $existing_record 现有的JSON记录
     * @return bool
     */
    private function add_single_svg_to_json_record($category_id, $svg_filename, $existing_record) {
        error_log("[SVG文件管理][新增] 添加单个SVG文件到JSON记录: 类目ID={$category_id}, 文件={$svg_filename}");

        // 检查文件是否已在记录中
        foreach ($existing_record['files'] as $file_info) {
            if ($file_info['filename'] === $svg_filename) {
                error_log("[SVG文件管理][新增] SVG文件 '{$svg_filename}' 已在JSON记录中，跳过添加");
                return true; // 已存在，返回成功
            }
        }

        // 获取类目信息
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            error_log("[SVG文件管理][新增] 类目不存在: {$category_id}");
            return false;
        }

        $safe_category_name = sanitize_title($term->name);
        $category_svg_dir = $this->processed_svg_dir . $safe_category_name . '/';
        $svg_file_path = $category_svg_dir . $svg_filename;

        // 检查文件是否存在
        if (!file_exists($svg_file_path)) {
            // 尝试在根目录查找
            $root_svg_path = $this->processed_svg_dir . $svg_filename;
            if (file_exists($root_svg_path)) {
                $svg_file_path = $root_svg_path;
                error_log("[SVG文件管理][新增] 在根目录找到SVG文件: {$root_svg_path}");
            } else {
                error_log("[SVG文件管理][新增] SVG文件不存在: {$svg_file_path}");
                return false;
            }
        }

        // 创建新的文件记录
        $new_file_info = array(
            'filename' => $svg_filename,
            'original_path' => $svg_file_path,
            'file_size' => filesize($svg_file_path),
            'created_time' => filemtime($svg_file_path),
            'migrated_to_oss' => false,
            'migration_time' => null
        );

        // 添加到现有记录
        $updated_record = $existing_record;
        $updated_record['files'][] = $new_file_info;
        $updated_record['total_files'] = count($updated_record['files']);
        $updated_record['last_updated'] = current_time('mysql');

        // 保存更新的记录
        $success = $this->update_category_json_record($category_id, $updated_record);

        if ($success) {
            error_log("[SVG文件管理][新增] 成功添加SVG文件 '{$svg_filename}' 到JSON记录");
        } else {
            error_log("[SVG文件管理][新增] 添加SVG文件 '{$svg_filename}' 到JSON记录失败");
        }

        return $success;
    }

    /**
     * 删除类目的 JSON 记录文件
     *
     * @param int $category_id 类目ID
     * @return array 操作结果
     */
    public function delete_category_json_record($category_id) {
        error_log("[SVG文件管理] 开始删除类目 {$category_id} 的JSON记录");

        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $json_filename = "category_{$category_id}_{$safe_category_name}.json";
        $json_filepath = $this->json_records_dir . $json_filename;

        if (!file_exists($json_filepath)) {
            error_log("[SVG文件管理] JSON记录文件不存在: {$json_filepath}");
            return array('success' => true, 'message' => 'JSON记录文件不存在，无需删除');
        }

        if (unlink($json_filepath)) {
            error_log("[SVG文件管理] 成功删除JSON记录文件: {$json_filepath}");
            return array('success' => true, 'message' => "成功删除JSON记录文件: {$json_filename}");
        } else {
            error_log("[SVG文件管理] 删除JSON记录文件失败: {$json_filepath}");
            return array('success' => false, 'message' => "删除JSON记录文件失败: {$json_filename}");
        }
    }

    /**
     * 在产品清理后更新JSON记录，移除已删除产品的SVG记录
     *
     * @param int $category_id 类目ID
     * @param array $deleted_svg_files 已删除的SVG文件列表
     * @return bool
     */
    public function update_json_record_after_product_cleanup($category_id, $deleted_svg_files = array()) {
        error_log("[SVG文件管理] 产品清理后更新JSON记录: 类目ID={$category_id}, 删除文件数=" . count($deleted_svg_files));

        $json_record = $this->get_category_json_record($category_id);
        if (!$json_record) {
            error_log("[SVG文件管理] 未找到类目 {$category_id} 的JSON记录，无需更新");
            return true; // 没有记录也算成功
        }

        if (empty($deleted_svg_files)) {
            error_log("[SVG文件管理] 没有删除的SVG文件，无需更新JSON记录");
            return true;
        }

        // 从JSON记录中移除已删除的文件
        $updated_files = array();
        $removed_count = 0;

        foreach ($json_record['files'] as $file_info) {
            $filename = $file_info['filename'];
            if (!in_array($filename, $deleted_svg_files)) {
                $updated_files[] = $file_info;
            } else {
                $removed_count++;
                error_log("[SVG文件管理] 从JSON记录中移除文件: {$filename}");
            }
        }

        // 更新JSON记录
        $json_record['files'] = $updated_files;
        $json_record['total_files'] = count($updated_files);
        $json_record['last_updated'] = current_time('mysql');

        $success = $this->update_category_json_record($category_id, $json_record);

        if ($success) {
            error_log("[SVG文件管理] 成功更新JSON记录: 类目ID={$category_id}, 移除了 {$removed_count} 个文件记录，剩余 " . count($updated_files) . " 个文件");
        } else {
            error_log("[SVG文件管理] 更新JSON记录失败: 类目ID={$category_id}");
        }

        return $success;
    }

    /**
     * 多线程转换SVG到PNG
     *
     * @param int $category_id 类目ID
     * @param int $threads 线程数
     * @param int $batch_size 批次大小
     * @return array 操作结果
     */
    public function convert_svg_to_png_multithread($category_id, $threads = 16, $batch_size = 50) {
        error_log("[SVG转PNG] 开始多线程转换类目 {$category_id}，线程数: {$threads}，批次大小: {$batch_size}");

        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $svg_dir = $this->processed_svg_dir . $safe_category_name . '/';

        if (!is_dir($svg_dir)) {
            return array('success' => false, 'message' => "SVG目录不存在: {$svg_dir}");
        }

        // 创建PNG输出目录
        $upload_dir = wp_upload_dir();
        $png_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_png/' . $safe_category_name . '/';
        if (!file_exists($png_dir)) {
            wp_mkdir_p($png_dir);
        }

        // 获取所有SVG文件
        $svg_files = glob($svg_dir . '*.svg');
        if (empty($svg_files)) {
            return array('success' => false, 'message' => '没有找到SVG文件');
        }

        error_log("[SVG转PNG] 找到 " . count($svg_files) . " 个SVG文件");

        // 分批处理
        $batches = array_chunk($svg_files, $batch_size);
        $total_converted = 0;
        $total_failed = 0;

        foreach ($batches as $batch_index => $batch) {
            error_log("[SVG转PNG] 处理批次 " . ($batch_index + 1) . "/" . count($batches) . "，包含 " . count($batch) . " 个文件");

            $result = $this->process_svg_batch_to_png($batch, $png_dir, $threads);
            $total_converted += $result['converted'];
            $total_failed += $result['failed'];
        }

        // 删除原始SVG文件
        foreach ($svg_files as $svg_file) {
            $png_file = $png_dir . basename($svg_file, '.svg') . '.png';
            if (file_exists($png_file)) {
                unlink($svg_file);
                error_log("[SVG转PNG] 删除原始SVG文件: " . basename($svg_file));
            }
        }

        $message = "SVG转PNG完成：转换 {$total_converted} 个文件，失败 {$total_failed} 个文件";
        error_log("[SVG转PNG] " . $message);

        return array(
            'success' => true,
            'message' => $message,
            'converted' => $total_converted,
            'failed' => $total_failed,
            'png_dir' => $png_dir
        );
    }

    /**
     * 处理SVG批次转换为PNG
     */
    private function process_svg_batch_to_png($svg_files, $png_dir, $threads) {
        $converted = 0;
        $failed = 0;

        // 创建文件列表
        $temp_file_list = tempnam(sys_get_temp_dir(), 'svg_files_');
        $file_list_content = '';
        foreach ($svg_files as $svg_file) {
            $file_list_content .= $svg_file . "\n";
        }
        file_put_contents($temp_file_list, $file_list_content);

        // 创建转换脚本
        $temp_script = tempnam(sys_get_temp_dir(), 'svg_to_png_');
        $script_content = "#!/bin/bash\n";
        $script_content .= "convert_single_svg() {\n";
        $script_content .= "    local svg_file=\"\$1\"\n";
        $script_content .= "    local png_dir=\"\$2\"\n";
        $script_content .= "    \n";
        $script_content .= "    if [[ ! -f \"\$svg_file\" ]]; then\n";
        $script_content .= "        echo \"FAILED: File not found - \$svg_file\"\n";
        $script_content .= "        return 1\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    local filename=\$(basename \"\$svg_file\" .svg)\n";
        $script_content .= "    local png_file=\"\$png_dir/\$filename.png\"\n";
        $script_content .= "    \n";
        $script_content .= "    # 确保输出目录存在\n";
        $script_content .= "    mkdir -p \"\$png_dir\"\n";
        $script_content .= "    \n";
        $script_content .= "    # 目标分辨率设置\n";
        $script_content .= "    local target_width=4509\n";
        $script_content .= "    local target_height=6012\n";
        $script_content .= "    \n";
        $script_content .= "    # 优先使用rsvg-convert（保持比例，适应目标尺寸）\n";
        $script_content .= "    if command -v rsvg-convert >/dev/null 2>&1; then\n";
        $script_content .= "        # 使用-a参数保持宽高比，-w和-h设置最大尺寸\n";
        $script_content .= "        if rsvg-convert -w \$target_width -h \$target_height -a -o \"\$png_file\" -b transparent \"\$svg_file\" 2>/dev/null; then\n";
        $script_content .= "            echo \"SUCCESS: \$filename (rsvg-convert)\"\n";
        $script_content .= "            return 0\n";
        $script_content .= "        fi\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    # 备用：使用inkscape（保持比例）\n";
        $script_content .= "    if command -v inkscape >/dev/null 2>&1; then\n";
        $script_content .= "        # 使用--export-area-page确保完整导出，保持比例\n";
        $script_content .= "        if inkscape --export-type=png --export-width=\$target_width --export-height=\$target_height --export-area-page --export-filename=\"\$png_file\" \"\$svg_file\" 2>/dev/null; then\n";
        $script_content .= "            echo \"SUCCESS: \$filename (inkscape)\"\n";
        $script_content .= "            return 0\n";
        $script_content .= "        fi\n";
        $script_content .= "        \n";
        $script_content .= "        # 如果上面失败，尝试只设置宽度，让高度自动计算保持比例\n";
        $script_content .= "        if inkscape --export-type=png --export-width=\$target_width --export-area-page --export-filename=\"\$png_file\" \"\$svg_file\" 2>/dev/null; then\n";
        $script_content .= "            echo \"SUCCESS: \$filename (inkscape-width-only)\"\n";
        $script_content .= "            return 0\n";
        $script_content .= "        fi\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    echo \"FAILED: \$filename\"\n";
        $script_content .= "    return 1\n";
        $script_content .= "}\n";
        $script_content .= "\n";
        $script_content .= "export -f convert_single_svg\n";
        $script_content .= "\n";
        $script_content .= "# 处理文件列表\n";
        $script_content .= "cat " . escapeshellarg($temp_file_list) . " | xargs -P " . intval($threads) . " -I {} bash -c 'convert_single_svg \"{}\" " . escapeshellarg($png_dir) . "'\n";

        file_put_contents($temp_script, $script_content);
        chmod($temp_script, 0755);

        error_log("[SVG转PNG] 执行批处理脚本: {$temp_script}");

        // 执行批处理
        $output = array();
        exec("bash " . escapeshellarg($temp_script), $output, $return_code);

        error_log("[SVG转PNG] 批处理返回代码: {$return_code}");
        error_log("[SVG转PNG] 批处理输出: " . implode("\n", $output));

        // 统计结果
        foreach ($output as $line) {
            if (strpos($line, 'SUCCESS:') === 0) {
                $converted++;
            } elseif (strpos($line, 'FAILED:') === 0) {
                $failed++;
            }
        }

        // 清理临时文件
        unlink($temp_script);
        unlink($temp_file_list);

        return array('converted' => $converted, 'failed' => $failed);
    }

    /**
     * 多线程转换PNG到FILF
     *
     * @param int $category_id 类目ID
     * @param int $threads 线程数
     * @param int $batch_size 批次大小
     * @return array 操作结果
     */
    public function convert_png_to_filf_multithread($category_id, $threads = 16, $batch_size = 50) {
        error_log("[PNG转FILF] 开始多线程转换类目 {$category_id}，线程数: {$threads}，批次大小: {$batch_size}");

        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $upload_dir = wp_upload_dir();
        $png_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_png/' . $safe_category_name . '/';

        if (!is_dir($png_dir)) {
            return array('success' => false, 'message' => "PNG目录不存在: {$png_dir}");
        }

        // 创建FILF输出目录
        $filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/' . $safe_category_name . '/';
        if (!file_exists($filf_dir)) {
            wp_mkdir_p($filf_dir);
        }

        // 获取所有PNG文件
        $png_files = glob($png_dir . '*.png');
        if (empty($png_files)) {
            return array('success' => false, 'message' => '没有找到PNG文件');
        }

        error_log("[PNG转FILF] 找到 " . count($png_files) . " 个PNG文件");

        // 分批处理
        $batches = array_chunk($png_files, $batch_size);
        $total_converted = 0;
        $total_failed = 0;
        $total_original_size = 0;
        $total_compressed_size = 0;

        foreach ($batches as $batch_index => $batch) {
            error_log("[PNG转FILF] 处理批次 " . ($batch_index + 1) . "/" . count($batches) . "，包含 " . count($batch) . " 个文件");

            $result = $this->process_png_batch_to_filf($batch, $filf_dir, $threads);
            $total_converted += $result['converted'];
            $total_failed += $result['failed'];
            $total_original_size += $result['original_size'];
            $total_compressed_size += $result['compressed_size'];
        }

        // 删除原始PNG文件
        foreach ($png_files as $png_file) {
            $filf_file = $filf_dir . basename($png_file, '.png') . '.flif';
            if (file_exists($filf_file)) {
                unlink($png_file);
                error_log("[PNG转FILF] 删除原始PNG文件: " . basename($png_file));
            }
        }

        $compression_ratio = $total_original_size > 0 ? round((1 - $total_compressed_size / $total_original_size) * 100, 2) : 0;

        $message = "PNG转FILF完成：转换 {$total_converted} 个文件，失败 {$total_failed} 个文件，压缩率: {$compression_ratio}%";
        error_log("[PNG转FILF] " . $message);

        return array(
            'success' => true,
            'message' => $message,
            'converted' => $total_converted,
            'failed' => $total_failed,
            'original_size' => $total_original_size,
            'compressed_size' => $total_compressed_size,
            'compression_ratio' => $compression_ratio,
            'filf_dir' => $filf_dir
        );
    }

    /**
     * 处理PNG批次转换为FILF
     */
    private function process_png_batch_to_filf($png_files, $filf_dir, $threads) {
        $converted = 0;
        $failed = 0;
        $original_size = 0;
        $compressed_size = 0;

        // 创建文件列表
        $temp_file_list = tempnam(sys_get_temp_dir(), 'png_files_');
        $file_list_content = '';
        foreach ($png_files as $png_file) {
            $file_list_content .= $png_file . "\n";
        }
        file_put_contents($temp_file_list, $file_list_content);

        // 创建转换脚本
        $temp_script = tempnam(sys_get_temp_dir(), 'png_to_filf_');
        $script_content = "#!/bin/bash\n";
        $script_content .= "convert_single_png() {\n";
        $script_content .= "    local png_file=\"\$1\"\n";
        $script_content .= "    local filf_dir=\"\$2\"\n";
        $script_content .= "    \n";
        $script_content .= "    if [[ ! -f \"\$png_file\" ]]; then\n";
        $script_content .= "        echo \"FAILED: File not found - \$png_file\"\n";
        $script_content .= "        return 1\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    local filename=\$(basename \"\$png_file\" .png)\n";
        $script_content .= "    local filf_file=\"\$filf_dir/\$filename.flif\"\n";
        $script_content .= "    \n";
        $script_content .= "    # 确保输出目录存在\n";
        $script_content .= "    mkdir -p \"\$filf_dir\"\n";
        $script_content .= "    \n";
        $script_content .= "    # 检查flif命令是否可用\n";
        $script_content .= "    if ! command -v flif >/dev/null 2>&1; then\n";
        $script_content .= "        echo \"FAILED: flif command not found - \$filename\"\n";
        $script_content .= "        return 1\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    # 转换PNG到FILF\n";
        $script_content .= "    # 首先尝试基本命令\n";
        $script_content .= "    if flif -e \"\$png_file\" \"\$filf_file\" 2>/dev/null; then\n";
        $script_content .= "        if [[ -f \"\$filf_file\" ]]; then\n";
        $script_content .= "            local orig_size=\$(stat -c%s \"\$png_file\" 2>/dev/null || echo 0)\n";
        $script_content .= "            local comp_size=\$(stat -c%s \"\$filf_file\" 2>/dev/null || echo 0)\n";
        $script_content .= "            echo \"SUCCESS: \$filename \$orig_size \$comp_size\"\n";
        $script_content .= "            return 0\n";
        $script_content .= "        fi\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    # 如果基本命令失败，尝试快速编码\n";
        $script_content .= "    if flif -e -E 30 \"\$png_file\" \"\$filf_file\" 2>/dev/null; then\n";
        $script_content .= "        if [[ -f \"\$filf_file\" ]]; then\n";
        $script_content .= "            local orig_size=\$(stat -c%s \"\$png_file\" 2>/dev/null || echo 0)\n";
        $script_content .= "            local comp_size=\$(stat -c%s \"\$filf_file\" 2>/dev/null || echo 0)\n";
        $script_content .= "            echo \"SUCCESS: \$filename \$orig_size \$comp_size (fast)\"\n";
        $script_content .= "            return 0\n";
        $script_content .= "        fi\n";
        $script_content .= "    fi\n";
        $script_content .= "    \n";
        $script_content .= "    echo \"FAILED: \$filename\"\n";
        $script_content .= "    return 1\n";
        $script_content .= "}\n";
        $script_content .= "\n";
        $script_content .= "export -f convert_single_png\n";
        $script_content .= "\n";
        $script_content .= "# 处理文件列表\n";
        $script_content .= "cat " . escapeshellarg($temp_file_list) . " | xargs -P " . intval($threads) . " -I {} bash -c 'convert_single_png \"{}\" " . escapeshellarg($filf_dir) . "'\n";

        file_put_contents($temp_script, $script_content);
        chmod($temp_script, 0755);

        error_log("[PNG转FILF] 执行批处理脚本: {$temp_script}");
        error_log("[PNG转FILF] 处理 " . count($png_files) . " 个PNG文件，使用 {$threads} 个线程");

        // 执行批处理
        $output = array();
        exec("bash " . escapeshellarg($temp_script), $output, $return_code);

        error_log("[PNG转FILF] 批处理返回代码: {$return_code}");
        error_log("[PNG转FILF] 批处理输出: " . implode("\n", $output));

        // 如果有错误，记录更详细的信息
        if ($return_code !== 0) {
            error_log("[PNG转FILF] 批处理失败，检查FLIF命令和文件权限");
            // 检查第一个PNG文件
            if (!empty($png_files)) {
                $first_png = $png_files[0];
                error_log("[PNG转FILF] 第一个PNG文件: {$first_png}，存在: " . (file_exists($first_png) ? '是' : '否'));
                if (file_exists($first_png)) {
                    error_log("[PNG转FILF] 文件大小: " . filesize($first_png) . " 字节");
                }
            }
        }

        // 统计结果
        foreach ($output as $line) {
            if (strpos($line, 'SUCCESS:') === 0) {
                $converted++;
                $parts = explode(' ', $line);
                if (count($parts) >= 4) {
                    $original_size += intval($parts[2]);
                    $compressed_size += intval($parts[3]);
                }
            } elseif (strpos($line, 'FAILED:') === 0) {
                $failed++;
            }
        }

        // 清理临时文件
        unlink($temp_script);
        unlink($temp_file_list);

        return array(
            'converted' => $converted,
            'failed' => $failed,
            'original_size' => $original_size,
            'compressed_size' => $compressed_size
        );
    }

    /**
     * 使用OSS Python SDK上传FILF文件到OSS
     *
     * @param int $category_id 类目ID
     * @param bool $verbose 是否显示详细日志
     * @param bool $overwrite 是否覆盖已存在的文件
     * @param bool $backup_old 是否备份旧文件
     * @return array 上传结果
     */
    public function upload_filf_to_oss_python_sdk($category_id, $verbose = false, $overwrite = true, $backup_old = false) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $upload_dir = wp_upload_dir();
        $filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/' . $safe_category_name . '/';

        if (!is_dir($filf_dir)) {
            return array('success' => false, 'message' => "FILF目录不存在: {$filf_dir}");
        }

        // 获取所有FILF文件
        $filf_files = glob($filf_dir . '*.flif');
        if (empty($filf_files)) {
            return array('success' => false, 'message' => '没有找到FILF文件');
        }

        error_log("[OSS上传] 开始上传类目 {$category_id} 的 " . count($filf_files) . " 个FILF文件");

        // 构建OSS前缀路径
        $oss_prefix = "filf/{$safe_category_name}/";

        // 获取插件目录
        $plugin_dir = plugin_dir_path(__FILE__);
        $oss_uploader_script = $plugin_dir . 'oss-uploader.py';

        // 检查上传脚本是否存在
        if (!file_exists($oss_uploader_script)) {
            return array(
                'success' => false,
                'message' => "OSS上传脚本不存在: {$oss_uploader_script}，请先运行 ./install-oss-sdk.sh"
            );
        }

        // 构建Python上传命令
        $python_cmd = $this->get_python_command();
        $result_file = tempnam(sys_get_temp_dir(), 'oss_upload_result_');

        // 构建命令参数
        $command_args = array(
            '--local-path', escapeshellarg($filf_dir),
            '--oss-prefix', escapeshellarg($oss_prefix),
            '--threads', '4',
            '--pattern', '"*.flif"',
            '--output-json', escapeshellarg($result_file)
        );

        // 添加覆盖相关参数
        if (!$overwrite) {
            $command_args[] = '--no-overwrite';
        }

        if ($backup_old) {
            $command_args[] = '--backup-old';
        }

        if ($verbose) {
            $command_args[] = '--verbose';
        }

        $upload_command = sprintf(
            '%s %s %s 2>&1',
            $python_cmd,
            escapeshellarg($oss_uploader_script),
            implode(' ', $command_args)
        );

        if ($verbose) {
            error_log("[OSS上传] 执行命令: {$upload_command}");
        }

        $start_time = time();
        $output = array();
        $return_code = 0;
        exec($upload_command, $output, $return_code);
        $upload_time = time() - $start_time;

        $output_str = implode("\n", $output);

        if ($verbose) {
            error_log("[OSS上传] 命令输出: {$output_str}");
        }

        if ($return_code === 0) {
            // 上传成功，尝试读取详细结果
            $upload_result = null;
            if (file_exists($result_file)) {
                $result_content = file_get_contents($result_file);
                $upload_result = json_decode($result_content, true);
                unlink($result_file);
            }

            $uploaded_count = $upload_result ? $upload_result['uploaded_count'] : count($filf_files);
            $total_size = $upload_result ? $upload_result['total_size'] : 0;

            // 计算总文件大小（如果结果中没有）
            if ($total_size == 0) {
                foreach ($filf_files as $file) {
                    $total_size += filesize($file);
                }
            }

            $message = "OSS上传完成：{$uploaded_count} 个文件，总大小 " . $this->format_bytes($total_size) . "，耗时 {$upload_time} 秒";
            error_log("[OSS上传] " . $message);

            // 上传成功后清理本地FILF文件
            $cleanup_result = $this->cleanup_local_filf_files($category_id, $verbose);

            return array(
                'success' => true,
                'message' => $message,
                'uploaded_count' => $uploaded_count,
                'total_size' => $total_size,
                'upload_time' => $upload_time,
                'oss_prefix' => $oss_prefix,
                'output' => $output_str,
                'upload_result' => $upload_result,
                'cleanup_result' => $cleanup_result
            );
        } else {
            $error_message = "OSS上传失败，返回代码: {$return_code}，输出: {$output_str}";
            error_log("[OSS上传] " . $error_message);

            // 清理结果文件
            if (file_exists($result_file)) {
                unlink($result_file);
            }

            return array(
                'success' => false,
                'message' => $error_message,
                'uploaded_count' => 0,
                'output' => $output_str
            );
        }
    }

    /**
     * 获取Python命令
     */
    private function get_python_command() {
        // 尝试不同的Python命令
        $python_commands = array('python3', 'python');

        foreach ($python_commands as $cmd) {
            $test_output = shell_exec("which {$cmd} 2>/dev/null");
            if (!empty($test_output)) {
                return $cmd;
            }
        }

        return 'python3'; // 默认使用python3
    }

    /**
     * 格式化字节大小
     */
    private function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 检查OSS中已存在的FILF文件
     *
     * @param int $category_id 类目ID
     * @param bool $verbose 是否显示详细日志
     * @return array 检查结果
     */
    public function check_oss_filf_files($category_id, $verbose = false) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $upload_dir = wp_upload_dir();
        $filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/' . $safe_category_name . '/';

        if (!is_dir($filf_dir)) {
            return array('success' => false, 'message' => "FILF目录不存在: {$filf_dir}");
        }

        // 获取所有FILF文件
        $filf_files = glob($filf_dir . '*.flif');
        if (empty($filf_files)) {
            return array('success' => false, 'message' => '没有找到FILF文件');
        }

        error_log("[OSS检查] 检查类目 {$category_id} 的 " . count($filf_files) . " 个FILF文件在OSS中的状态");

        // 构建OSS前缀路径
        $oss_prefix = "filf/{$safe_category_name}/";

        // 获取插件目录
        $plugin_dir = plugin_dir_path(__FILE__);
        $oss_uploader_script = $plugin_dir . 'oss-uploader.py';

        // 检查上传脚本是否存在
        if (!file_exists($oss_uploader_script)) {
            return array(
                'success' => false,
                'message' => "OSS上传脚本不存在: {$oss_uploader_script}"
            );
        }

        // 构建Python检查命令
        $python_cmd = $this->get_python_command();
        $result_file = tempnam(sys_get_temp_dir(), 'oss_check_result_');
        $check_command = sprintf(
            '%s %s --local-path %s --oss-prefix %s --check-existing --pattern "*.flif" --output-json %s 2>&1',
            $python_cmd,
            escapeshellarg($oss_uploader_script),
            escapeshellarg($filf_dir),
            escapeshellarg($oss_prefix),
            escapeshellarg($result_file)
        );

        if ($verbose) {
            error_log("[OSS检查] 执行命令: {$check_command}");
        }

        $start_time = time();
        $output = array();
        $return_code = 0;
        exec($check_command, $output, $return_code);
        $check_time = time() - $start_time;

        $output_str = implode("\n", $output);

        if ($verbose) {
            error_log("[OSS检查] 命令输出: {$output_str}");
        }

        if ($return_code === 0) {
            // 检查成功，读取详细结果
            $check_result = null;
            if (file_exists($result_file)) {
                $result_content = file_get_contents($result_file);
                $check_result = json_decode($result_content, true);
                unlink($result_file);
            }

            $total_files = $check_result ? $check_result['total_files'] : count($filf_files);
            $existing_files = $check_result ? $check_result['existing_files'] : 0;
            $new_files = $check_result ? $check_result['new_files'] : $total_files;

            $message = "OSS文件检查完成：总计 {$total_files} 个文件，已存在 {$existing_files} 个，新文件 {$new_files} 个，耗时 {$check_time} 秒";
            error_log("[OSS检查] " . $message);

            return array(
                'success' => true,
                'message' => $message,
                'total_files' => $total_files,
                'existing_files' => $existing_files,
                'new_files' => $new_files,
                'check_time' => $check_time,
                'oss_prefix' => $oss_prefix,
                'output' => $output_str,
                'check_result' => $check_result
            );
        } else {
            $error_message = "OSS文件检查失败，返回代码: {$return_code}，输出: {$output_str}";
            error_log("[OSS检查] " . $error_message);

            // 清理结果文件
            if (file_exists($result_file)) {
                unlink($result_file);
            }

            return array(
                'success' => false,
                'message' => $error_message,
                'output' => $output_str
            );
        }
    }

    /**
     * 清理本地FILF文件（OSS上传成功后）
     *
     * @param int $category_id 类目ID
     * @param bool $verbose 是否显示详细日志
     * @return array 清理结果
     */
    public function cleanup_local_filf_files($category_id, $verbose = false) {
        $term = get_term($category_id, 'product_cat');
        if (!$term || is_wp_error($term)) {
            return array('success' => false, 'message' => '类目不存在');
        }

        $safe_category_name = sanitize_title($term->name);
        $upload_dir = wp_upload_dir();
        $filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/' . $safe_category_name . '/';

        if (!is_dir($filf_dir)) {
            return array(
                'success' => true,
                'message' => 'FILF目录不存在，无需清理',
                'deleted_count' => 0,
                'freed_space' => 0
            );
        }

        // 获取所有FILF文件
        $filf_files = glob($filf_dir . '*.flif');
        if (empty($filf_files)) {
            return array(
                'success' => true,
                'message' => '没有找到FILF文件，无需清理',
                'deleted_count' => 0,
                'freed_space' => 0
            );
        }

        error_log("[FILF清理] 开始清理类目 {$category_id} 的 " . count($filf_files) . " 个本地FILF文件");

        $deleted_count = 0;
        $freed_space = 0;
        $failed_files = array();

        foreach ($filf_files as $filf_file) {
            $file_size = filesize($filf_file);
            $filename = basename($filf_file);

            if (unlink($filf_file)) {
                $deleted_count++;
                $freed_space += $file_size;

                if ($verbose) {
                    error_log("[FILF清理] 删除文件: {$filename} ({$this->format_bytes($file_size)})");
                }
            } else {
                $failed_files[] = $filename;
                error_log("[FILF清理] 删除失败: {$filename}");
            }
        }

        // 尝试删除空目录
        $remaining_files = glob($filf_dir . '*');
        if (empty($remaining_files)) {
            if (rmdir($filf_dir)) {
                if ($verbose) {
                    error_log("[FILF清理] 删除空目录: {$filf_dir}");
                }
            }
        }

        $message = "本地FILF文件清理完成：删除 {$deleted_count} 个文件，释放空间 " . $this->format_bytes($freed_space);
        if (!empty($failed_files)) {
            $message .= "，失败 " . count($failed_files) . " 个文件";
        }

        error_log("[FILF清理] " . $message);

        return array(
            'success' => empty($failed_files),
            'message' => $message,
            'deleted_count' => $deleted_count,
            'failed_count' => count($failed_files),
            'freed_space' => $freed_space,
            'failed_files' => $failed_files
        );
    }

    /**
     * 批量清理所有本地FILF文件
     *
     * @param array $category_ids 类目ID数组，为空则清理所有
     * @param bool $verbose 是否显示详细日志
     * @return array 批量清理结果
     */
    public function batch_cleanup_local_filf_files($category_ids = array(), $verbose = false) {
        $upload_dir = wp_upload_dir();
        $base_filf_dir = $upload_dir['basedir'] . '/shoe-svg-generator/processed_filf/';

        if (!is_dir($base_filf_dir)) {
            return array(
                'success' => true,
                'message' => 'FILF基础目录不存在，无需清理',
                'total_deleted' => 0,
                'total_freed_space' => 0,
                'processed_categories' => 0
            );
        }

        // 如果没有指定类目，则获取所有FILF目录
        if (empty($category_ids)) {
            $filf_dirs = glob($base_filf_dir . '*', GLOB_ONLYDIR);
            $categories_to_process = array();

            foreach ($filf_dirs as $dir) {
                $dir_name = basename($dir);
                // 确保目录路径以斜杠结尾
                $dir_path = rtrim($dir, '/') . '/';

                // 尝试从目录名获取类目信息
                $categories_to_process[] = array(
                    'dir_name' => $dir_name,
                    'dir_path' => $dir_path
                );
            }
        } else {
            // 根据类目ID构建目录信息
            $categories_to_process = array();
            foreach ($category_ids as $category_id) {
                $term = get_term($category_id, 'product_cat');
                if ($term && !is_wp_error($term)) {
                    $safe_category_name = sanitize_title($term->name);
                    $categories_to_process[] = array(
                        'category_id' => $category_id,
                        'dir_name' => $safe_category_name,
                        'dir_path' => $base_filf_dir . $safe_category_name . '/'
                    );
                }
            }
        }

        if (empty($categories_to_process)) {
            return array(
                'success' => true,
                'message' => '没有找到要清理的FILF目录',
                'total_deleted' => 0,
                'total_freed_space' => 0,
                'processed_categories' => 0
            );
        }

        error_log("[批量FILF清理] 开始清理 " . count($categories_to_process) . " 个类目的FILF文件");

        if ($verbose) {
            error_log("[批量FILF清理] 详细模式已启用");
        }

        $total_deleted = 0;
        $total_freed_space = 0;
        $processed_categories = 0;
        $results = array();

        foreach ($categories_to_process as $category_info) {
            $dir_path = $category_info['dir_path'];
            $dir_name = $category_info['dir_name'];

            if ($verbose) {
                error_log("[批量FILF清理] 检查目录: {$dir_path}");
            }

            if (!is_dir($dir_path)) {
                if ($verbose) {
                    error_log("[批量FILF清理] 目录不存在，跳过: {$dir_path}");
                }
                continue;
            }

            // 获取目录中的FILF文件
            $filf_files = glob($dir_path . '*.flif');
            if ($verbose) {
                error_log("[批量FILF清理] 目录 {$dir_name} 中找到 " . count($filf_files) . " 个FILF文件");
            }

            if (empty($filf_files)) {
                if ($verbose) {
                    error_log("[批量FILF清理] 目录 {$dir_name} 中没有FILF文件，跳过");
                }
                continue;
            }

            $deleted_count = 0;
            $freed_space = 0;
            $failed_files = array();

            foreach ($filf_files as $filf_file) {
                $file_size = filesize($filf_file);
                $filename = basename($filf_file);

                if (unlink($filf_file)) {
                    $deleted_count++;
                    $freed_space += $file_size;

                    if ($verbose) {
                        error_log("[批量FILF清理] 删除文件: {$dir_name}/{$filename} ({$this->format_bytes($file_size)})");
                    }
                } else {
                    $failed_files[] = $filename;
                    error_log("[批量FILF清理] 删除失败: {$dir_name}/{$filename}");
                }
            }

            // 尝试删除空目录
            $remaining_files = glob($dir_path . '*');
            if (empty($remaining_files)) {
                if (rmdir($dir_path)) {
                    if ($verbose) {
                        error_log("[批量FILF清理] 删除空目录: {$dir_path}");
                    }
                }
            }

            $total_deleted += $deleted_count;
            $total_freed_space += $freed_space;
            $processed_categories++;

            $results[] = array(
                'dir_name' => $dir_name,
                'deleted_count' => $deleted_count,
                'freed_space' => $freed_space,
                'failed_files' => $failed_files
            );
        }

        $message = "批量FILF文件清理完成：处理 {$processed_categories} 个类目，删除 {$total_deleted} 个文件，释放空间 " . $this->format_bytes($total_freed_space);
        error_log("[批量FILF清理] " . $message);

        return array(
            'success' => true,
            'message' => $message,
            'total_deleted' => $total_deleted,
            'total_freed_space' => $total_freed_space,
            'processed_categories' => $processed_categories,
            'results' => $results
        );
    }
}
